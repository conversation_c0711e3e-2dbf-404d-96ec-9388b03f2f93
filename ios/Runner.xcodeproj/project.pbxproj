// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		0004078E25E5A8E19E41073E /* Pods_YL.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9DEF331880F56F20B826C2A6 /* Pods_YL.framework */; };
		002142AD2DAFE74800805928 /* Release-JS2.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 002142AC2DAFE74800805928 /* Release-JS2.xcconfig */; };
		002142CA2DAFE89E00805928 /* Assets-JS.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 002142C62DAFE89E00805928 /* Assets-JS.xcassets */; };
		002142CB2DAFE89E00805928 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 002142C72DAFE89E00805928 /* GoogleService-Info.plist */; };
		002142CC2DAFE89E00805928 /* Assets-JS.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 002142C62DAFE89E00805928 /* Assets-JS.xcassets */; };
		002142CD2DAFE89E00805928 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 002142C72DAFE89E00805928 /* GoogleService-Info.plist */; };
		002142D52DAFE8AE00805928 /* Release-JS.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 002142D22DAFE8AE00805928 /* Release-JS.xcconfig */; };
		002142D62DAFE8AE00805928 /* Release-JS.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 002142D22DAFE8AE00805928 /* Release-JS.xcconfig */; };
		002142DD2DAFE9CE00805928 /* Debug-YL.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 002142D82DAFE9CE00805928 /* Debug-YL.xcconfig */; };
		002142DE2DAFE9CE00805928 /* Release-YL.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 002142D92DAFE9CE00805928 /* Release-YL.xcconfig */; };
		002142DF2DAFE9CE00805928 /* Assets-YL.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 002142D72DAFE9CE00805928 /* Assets-YL.xcassets */; };
		002142E62DAFE9CE00805928 /* Debug-YL.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 002142D82DAFE9CE00805928 /* Debug-YL.xcconfig */; };
		002142E72DAFE9CE00805928 /* Release-YL.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 002142D92DAFE9CE00805928 /* Release-YL.xcconfig */; };
		002142E82DAFE9CE00805928 /* Assets-YL.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 002142D72DAFE9CE00805928 /* Assets-YL.xcassets */; };
		00499B182DB642F10022EDD6 /* exportOptions.plist in Resources */ = {isa = PBXBuildFile; fileRef = 00499B172DB642F10022EDD6 /* exportOptions.plist */; };
		00499B1A2DB642FC0022EDD6 /* exportOptions.plist in Resources */ = {isa = PBXBuildFile; fileRef = 00499B192DB642FC0022EDD6 /* exportOptions.plist */; };
		00499B532DB667C20022EDD6 /* Generated.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 00499B522DB667C20022EDD6 /* Generated.xcconfig */; };
		00499B542DB667C20022EDD6 /* Generated.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 00499B522DB667C20022EDD6 /* Generated.xcconfig */; };
		00499B552DB667C20022EDD6 /* Generated.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 00499B522DB667C20022EDD6 /* Generated.xcconfig */; };
		00499B562DB667C20022EDD6 /* Generated.xcconfig in Resources */ = {isa = PBXBuildFile; fileRef = 00499B522DB667C20022EDD6 /* Generated.xcconfig */; };
		00B17E362DACE52300DD198E /* JS.plist in Resources */ = {isa = PBXBuildFile; fileRef = 00B17E352DACE52300DD198E /* JS.plist */; };
		00B17E382DACE52E00DD198E /* JS2.plist in Resources */ = {isa = PBXBuildFile; fileRef = 00B17E372DACE52E00DD198E /* JS2.plist */; };
		00B17E3A2DACE53B00DD198E /* YL.plist in Resources */ = {isa = PBXBuildFile; fileRef = 00B17E392DACE53B00DD198E /* YL.plist */; };
		00C56BED2DB637A70092C07B /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 00C56BEC2DB637A70092C07B /* GoogleService-Info.plist */; };
		00C56BEE2DB637A70092C07B /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 00C56BEC2DB637A70092C07B /* GoogleService-Info.plist */; };
		1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */ = {isa = PBXBuildFile; fileRef = 1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */; };
		3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */; };
		3F9A0F7F37498F47B3240433 /* Pods_JS.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5DFBF8BF68E4F513C0F5B25E /* Pods_JS.framework */; };
		404DB663D0BE1E37E2410D90 /* Pods_Runner.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1F31EE3D7A8E5F8B67A20508 /* Pods_Runner.framework */; };
		4E80C1CC2D3C48AB007F6C7F /* exportOptions.plist in Resources */ = {isa = PBXBuildFile; fileRef = 4E80C1CB2D3C48AB007F6C7F /* exportOptions.plist */; };
		4FA6B080841CE3E1D670B15F /* Pods_JS.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B7231853032A904AB04C3E95 /* Pods_JS.framework */; };
		74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74858FAE1ED2DC5600515810 /* AppDelegate.swift */; };
		97C146FC1CF9000F007C117D /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FA1CF9000F007C117D /* Main.storyboard */; };
		97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FD1CF9000F007C117D /* Assets.xcassets */; };
		97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */; };
		9966BC07C298CB8B673B67DA /* Pods_YL.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C43A6BB18532020417615117 /* Pods_YL.framework */; };
		A48A6A42783C0BA1CE67A795 /* Pods_Runner.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FE620321AA57D35106C93AE6 /* Pods_Runner.framework */; };
		AB6EC12238E2EFD8DBF6BF33 /* (null) in Frameworks */ = {isa = PBXBuildFile; };
		D0D82CB69247895912F158FE /* Pods_JS2.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 2CAF7F0C8872748D01698D02 /* Pods_JS2.framework */; };
		F11983B2CD9BD5AE4F685301 /* Pods_Runner.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 411A78EB9EFA73C7772F8EA4 /* Pods_Runner.framework */; };
		FB7596E42D62EB6A000241A3 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74858FAE1ED2DC5600515810 /* AppDelegate.swift */; };
		FB7596E52D62EB6A000241A3 /* GeneratedPluginRegistrant.m in Sources */ = {isa = PBXBuildFile; fileRef = 1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */; };
		FB7596E92D62EB6A000241A3 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */; };
		FB7596EA2D62EB6A000241A3 /* exportOptions.plist in Resources */ = {isa = PBXBuildFile; fileRef = 4E80C1CB2D3C48AB007F6C7F /* exportOptions.plist */; };
		FB7596EB2D62EB6A000241A3 /* AppFrameworkInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */; };
		FB7596ED2D62EB6A000241A3 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FA1CF9000F007C117D /* Main.storyboard */; };
		FB7596FD2D62EB70000241A3 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74858FAE1ED2DC5600515810 /* AppDelegate.swift */; };
		FB7596FE2D62EB70000241A3 /* GeneratedPluginRegistrant.m in Sources */ = {isa = PBXBuildFile; fileRef = 1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */; };
		FB7597022D62EB70000241A3 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */; };
		FB7597042D62EB70000241A3 /* AppFrameworkInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */; };
		FB7597062D62EB70000241A3 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FA1CF9000F007C117D /* Main.storyboard */; };
		FBD1D3D32D9433A800FA2C43 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74858FAE1ED2DC5600515810 /* AppDelegate.swift */; };
		FBD1D3D42D9433A800FA2C43 /* GeneratedPluginRegistrant.m in Sources */ = {isa = PBXBuildFile; fileRef = 1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */; };
		FBD1D3D92D9433A800FA2C43 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */; };
		FBD1D3DB2D9433A800FA2C43 /* AppFrameworkInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */; };
		FBD1D3DC2D9433A800FA2C43 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FA1CF9000F007C117D /* Main.storyboard */; };
		FBD1D3F12D94343800FA2C43 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = FBD1D3EF2D94343800FA2C43 /* GoogleService-Info.plist */; };
		FBD1D3F32D94343800FA2C43 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = FBD1D3EF2D94343800FA2C43 /* GoogleService-Info.plist */; };
		FBD1D3F42D94343800FA2C43 /* Assets-JS2.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = FBD1D3EE2D94343800FA2C43 /* Assets-JS2.xcassets */; };
		FBD1D3F52D94347300FA2C43 /* Assets-JS2.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = FBD1D3EE2D94343800FA2C43 /* Assets-JS2.xcassets */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		9705A1C41CF9048500538489 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		FB7596EE2D62EB6A000241A3 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		FB7597072D62EB70000241A3 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		FBD1D3DD2D9433A800FA2C43 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		00162B3B2D9ECFAE005CA975 /* JS2Release-JS2.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "JS2Release-JS2.entitlements"; sourceTree = "<group>"; };
		002142AC2DAFE74800805928 /* Release-JS2.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = "Release-JS2.xcconfig"; sourceTree = "<group>"; };
		002142C62DAFE89E00805928 /* Assets-JS.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Assets-JS.xcassets"; sourceTree = "<group>"; };
		002142C72DAFE89E00805928 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		002142C82DAFE89E00805928 /* JSRelease-JS.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "JSRelease-JS.entitlements"; sourceTree = "<group>"; };
		002142D22DAFE8AE00805928 /* Release-JS.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = "Release-JS.xcconfig"; sourceTree = "<group>"; };
		002142D72DAFE9CE00805928 /* Assets-YL.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Assets-YL.xcassets"; sourceTree = "<group>"; };
		002142D82DAFE9CE00805928 /* Debug-YL.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = "Debug-YL.xcconfig"; sourceTree = "<group>"; };
		002142D92DAFE9CE00805928 /* Release-YL.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; path = "Release-YL.xcconfig"; sourceTree = "<group>"; };
		002142DA2DAFE9CE00805928 /* YLDebug-YL.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "YLDebug-YL.entitlements"; sourceTree = "<group>"; };
		002142DB2DAFE9CE00805928 /* YLRelease-YL.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "YLRelease-YL.entitlements"; sourceTree = "<group>"; };
		00499B172DB642F10022EDD6 /* exportOptions.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = exportOptions.plist; sourceTree = "<group>"; };
		00499B192DB642FC0022EDD6 /* exportOptions.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = exportOptions.plist; sourceTree = "<group>"; };
		00499B522DB667C20022EDD6 /* Generated.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = Generated.xcconfig; path = Flutter/Generated.xcconfig; sourceTree = "<group>"; };
		007C60C9E0D5538B378AE892 /* Pods-JS2.release-js.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS2.release-js.xcconfig"; path = "Target Support Files/Pods-JS2/Pods-JS2.release-js.xcconfig"; sourceTree = "<group>"; };
		00B17E352DACE52300DD198E /* JS.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = JS.plist; sourceTree = "<group>"; };
		00B17E372DACE52E00DD198E /* JS2.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = JS2.plist; sourceTree = "<group>"; };
		00B17E392DACE53B00DD198E /* YL.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = YL.plist; sourceTree = "<group>"; };
		00C56BEC2DB637A70092C07B /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		0A1EEEE32D540E927F4E4F9C /* Pods-Runner.debug-js2.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug-js2.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug-js2.xcconfig"; sourceTree = "<group>"; };
		0DF1A7B9C9B044DE359EC101 /* Pods-JS2.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS2.debug.xcconfig"; path = "Target Support Files/Pods-JS2/Pods-JS2.debug.xcconfig"; sourceTree = "<group>"; };
		0E230580014CCC0000B10700 /* Pods-JS2.profile-js2.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS2.profile-js2.xcconfig"; path = "Target Support Files/Pods-JS2/Pods-JS2.profile-js2.xcconfig"; sourceTree = "<group>"; };
		0E8AD10CA67349F512AB1C8F /* Pods-JS2.profile-js.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS2.profile-js.xcconfig"; path = "Target Support Files/Pods-JS2/Pods-JS2.profile-js.xcconfig"; sourceTree = "<group>"; };
		147BD11AE2E4BBAE129F5AC7 /* Pods-JS.debug-js2.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS.debug-js2.xcconfig"; path = "Target Support Files/Pods-JS/Pods-JS.debug-js2.xcconfig"; sourceTree = "<group>"; };
		1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GeneratedPluginRegistrant.h; sourceTree = "<group>"; };
		1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GeneratedPluginRegistrant.m; sourceTree = "<group>"; };
		1606D8F6F2B172199465D55C /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		1772D9067679CB010F984FE8 /* Pods-JS.profile-yl.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS.profile-yl.xcconfig"; path = "Target Support Files/Pods-JS/Pods-JS.profile-yl.xcconfig"; sourceTree = "<group>"; };
		1DBBD6E435F4DDE420B4ABFA /* Pods-Runner.profile-yl.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile-yl.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile-yl.xcconfig"; sourceTree = "<group>"; };
		1F31EE3D7A8E5F8B67A20508 /* Pods_Runner.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1FFA0B633F8A01D10DA97B50 /* Pods-Runner.opo.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.opo.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.opo.xcconfig"; sourceTree = "<group>"; };
		2444F5F6A882F287BB8A6762 /* Pods-YL.profile-yl.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-YL.profile-yl.xcconfig"; path = "Target Support Files/Pods-YL/Pods-YL.profile-yl.xcconfig"; sourceTree = "<group>"; };
		2639A042649FE04FC8DF049F /* Pods-JS2.profile-yl.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS2.profile-yl.xcconfig"; path = "Target Support Files/Pods-JS2/Pods-JS2.profile-yl.xcconfig"; sourceTree = "<group>"; };
		283DAEB72E4AA09137E968A5 /* Pods-Runner.release-js.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release-js.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release-js.xcconfig"; sourceTree = "<group>"; };
		28602180CE472F081A4032F7 /* Pods-YL.opo.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-YL.opo.xcconfig"; path = "Target Support Files/Pods-YL/Pods-YL.opo.xcconfig"; sourceTree = "<group>"; };
		2CAF7F0C8872748D01698D02 /* Pods_JS2.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_JS2.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		319CC0A0AD937106E9BDDB93 /* Pods-YL.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-YL.release.xcconfig"; path = "Target Support Files/Pods-YL/Pods-YL.release.xcconfig"; sourceTree = "<group>"; };
		331C807B294A618700263BE5 /* RunnerTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RunnerTests.swift; sourceTree = "<group>"; };
		33BA59504EE6A6EFFEBFBF7D /* Pods-JS2.opo.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS2.opo.xcconfig"; path = "Target Support Files/Pods-JS2/Pods-JS2.opo.xcconfig"; sourceTree = "<group>"; };
		3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = AppFrameworkInfo.plist; path = Flutter/AppFrameworkInfo.plist; sourceTree = "<group>"; };
		3C8F58ABCDC595F39B795653 /* Pods-JS2.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS2.release.xcconfig"; path = "Target Support Files/Pods-JS2/Pods-JS2.release.xcconfig"; sourceTree = "<group>"; };
		3D442BF9C0CD6ADB12CEEAAC /* Pods-JS.release-js.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS.release-js.xcconfig"; path = "Target Support Files/Pods-JS/Pods-JS.release-js.xcconfig"; sourceTree = "<group>"; };
		411A78EB9EFA73C7772F8EA4 /* Pods_Runner.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		436FF1F3AE1BD42F95C215DB /* Pods-YL.debug-yl.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-YL.debug-yl.xcconfig"; path = "Target Support Files/Pods-YL/Pods-YL.debug-yl.xcconfig"; sourceTree = "<group>"; };
		45CD3534C530EB39E3678FAF /* Pods-JS.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS.profile.xcconfig"; path = "Target Support Files/Pods-JS/Pods-JS.profile.xcconfig"; sourceTree = "<group>"; };
		47BA54402878C6B845EBFDC2 /* Pods-YL.debug-js.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-YL.debug-js.xcconfig"; path = "Target Support Files/Pods-YL/Pods-YL.debug-js.xcconfig"; sourceTree = "<group>"; };
		4A9D1DC7D3659118AEACED02 /* Pods-Runner.debug-js.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug-js.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug-js.xcconfig"; sourceTree = "<group>"; };
		4AB84B5A2CDA4D1A00295575 /* Runner.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Runner.entitlements; sourceTree = "<group>"; };
		4E80C1CB2D3C48AB007F6C7F /* exportOptions.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = exportOptions.plist; sourceTree = "<group>"; };
		4FB987968A1D2926F394E090 /* Pods-YL.release-js.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-YL.release-js.xcconfig"; path = "Target Support Files/Pods-YL/Pods-YL.release-js.xcconfig"; sourceTree = "<group>"; };
		5313F0F1E3862C0025F94FA3 /* Pods-RunnerTests.debug-js.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.debug-js.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.debug-js.xcconfig"; sourceTree = "<group>"; };
		5720D039F5F596A638743CD0 /* Pods-JS.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS.release.xcconfig"; path = "Target Support Files/Pods-JS/Pods-JS.release.xcconfig"; sourceTree = "<group>"; };
		5DFBF8BF68E4F513C0F5B25E /* Pods_JS.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_JS.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		5F4A68A9994A1835C20B7AAA /* Pods-RunnerTests.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.profile.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.profile.xcconfig"; sourceTree = "<group>"; };
		5F5C19DFEE26585AE98CDADE /* Pods-YL.debug-js2.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-YL.debug-js2.xcconfig"; path = "Target Support Files/Pods-YL/Pods-YL.debug-js2.xcconfig"; sourceTree = "<group>"; };
		64D0EF6244CF53C04EEC2928 /* Pods-Runner.profile-js.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile-js.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile-js.xcconfig"; sourceTree = "<group>"; };
		687304D7847D835FD1DE375F /* Pods-JS.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS.debug.xcconfig"; path = "Target Support Files/Pods-JS/Pods-JS.debug.xcconfig"; sourceTree = "<group>"; };
		70D96247805745898AD8E41A /* Pods-YL.profile-js2.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-YL.profile-js2.xcconfig"; path = "Target Support Files/Pods-YL/Pods-YL.profile-js2.xcconfig"; sourceTree = "<group>"; };
		744A84B464904A6E2D09B5B7 /* Pods-Runner.release-yl.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release-yl.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release-yl.xcconfig"; sourceTree = "<group>"; };
		7454BF5A9ED374E39EF40F16 /* Pods-JS2.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS2.profile.xcconfig"; path = "Target Support Files/Pods-JS2/Pods-JS2.profile.xcconfig"; sourceTree = "<group>"; };
		74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Runner-Bridging-Header.h"; sourceTree = "<group>"; };
		74858FAE1ED2DC5600515810 /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		7AFA3C8E1D35360C0083082E /* Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = Release.xcconfig; path = Flutter/Release.xcconfig; sourceTree = "<group>"; };
		7C0736F17E402969358E0DB0 /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		8397838C1BB71D976B40F3D1 /* Pods-JS.profile-js.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS.profile-js.xcconfig"; path = "Target Support Files/Pods-JS/Pods-JS.profile-js.xcconfig"; sourceTree = "<group>"; };
		893A9F66069EFE64991B3E75 /* Pods-JS.release-js2.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS.release-js2.xcconfig"; path = "Target Support Files/Pods-JS/Pods-JS.release-js2.xcconfig"; sourceTree = "<group>"; };
		89EA52F695B34BF7FBCDF8F0 /* Pods-JS.release-yl.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS.release-yl.xcconfig"; path = "Target Support Files/Pods-JS/Pods-JS.release-yl.xcconfig"; sourceTree = "<group>"; };
		90BA11E8F4E2C00254B5728D /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		9376C97AF0CC0FF50E772AA4 /* Pods-JS2.release-yl.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS2.release-yl.xcconfig"; path = "Target Support Files/Pods-JS2/Pods-JS2.release-yl.xcconfig"; sourceTree = "<group>"; };
		9615F567C893697150278ECD /* Pods-YL.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-YL.profile.xcconfig"; path = "Target Support Files/Pods-YL/Pods-YL.profile.xcconfig"; sourceTree = "<group>"; };
		9740EEB21CF90195004384FC /* Debug.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Debug.xcconfig; path = Flutter/Debug.xcconfig; sourceTree = "<group>"; };
		97C146EE1CF9000F007C117D /* Runner.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Runner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		97C146FB1CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		97C146FD1CF9000F007C117D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		97C147001CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		97C147021CF9000F007C117D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		9A79C5279BA8847E365665A4 /* Pods-YL.release-yl.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-YL.release-yl.xcconfig"; path = "Target Support Files/Pods-YL/Pods-YL.release-yl.xcconfig"; sourceTree = "<group>"; };
		9DEF331880F56F20B826C2A6 /* Pods_YL.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_YL.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		9F3C387C1ECD62EC4229FF0C /* Pods-Runner.profile-js2.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile-js2.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile-js2.xcconfig"; sourceTree = "<group>"; };
		A0CEF702D08289036D5ECF51 /* Pods-Runner.debug-yl.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug-yl.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug-yl.xcconfig"; sourceTree = "<group>"; };
		A1B59F968BACD0D20AE9B461 /* Pods-JS2.debug-js2.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS2.debug-js2.xcconfig"; path = "Target Support Files/Pods-JS2/Pods-JS2.debug-js2.xcconfig"; sourceTree = "<group>"; };
		A6A9D1FA94F55ABB76358396 /* Pods-JS.debug-yl.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS.debug-yl.xcconfig"; path = "Target Support Files/Pods-JS/Pods-JS.debug-yl.xcconfig"; sourceTree = "<group>"; };
		AEC6272EFD3F1BE09AE9C694 /* Pods-JS2.debug-js.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS2.debug-js.xcconfig"; path = "Target Support Files/Pods-JS2/Pods-JS2.debug-js.xcconfig"; sourceTree = "<group>"; };
		B13F2ACA7E433E2B4CF57DB9 /* Pods-YL.profile-js.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-YL.profile-js.xcconfig"; path = "Target Support Files/Pods-YL/Pods-YL.profile-js.xcconfig"; sourceTree = "<group>"; };
		B7231853032A904AB04C3E95 /* Pods_JS.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_JS.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		C04E101D2BF69D10E98F297D /* Pods-YL.release-js2.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-YL.release-js2.xcconfig"; path = "Target Support Files/Pods-YL/Pods-YL.release-js2.xcconfig"; sourceTree = "<group>"; };
		C3AC4796FEC5209BA12981CE /* Pods-JS2.release-js2.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS2.release-js2.xcconfig"; path = "Target Support Files/Pods-JS2/Pods-JS2.release-js2.xcconfig"; sourceTree = "<group>"; };
		C43A6BB18532020417615117 /* Pods_YL.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_YL.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		CB3C3DD71E89005C9D6815A9 /* Pods-JS.opo.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS.opo.xcconfig"; path = "Target Support Files/Pods-JS/Pods-JS.opo.xcconfig"; sourceTree = "<group>"; };
		E633F86CB6836F32665A4E97 /* Pods-YL.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-YL.debug.xcconfig"; path = "Target Support Files/Pods-YL/Pods-YL.debug.xcconfig"; sourceTree = "<group>"; };
		E6A422D9A774EBB0C6313A5A /* Pods-JS.profile-js2.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS.profile-js2.xcconfig"; path = "Target Support Files/Pods-JS/Pods-JS.profile-js2.xcconfig"; sourceTree = "<group>"; };
		E6D31185C3D053DEE38B9C6D /* Pods-JS2.debug-yl.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS2.debug-yl.xcconfig"; path = "Target Support Files/Pods-JS2/Pods-JS2.debug-yl.xcconfig"; sourceTree = "<group>"; };
		EA9BC7549B1DAED402015C23 /* Pods-JS.debug-js.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JS.debug-js.xcconfig"; path = "Target Support Files/Pods-JS/Pods-JS.debug-js.xcconfig"; sourceTree = "<group>"; };
		F87C5D483350203C37CBA21A /* Pods-Runner.release-js2.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release-js2.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release-js2.xcconfig"; sourceTree = "<group>"; };
		FB7596F62D62EB6A000241A3 /* YL.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = YL.app; sourceTree = BUILT_PRODUCTS_DIR; };
		FB75970F2D62EB70000241A3 /* JS.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = JS.app; sourceTree = BUILT_PRODUCTS_DIR; };
		FBD1D3EB2D9433A800FA2C43 /* JS2.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = JS2.app; sourceTree = BUILT_PRODUCTS_DIR; };
		FBD1D3EE2D94343800FA2C43 /* Assets-JS2.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Assets-JS2.xcassets"; sourceTree = "<group>"; };
		FBD1D3EF2D94343800FA2C43 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		FDD29CD76D257CD439DD146A /* Pods-RunnerTests.debug-yl.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.debug-yl.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.debug-yl.xcconfig"; sourceTree = "<group>"; };
		FE620321AA57D35106C93AE6 /* Pods_Runner.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		FB7596F82D62EB6A000241A3 /* Exceptions for "AppSupportFiles" folder in "YL" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				"AliVideoCert-com_appyl_yl-20250103200919.crt",
			);
			target = FB7596E02D62EB6A000241A3 /* YL */;
		};
		FB7597112D62EB70000241A3 /* Exceptions for "AppSupportFiles" folder in "JS" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				"AliVideoCert-com_appyl_yl-20250103200919.crt",
			);
			target = FB7596F92D62EB70000241A3 /* JS */;
		};
		FBD1D3ED2D9433A800FA2C43 /* Exceptions for "AppSupportFiles" folder in "JS2" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				"AliVideoCert-com_appyl_yl-20250103200919.crt",
			);
			target = FBD1D3CF2D9433A800FA2C43 /* JS2 */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		4E3630552D288E0600D1A816 /* AppSupportFiles */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				FB7596F82D62EB6A000241A3 /* Exceptions for "AppSupportFiles" folder in "YL" target */,
				FB7597112D62EB70000241A3 /* Exceptions for "AppSupportFiles" folder in "JS" target */,
				FBD1D3ED2D9433A800FA2C43 /* Exceptions for "AppSupportFiles" folder in "JS2" target */,
			);
			path = AppSupportFiles;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		97C146EB1CF9000F007C117D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AB6EC12238E2EFD8DBF6BF33 /* (null) in Frameworks */,
				F11983B2CD9BD5AE4F685301 /* Pods_Runner.framework in Frameworks */,
				A48A6A42783C0BA1CE67A795 /* Pods_Runner.framework in Frameworks */,
				404DB663D0BE1E37E2410D90 /* Pods_Runner.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FB7596E62D62EB6A000241A3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0004078E25E5A8E19E41073E /* Pods_YL.framework in Frameworks */,
				9966BC07C298CB8B673B67DA /* Pods_YL.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FB7596FF2D62EB70000241A3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4FA6B080841CE3E1D670B15F /* Pods_JS.framework in Frameworks */,
				3F9A0F7F37498F47B3240433 /* Pods_JS.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FBD1D3D52D9433A800FA2C43 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D0D82CB69247895912F158FE /* Pods_JS2.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		002142C92DAFE89E00805928 /* JS */ = {
			isa = PBXGroup;
			children = (
				00499B172DB642F10022EDD6 /* exportOptions.plist */,
				002142D22DAFE8AE00805928 /* Release-JS.xcconfig */,
				002142C62DAFE89E00805928 /* Assets-JS.xcassets */,
				002142C72DAFE89E00805928 /* GoogleService-Info.plist */,
				002142C82DAFE89E00805928 /* JSRelease-JS.entitlements */,
			);
			path = JS;
			sourceTree = "<group>";
		};
		002142DC2DAFE9CE00805928 /* YL */ = {
			isa = PBXGroup;
			children = (
				4E80C1CB2D3C48AB007F6C7F /* exportOptions.plist */,
				00C56BEC2DB637A70092C07B /* GoogleService-Info.plist */,
				002142D72DAFE9CE00805928 /* Assets-YL.xcassets */,
				002142D82DAFE9CE00805928 /* Debug-YL.xcconfig */,
				002142D92DAFE9CE00805928 /* Release-YL.xcconfig */,
				002142DA2DAFE9CE00805928 /* YLDebug-YL.entitlements */,
				002142DB2DAFE9CE00805928 /* YLRelease-YL.entitlements */,
			);
			path = YL;
			sourceTree = "<group>";
		};
		331C8082294A63A400263BE5 /* RunnerTests */ = {
			isa = PBXGroup;
			children = (
				331C807B294A618700263BE5 /* RunnerTests.swift */,
			);
			path = RunnerTests;
			sourceTree = "<group>";
		};
		9740EEB11CF90186004384FC /* Flutter */ = {
			isa = PBXGroup;
			children = (
				3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */,
				9740EEB21CF90195004384FC /* Debug.xcconfig */,
				7AFA3C8E1D35360C0083082E /* Release.xcconfig */,
			);
			name = Flutter;
			sourceTree = "<group>";
		};
		97C146E51CF9000F007C117D = {
			isa = PBXGroup;
			children = (
				00499B522DB667C20022EDD6 /* Generated.xcconfig */,
				002142DC2DAFE9CE00805928 /* YL */,
				002142C92DAFE89E00805928 /* JS */,
				FBD1D3F02D94343800FA2C43 /* JS2 */,
				00B17E392DACE53B00DD198E /* YL.plist */,
				00B17E372DACE52E00DD198E /* JS2.plist */,
				00B17E352DACE52300DD198E /* JS.plist */,
				9740EEB11CF90186004384FC /* Flutter */,
				97C146F01CF9000F007C117D /* Runner */,
				97C146EF1CF9000F007C117D /* Products */,
				331C8082294A63A400263BE5 /* RunnerTests */,
				BBC3FD0DCA10BC0E401BD2B2 /* Pods */,
				FB7597222D62F4A3000241A3 /* Recovered References */,
				9D8A6E074DC4343CB89D0020 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		97C146EF1CF9000F007C117D /* Products */ = {
			isa = PBXGroup;
			children = (
				97C146EE1CF9000F007C117D /* Runner.app */,
				FB7596F62D62EB6A000241A3 /* YL.app */,
				FB75970F2D62EB70000241A3 /* JS.app */,
				FBD1D3EB2D9433A800FA2C43 /* JS2.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		97C146F01CF9000F007C117D /* Runner */ = {
			isa = PBXGroup;
			children = (
				4E3630552D288E0600D1A816 /* AppSupportFiles */,
				4AB84B5A2CDA4D1A00295575 /* Runner.entitlements */,
				97C146FA1CF9000F007C117D /* Main.storyboard */,
				97C146FD1CF9000F007C117D /* Assets.xcassets */,
				97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */,
				97C147021CF9000F007C117D /* Info.plist */,
				1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */,
				1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */,
				74858FAE1ED2DC5600515810 /* AppDelegate.swift */,
				74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */,
			);
			path = Runner;
			sourceTree = "<group>";
		};
		9D8A6E074DC4343CB89D0020 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				1F31EE3D7A8E5F8B67A20508 /* Pods_Runner.framework */,
				C43A6BB18532020417615117 /* Pods_YL.framework */,
				2CAF7F0C8872748D01698D02 /* Pods_JS2.framework */,
				5DFBF8BF68E4F513C0F5B25E /* Pods_JS.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		BBC3FD0DCA10BC0E401BD2B2 /* Pods */ = {
			isa = PBXGroup;
			children = (
				1606D8F6F2B172199465D55C /* Pods-Runner.debug.xcconfig */,
				90BA11E8F4E2C00254B5728D /* Pods-Runner.release.xcconfig */,
				7C0736F17E402969358E0DB0 /* Pods-Runner.profile.xcconfig */,
				5F4A68A9994A1835C20B7AAA /* Pods-RunnerTests.profile.xcconfig */,
				687304D7847D835FD1DE375F /* Pods-JS.debug.xcconfig */,
				5720D039F5F596A638743CD0 /* Pods-JS.release.xcconfig */,
				45CD3534C530EB39E3678FAF /* Pods-JS.profile.xcconfig */,
				E633F86CB6836F32665A4E97 /* Pods-YL.debug.xcconfig */,
				319CC0A0AD937106E9BDDB93 /* Pods-YL.release.xcconfig */,
				9615F567C893697150278ECD /* Pods-YL.profile.xcconfig */,
				A6A9D1FA94F55ABB76358396 /* Pods-JS.debug-yl.xcconfig */,
				EA9BC7549B1DAED402015C23 /* Pods-JS.debug-js.xcconfig */,
				A0CEF702D08289036D5ECF51 /* Pods-Runner.debug-yl.xcconfig */,
				4A9D1DC7D3659118AEACED02 /* Pods-Runner.debug-js.xcconfig */,
				FDD29CD76D257CD439DD146A /* Pods-RunnerTests.debug-yl.xcconfig */,
				5313F0F1E3862C0025F94FA3 /* Pods-RunnerTests.debug-js.xcconfig */,
				436FF1F3AE1BD42F95C215DB /* Pods-YL.debug-yl.xcconfig */,
				47BA54402878C6B845EBFDC2 /* Pods-YL.debug-js.xcconfig */,
				3D442BF9C0CD6ADB12CEEAAC /* Pods-JS.release-js.xcconfig */,
				89EA52F695B34BF7FBCDF8F0 /* Pods-JS.release-yl.xcconfig */,
				283DAEB72E4AA09137E968A5 /* Pods-Runner.release-js.xcconfig */,
				744A84B464904A6E2D09B5B7 /* Pods-Runner.release-yl.xcconfig */,
				4FB987968A1D2926F394E090 /* Pods-YL.release-js.xcconfig */,
				9A79C5279BA8847E365665A4 /* Pods-YL.release-yl.xcconfig */,
				1772D9067679CB010F984FE8 /* Pods-JS.profile-yl.xcconfig */,
				8397838C1BB71D976B40F3D1 /* Pods-JS.profile-js.xcconfig */,
				1DBBD6E435F4DDE420B4ABFA /* Pods-Runner.profile-yl.xcconfig */,
				64D0EF6244CF53C04EEC2928 /* Pods-Runner.profile-js.xcconfig */,
				2444F5F6A882F287BB8A6762 /* Pods-YL.profile-yl.xcconfig */,
				B13F2ACA7E433E2B4CF57DB9 /* Pods-YL.profile-js.xcconfig */,
				147BD11AE2E4BBAE129F5AC7 /* Pods-JS.debug-js2.xcconfig */,
				893A9F66069EFE64991B3E75 /* Pods-JS.release-js2.xcconfig */,
				E6A422D9A774EBB0C6313A5A /* Pods-JS.profile-js2.xcconfig */,
				0DF1A7B9C9B044DE359EC101 /* Pods-JS2.debug.xcconfig */,
				E6D31185C3D053DEE38B9C6D /* Pods-JS2.debug-yl.xcconfig */,
				AEC6272EFD3F1BE09AE9C694 /* Pods-JS2.debug-js.xcconfig */,
				A1B59F968BACD0D20AE9B461 /* Pods-JS2.debug-js2.xcconfig */,
				3C8F58ABCDC595F39B795653 /* Pods-JS2.release.xcconfig */,
				9376C97AF0CC0FF50E772AA4 /* Pods-JS2.release-yl.xcconfig */,
				007C60C9E0D5538B378AE892 /* Pods-JS2.release-js.xcconfig */,
				C3AC4796FEC5209BA12981CE /* Pods-JS2.release-js2.xcconfig */,
				7454BF5A9ED374E39EF40F16 /* Pods-JS2.profile.xcconfig */,
				2639A042649FE04FC8DF049F /* Pods-JS2.profile-yl.xcconfig */,
				0E8AD10CA67349F512AB1C8F /* Pods-JS2.profile-js.xcconfig */,
				0E230580014CCC0000B10700 /* Pods-JS2.profile-js2.xcconfig */,
				0A1EEEE32D540E927F4E4F9C /* Pods-Runner.debug-js2.xcconfig */,
				F87C5D483350203C37CBA21A /* Pods-Runner.release-js2.xcconfig */,
				9F3C387C1ECD62EC4229FF0C /* Pods-Runner.profile-js2.xcconfig */,
				5F5C19DFEE26585AE98CDADE /* Pods-YL.debug-js2.xcconfig */,
				C04E101D2BF69D10E98F297D /* Pods-YL.release-js2.xcconfig */,
				70D96247805745898AD8E41A /* Pods-YL.profile-js2.xcconfig */,
				CB3C3DD71E89005C9D6815A9 /* Pods-JS.opo.xcconfig */,
				33BA59504EE6A6EFFEBFBF7D /* Pods-JS2.opo.xcconfig */,
				1FFA0B633F8A01D10DA97B50 /* Pods-Runner.opo.xcconfig */,
				28602180CE472F081A4032F7 /* Pods-YL.opo.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		FB7597222D62F4A3000241A3 /* Recovered References */ = {
			isa = PBXGroup;
			children = (
				411A78EB9EFA73C7772F8EA4 /* Pods_Runner.framework */,
				FE620321AA57D35106C93AE6 /* Pods_Runner.framework */,
				9DEF331880F56F20B826C2A6 /* Pods_YL.framework */,
				B7231853032A904AB04C3E95 /* Pods_JS.framework */,
			);
			name = "Recovered References";
			sourceTree = "<group>";
		};
		FBD1D3F02D94343800FA2C43 /* JS2 */ = {
			isa = PBXGroup;
			children = (
				00499B192DB642FC0022EDD6 /* exportOptions.plist */,
				002142AC2DAFE74800805928 /* Release-JS2.xcconfig */,
				00162B3B2D9ECFAE005CA975 /* JS2Release-JS2.entitlements */,
				FBD1D3EE2D94343800FA2C43 /* Assets-JS2.xcassets */,
				FBD1D3EF2D94343800FA2C43 /* GoogleService-Info.plist */,
			);
			path = JS2;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		97C146ED1CF9000F007C117D /* Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */;
			buildPhases = (
				85AA58AF345B15A1BDD48D57 /* [CP] Check Pods Manifest.lock */,
				9740EEB61CF901F6004384FC /* Run Script */,
				97C146EA1CF9000F007C117D /* Sources */,
				97C146EB1CF9000F007C117D /* Frameworks */,
				97C146EC1CF9000F007C117D /* Resources */,
				9705A1C41CF9048500538489 /* Embed Frameworks */,
				3B06AD1E1E4923F5004D2608 /* Thin Binary */,
				8CD1B36189EDEEF980FF4FC4 /* [CP] Embed Pods Frameworks */,
				27386424B4B1E2684CE44D34 /* [CP] Copy Pods Resources */,
				1FB019E42CCF25F0AAA48FCD /* FlutterFire: "flutterfire bundle-service-file" */,
				FEBF5784CDF25C8858ECA317 /* FlutterFire: "flutterfire upload-crashlytics-symbols" */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				4E3630552D288E0600D1A816 /* AppSupportFiles */,
			);
			name = Runner;
			productName = Runner;
			productReference = 97C146EE1CF9000F007C117D /* Runner.app */;
			productType = "com.apple.product-type.application";
		};
		FB7596E02D62EB6A000241A3 /* YL */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FB7596F22D62EB6A000241A3 /* Build configuration list for PBXNativeTarget "YL" */;
			buildPhases = (
				FB7596E12D62EB6A000241A3 /* [CP] Check Pods Manifest.lock */,
				FB7596E22D62EB6A000241A3 /* Run Script */,
				FB7596E32D62EB6A000241A3 /* Sources */,
				FB7596E62D62EB6A000241A3 /* Frameworks */,
				FB7596E82D62EB6A000241A3 /* Resources */,
				FB7596EE2D62EB6A000241A3 /* Embed Frameworks */,
				FB7596EF2D62EB6A000241A3 /* Thin Binary */,
				FB7596F02D62EB6A000241A3 /* [CP] Embed Pods Frameworks */,
				FB7596F12D62EB6A000241A3 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				4E3630552D288E0600D1A816 /* AppSupportFiles */,
			);
			name = YL;
			productName = Runner;
			productReference = FB7596F62D62EB6A000241A3 /* YL.app */;
			productType = "com.apple.product-type.application";
		};
		FB7596F92D62EB70000241A3 /* JS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FB75970B2D62EB70000241A3 /* Build configuration list for PBXNativeTarget "JS" */;
			buildPhases = (
				FB7596FA2D62EB70000241A3 /* [CP] Check Pods Manifest.lock */,
				FB7596FB2D62EB70000241A3 /* Run Script */,
				FB7596FC2D62EB70000241A3 /* Sources */,
				FB7596FF2D62EB70000241A3 /* Frameworks */,
				FB7597012D62EB70000241A3 /* Resources */,
				FB7597072D62EB70000241A3 /* Embed Frameworks */,
				FB7597082D62EB70000241A3 /* Thin Binary */,
				FB7597092D62EB70000241A3 /* [CP] Embed Pods Frameworks */,
				FB75970A2D62EB70000241A3 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				4E3630552D288E0600D1A816 /* AppSupportFiles */,
			);
			name = JS;
			productName = Runner;
			productReference = FB75970F2D62EB70000241A3 /* JS.app */;
			productType = "com.apple.product-type.application";
		};
		FBD1D3CF2D9433A800FA2C43 /* JS2 */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FBD1D3E12D9433A800FA2C43 /* Build configuration list for PBXNativeTarget "JS2" */;
			buildPhases = (
				FBD1D3D02D9433A800FA2C43 /* [CP] Check Pods Manifest.lock */,
				FBD1D3D12D9433A800FA2C43 /* Run Script */,
				FBD1D3D22D9433A800FA2C43 /* Sources */,
				FBD1D3D52D9433A800FA2C43 /* Frameworks */,
				FBD1D3D82D9433A800FA2C43 /* Resources */,
				FBD1D3DD2D9433A800FA2C43 /* Embed Frameworks */,
				FBD1D3DE2D9433A800FA2C43 /* Thin Binary */,
				FBD1D3DF2D9433A800FA2C43 /* [CP] Embed Pods Frameworks */,
				FBD1D3E02D9433A800FA2C43 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				4E3630552D288E0600D1A816 /* AppSupportFiles */,
			);
			name = JS2;
			productName = Runner;
			productReference = FBD1D3EB2D9433A800FA2C43 /* JS2.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		97C146E61CF9000F007C117D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1510;
				ORGANIZATIONNAME = "";
				TargetAttributes = {
					97C146ED1CF9000F007C117D = {
						CreatedOnToolsVersion = 7.3.1;
						LastSwiftMigration = 1100;
					};
				};
			};
			buildConfigurationList = 97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 97C146E51CF9000F007C117D;
			preferredProjectObjectVersion = 77;
			productRefGroup = 97C146EF1CF9000F007C117D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				97C146ED1CF9000F007C117D /* Runner */,
				FB7596E02D62EB6A000241A3 /* YL */,
				FB7596F92D62EB70000241A3 /* JS */,
				FBD1D3CF2D9433A800FA2C43 /* JS2 */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		97C146EC1CF9000F007C117D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				00499B552DB667C20022EDD6 /* Generated.xcconfig in Resources */,
				002142CA2DAFE89E00805928 /* Assets-JS.xcassets in Resources */,
				002142D62DAFE8AE00805928 /* Release-JS.xcconfig in Resources */,
				002142CB2DAFE89E00805928 /* GoogleService-Info.plist in Resources */,
				97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */,
				002142DD2DAFE9CE00805928 /* Debug-YL.xcconfig in Resources */,
				002142DE2DAFE9CE00805928 /* Release-YL.xcconfig in Resources */,
				002142DF2DAFE9CE00805928 /* Assets-YL.xcassets in Resources */,
				4E80C1CC2D3C48AB007F6C7F /* exportOptions.plist in Resources */,
				00C56BED2DB637A70092C07B /* GoogleService-Info.plist in Resources */,
				3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */,
				97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */,
				FBD1D3F32D94343800FA2C43 /* GoogleService-Info.plist in Resources */,
				FBD1D3F42D94343800FA2C43 /* Assets-JS2.xcassets in Resources */,
				97C146FC1CF9000F007C117D /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FB7596E82D62EB6A000241A3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FB7596E92D62EB6A000241A3 /* LaunchScreen.storyboard in Resources */,
				00B17E3A2DACE53B00DD198E /* YL.plist in Resources */,
				FB7596EA2D62EB6A000241A3 /* exportOptions.plist in Resources */,
				002142E62DAFE9CE00805928 /* Debug-YL.xcconfig in Resources */,
				002142E72DAFE9CE00805928 /* Release-YL.xcconfig in Resources */,
				002142E82DAFE9CE00805928 /* Assets-YL.xcassets in Resources */,
				00499B532DB667C20022EDD6 /* Generated.xcconfig in Resources */,
				00C56BEE2DB637A70092C07B /* GoogleService-Info.plist in Resources */,
				FB7596EB2D62EB6A000241A3 /* AppFrameworkInfo.plist in Resources */,
				FB7596ED2D62EB6A000241A3 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FB7597012D62EB70000241A3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FB7597022D62EB70000241A3 /* LaunchScreen.storyboard in Resources */,
				00B17E362DACE52300DD198E /* JS.plist in Resources */,
				002142D52DAFE8AE00805928 /* Release-JS.xcconfig in Resources */,
				FB7597042D62EB70000241A3 /* AppFrameworkInfo.plist in Resources */,
				FB7597062D62EB70000241A3 /* Main.storyboard in Resources */,
				00499B562DB667C20022EDD6 /* Generated.xcconfig in Resources */,
				00499B182DB642F10022EDD6 /* exportOptions.plist in Resources */,
				002142CC2DAFE89E00805928 /* Assets-JS.xcassets in Resources */,
				002142CD2DAFE89E00805928 /* GoogleService-Info.plist in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FBD1D3D82D9433A800FA2C43 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FBD1D3F52D94347300FA2C43 /* Assets-JS2.xcassets in Resources */,
				FBD1D3D92D9433A800FA2C43 /* LaunchScreen.storyboard in Resources */,
				FBD1D3F12D94343800FA2C43 /* GoogleService-Info.plist in Resources */,
				FBD1D3DB2D9433A800FA2C43 /* AppFrameworkInfo.plist in Resources */,
				00B17E382DACE52E00DD198E /* JS2.plist in Resources */,
				00499B542DB667C20022EDD6 /* Generated.xcconfig in Resources */,
				00499B1A2DB642FC0022EDD6 /* exportOptions.plist in Resources */,
				FBD1D3DC2D9433A800FA2C43 /* Main.storyboard in Resources */,
				002142AD2DAFE74800805928 /* Release-JS2.xcconfig in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		1FB019E42CCF25F0AAA48FCD /* FlutterFire: "flutterfire bundle-service-file" */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "FlutterFire: \"flutterfire bundle-service-file\"";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\n#!/bin/bash\nPATH=\"${PATH}:$FLUTTER_ROOT/bin:${PUB_CACHE}/bin:$HOME/.pub-cache/bin\"\nflutterfire bundle-service-file --plist-destination=\"${BUILT_PRODUCTS_DIR}/${PRODUCT_NAME}.app\" --build-configuration=${CONFIGURATION} --platform=ios --apple-project-path=\"${SRCROOT}\"\n";
		};
		27386424B4B1E2684CE44D34 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		3B06AD1E1E4923F5004D2608 /* Thin Binary */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${TARGET_BUILD_DIR}/${INFOPLIST_PATH}",
			);
			name = "Thin Binary";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" embed_and_thin";
		};
		85AA58AF345B15A1BDD48D57 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Runner-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		8CD1B36189EDEEF980FF4FC4 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		9740EEB61CF901F6004384FC /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" build\n";
		};
		FB7596E12D62EB6A000241A3 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-YL-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		FB7596E22D62EB6A000241A3 /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" build\n";
		};
		FB7596EF2D62EB6A000241A3 /* Thin Binary */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${TARGET_BUILD_DIR}/${INFOPLIST_PATH}",
			);
			name = "Thin Binary";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" embed_and_thin";
		};
		FB7596F02D62EB6A000241A3 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-YL/Pods-YL-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-YL/Pods-YL-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-YL/Pods-YL-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		FB7596F12D62EB6A000241A3 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-YL/Pods-YL-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-YL/Pods-YL-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-YL/Pods-YL-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		FB7596FA2D62EB70000241A3 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-JS-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		FB7596FB2D62EB70000241A3 /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" build";
		};
		FB7597082D62EB70000241A3 /* Thin Binary */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${TARGET_BUILD_DIR}/${INFOPLIST_PATH}",
			);
			name = "Thin Binary";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" embed_and_thin";
		};
		FB7597092D62EB70000241A3 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-JS/Pods-JS-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-JS/Pods-JS-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-JS/Pods-JS-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		FB75970A2D62EB70000241A3 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-JS/Pods-JS-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-JS/Pods-JS-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-JS/Pods-JS-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		FBD1D3D02D9433A800FA2C43 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-JS2-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		FBD1D3D12D9433A800FA2C43 /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" build";
		};
		FBD1D3DE2D9433A800FA2C43 /* Thin Binary */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${TARGET_BUILD_DIR}/${INFOPLIST_PATH}",
			);
			name = "Thin Binary";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" embed_and_thin";
		};
		FBD1D3DF2D9433A800FA2C43 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-JS2/Pods-JS2-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-JS2/Pods-JS2-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-JS2/Pods-JS2-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		FBD1D3E02D9433A800FA2C43 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-JS2/Pods-JS2-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-JS2/Pods-JS2-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-JS2/Pods-JS2-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		FEBF5784CDF25C8858ECA317 /* FlutterFire: "flutterfire upload-crashlytics-symbols" */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "FlutterFire: \"flutterfire upload-crashlytics-symbols\"";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\n#!/bin/bash\nPATH=\"${PATH}:$FLUTTER_ROOT/bin:${PUB_CACHE}/bin:$HOME/.pub-cache/bin\"\n\nif [ -z \"$PODS_ROOT\" ] || [ ! -d \"$PODS_ROOT/FirebaseCrashlytics\" ]; then\n  # Cannot use \"BUILD_DIR%/Build/*\" as per Firebase documentation, it points to \"flutter-project/build/ios/*\" path which doesn't have run script\n  DERIVED_DATA_PATH=$(echo \"$BUILD_ROOT\" | sed -E 's|(.*DerivedData/[^/]+).*|\\1|')\n  PATH_TO_CRASHLYTICS_UPLOAD_SCRIPT=\"${DERIVED_DATA_PATH}/SourcePackages/checkouts/firebase-ios-sdk/Crashlytics/run\"\nelse\n  PATH_TO_CRASHLYTICS_UPLOAD_SCRIPT=\"$PODS_ROOT/FirebaseCrashlytics/run\"\nfi\n\n# Command to upload symbols script used to upload symbols to Firebase server\nflutterfire upload-crashlytics-symbols --upload-symbols-script-path=\"$PATH_TO_CRASHLYTICS_UPLOAD_SCRIPT\" --platform=ios --apple-project-path=\"${SRCROOT}\" --env-platform-name=\"${PLATFORM_NAME}\" --env-configuration=\"${CONFIGURATION}\" --env-project-dir=\"${PROJECT_DIR}\" --env-built-products-dir=\"${BUILT_PRODUCTS_DIR}\" --env-dwarf-dsym-folder-path=\"${DWARF_DSYM_FOLDER_PATH}\" --env-dwarf-dsym-file-name=\"${DWARF_DSYM_FILE_NAME}\" --env-infoplist-path=\"${INFOPLIST_PATH}\" --build-configuration=${CONFIGURATION}\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		97C146EA1CF9000F007C117D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */,
				1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FB7596E32D62EB6A000241A3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FB7596E42D62EB6A000241A3 /* AppDelegate.swift in Sources */,
				FB7596E52D62EB6A000241A3 /* GeneratedPluginRegistrant.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FB7596FC2D62EB70000241A3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FB7596FD2D62EB70000241A3 /* AppDelegate.swift in Sources */,
				FB7596FE2D62EB70000241A3 /* GeneratedPluginRegistrant.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		FBD1D3D22D9433A800FA2C43 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FBD1D3D32D9433A800FA2C43 /* AppDelegate.swift in Sources */,
				FBD1D3D42D9433A800FA2C43 /* GeneratedPluginRegistrant.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		97C146FA1CF9000F007C117D /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C146FB1CF9000F007C117D /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C147001CF9000F007C117D /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		0021427D2DAFDA2700805928 /* Release-JS */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "Release-JS";
		};
		0021427E2DAFDA2700805928 /* Release-JS */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "永利娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.wd.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = wd_distribution;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Release-JS";
		};
		0021427F2DAFDA2700805928 /* Release-JS */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = YL.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "永利娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS";
				PRODUCT_BUNDLE_IDENTIFIER = com.wd.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = yl_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Release-JS";
		};
		002142802DAFDA2700805928 /* Release-JS */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 002142D22DAFE8AE00805928 /* Release-JS.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = JS.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "金沙娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.js.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = js_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Release-JS";
		};
		002142812DAFDA2700805928 /* Release-JS */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = JS2.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "金沙娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.app.elegant.js2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = js2_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Release-JS";
		};
		002142822DAFDA2F00805928 /* Release-JS2 */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "Release-JS2";
		};
		002142832DAFDA2F00805928 /* Release-JS2 */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "永利娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.wd.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = wd_distribution;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Release-JS2";
		};
		002142842DAFDA2F00805928 /* Release-JS2 */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = YL.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "永利娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS";
				PRODUCT_BUNDLE_IDENTIFIER = com.wd.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = yl_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Release-JS2";
		};
		002142852DAFDA2F00805928 /* Release-JS2 */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = JS.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "金沙娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.js.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = js_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Release-JS2";
		};
		002142862DAFDA2F00805928 /* Release-JS2 */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 002142AC2DAFE74800805928 /* Release-JS2.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = JS2.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "金沙娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.app.elegant.js2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = js2_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Release-JS2";
		};
		00B180212DAF8B8500DD198E /* Release-YL */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "Release-YL";
		};
		00B180222DAF8B8500DD198E /* Release-YL */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "永利娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.wd.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = wd_distribution;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Release-YL";
		};
		00B180232DAF8B8500DD198E /* Release-YL */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 002142D92DAFE9CE00805928 /* Release-YL.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = YL.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "永利娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS";
				PRODUCT_BUNDLE_IDENTIFIER = com.wd.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = yl_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Release-YL";
		};
		00B180242DAF8B8500DD198E /* Release-YL */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = JS.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "金沙娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.js.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = js_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Release-YL";
		};
		00B180252DAF8B8500DD198E /* Release-YL */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = JS2.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "金沙娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.app.elegant.js2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = js2_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Release-YL";
		};
		00CFB3B52DBDD6530026C301 /* Debug-YL */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = "Debug-YL";
		};
		00CFB3B62DBDD6530026C301 /* Debug-YL */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "永利娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.wd.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = wd_distribution;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Debug-YL";
		};
		00CFB3B72DBDD6530026C301 /* Debug-YL */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 002142D82DAFE9CE00805928 /* Debug-YL.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = YL.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "永利娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS";
				PRODUCT_BUNDLE_IDENTIFIER = com.wd.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = yl_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Debug-YL";
		};
		00CFB3B82DBDD6530026C301 /* Debug-YL */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = JS.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "金沙娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.js.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = js_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Debug-YL";
		};
		00CFB3B92DBDD6530026C301 /* Debug-YL */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = JS2.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "金沙娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.app.elegant.js2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = js2_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Debug-YL";
		};
		249021D3217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		249021D4217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "永利娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.wd.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Profile;
		};
		97C147031CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		97C147041CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		97C147061CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "永利娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.wd.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		97C147071CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "永利娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.wd.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = wd_distribution;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		FB7596F32D62EB6A000241A3 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E633F86CB6836F32665A4E97 /* Pods-YL.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = YL.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "永利娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS";
				PRODUCT_BUNDLE_IDENTIFIER = com.wd.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = yl_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		FB7596F42D62EB6A000241A3 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 319CC0A0AD937106E9BDDB93 /* Pods-YL.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = YL.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "永利娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS";
				PRODUCT_BUNDLE_IDENTIFIER = com.wd.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = yl_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		FB7596F52D62EB6A000241A3 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = YL.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "永利娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D COCOAPODS";
				PRODUCT_BUNDLE_IDENTIFIER = com.wd.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = yl_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Profile;
		};
		FB75970C2D62EB70000241A3 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 002142D22DAFE8AE00805928 /* Release-JS.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = JS.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "金沙娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.js.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = js_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		FB75970D2D62EB70000241A3 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 002142D22DAFE8AE00805928 /* Release-JS.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = JS.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "金沙娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.js.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = js_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		FB75970E2D62EB70000241A3 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = JS.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "金沙娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.js.elegant2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = js_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Profile;
		};
		FBD1D3E22D9433A800FA2C43 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 002142AC2DAFE74800805928 /* Release-JS2.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = JS2.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "金沙娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.app.elegant.js2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = js2_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		FBD1D3E52D9433A800FA2C43 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 002142AC2DAFE74800805928 /* Release-JS2.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = JS2.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "金沙娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.app.elegant.js2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = js2_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		FBD1D3E82D9433A800FA2C43 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 84DQM7K34Q;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = JS2.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "金沙娱乐";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.app.elegant.js2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = js2_ad_hoc;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Profile;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147031CF9000F007C117D /* Debug */,
				97C147041CF9000F007C117D /* Release */,
				00B180212DAF8B8500DD198E /* Release-YL */,
				00CFB3B52DBDD6530026C301 /* Debug-YL */,
				0021427D2DAFDA2700805928 /* Release-JS */,
				002142822DAFDA2F00805928 /* Release-JS2 */,
				249021D3217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147061CF9000F007C117D /* Debug */,
				97C147071CF9000F007C117D /* Release */,
				00B180222DAF8B8500DD198E /* Release-YL */,
				00CFB3B62DBDD6530026C301 /* Debug-YL */,
				0021427E2DAFDA2700805928 /* Release-JS */,
				002142832DAFDA2F00805928 /* Release-JS2 */,
				249021D4217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FB7596F22D62EB6A000241A3 /* Build configuration list for PBXNativeTarget "YL" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FB7596F32D62EB6A000241A3 /* Debug */,
				FB7596F42D62EB6A000241A3 /* Release */,
				00B180232DAF8B8500DD198E /* Release-YL */,
				00CFB3B72DBDD6530026C301 /* Debug-YL */,
				0021427F2DAFDA2700805928 /* Release-JS */,
				002142842DAFDA2F00805928 /* Release-JS2 */,
				FB7596F52D62EB6A000241A3 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FB75970B2D62EB70000241A3 /* Build configuration list for PBXNativeTarget "JS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FB75970C2D62EB70000241A3 /* Debug */,
				FB75970D2D62EB70000241A3 /* Release */,
				00B180242DAF8B8500DD198E /* Release-YL */,
				00CFB3B82DBDD6530026C301 /* Debug-YL */,
				002142802DAFDA2700805928 /* Release-JS */,
				002142852DAFDA2F00805928 /* Release-JS2 */,
				FB75970E2D62EB70000241A3 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FBD1D3E12D9433A800FA2C43 /* Build configuration list for PBXNativeTarget "JS2" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FBD1D3E22D9433A800FA2C43 /* Debug */,
				FBD1D3E52D9433A800FA2C43 /* Release */,
				00B180252DAF8B8500DD198E /* Release-YL */,
				00CFB3B92DBDD6530026C301 /* Debug-YL */,
				002142812DAFDA2700805928 /* Release-JS */,
				002142862DAFDA2F00805928 /* Release-JS2 */,
				FBD1D3E82D9433A800FA2C43 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 97C146E61CF9000F007C117D /* Project object */;
}
