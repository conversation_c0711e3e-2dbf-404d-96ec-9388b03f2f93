PODS:
  - AliPlayerSDK_iOS (6.21.0):
    - AliPlayerSDK_iOS/AliPlayerSDK (= 6.21.0)
  - AliPlayerSDK_iOS/AliPlayerSDK (6.21.0)
  - app_badge_plus (1.2.1):
    - Flutter
  - audio_session (0.0.1):
    - Flutter
  - camera_avfoundation (0.0.1):
    - Flutter
  - captcha_plugin_flutter (0.0.2):
    - Flutter
    - NTESVerifyCode (= 3.6.4)
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - fc_native_video_thumbnail (0.0.1):
    - Flutter
    - FlutterMacOS
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Firebase/CoreOnly (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - Firebase/Crashlytics (11.10.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 11.10.0)
  - firebase_core (3.13.0):
    - Firebase/CoreOnly (= 11.10.0)
    - Flutter
  - firebase_crashlytics (4.3.5):
    - Firebase/Crashlytics (= 11.10.0)
    - firebase_core
    - Flutter
  - FirebaseCore (11.10.0):
    - FirebaseCoreInternal (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreExtension (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - FirebaseCoreInternal (11.10.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseCrashlytics (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSessions (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - FirebaseInstallations (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseRemoteConfigInterop (11.11.0)
  - FirebaseSessions (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseCoreExtension (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - nanopb (~> 3.30910.0)
    - PromisesSwift (~> 2.1)
  - Flutter (1.0.0)
  - flutter_aliplayer (6.21.0):
    - Flutter
    - flutter_aliplayer/AliPlayerSDKFrameworks (= 6.21.0)
    - MJExtension
  - flutter_aliplayer/AliPlayerSDKFrameworks (6.21.0):
    - AliPlayerSDK_iOS (= 6.21.0)
    - Flutter
    - MJExtension
  - flutter_avif_ios (0.0.1):
    - Flutter
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_image_gallery_saver (0.0.1):
    - Flutter
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_plugin_engagelab (0.0.3):
    - Flutter
    - MTPush (= 4.4.0)
  - fluttertoast (0.0.2):
    - Flutter
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - HydraAsync (2.0.6)
  - image_gallery_saver (2.0.2):
    - Flutter
  - image_gallery_saver_plus (0.0.1):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - libOpenInstallSDK (2.8.5.2)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - media_kit_libs_ios_video (1.0.4):
    - Flutter
  - media_kit_video (0.0.1):
    - Flutter
  - memory_info (0.0.1):
    - Flutter
  - MJExtension (3.4.2)
  - MTPush (4.4.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - NTESVerifyCode (3.6.4)
  - open_file_ios (0.0.1):
    - Flutter
  - openinstall_flutter_plugin (0.0.1):
    - Flutter
    - libOpenInstallSDK
  - package_info_plus (0.4.5):
    - Flutter
  - pasteboard (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - photo_manager (2.0.0):
    - Flutter
    - FlutterMacOS
  - pointer_interceptor_ios (0.0.1):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - record_darwin (1.0.0):
    - Flutter
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - sensors_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - tencent_cloud_chat_sdk (8.0.0):
    - Flutter
    - HydraAsync
    - TXIMSDK_Plus_iOS_XCFramework (= 8.5.6864)
  - tencent_cloud_uikit_core (0.0.1):
    - Flutter
  - TXIMSDK_Plus_iOS_XCFramework (8.5.6864)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - volume_controller (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - app_badge_plus (from `.symlinks/plugins/app_badge_plus/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - captcha_plugin_flutter (from `.symlinks/plugins/captcha_plugin_flutter/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - fc_native_video_thumbnail (from `.symlinks/plugins/fc_native_video_thumbnail/darwin`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - Flutter (from `Flutter`)
  - flutter_aliplayer (from `.symlinks/plugins/flutter_aliplayer/ios`)
  - flutter_avif_ios (from `.symlinks/plugins/flutter_avif_ios/ios`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_image_gallery_saver (from `.symlinks/plugins/flutter_image_gallery_saver/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_plugin_engagelab (from `.symlinks/plugins/flutter_plugin_engagelab/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - image_gallery_saver_plus (from `.symlinks/plugins/image_gallery_saver_plus/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - media_kit_libs_ios_video (from `.symlinks/plugins/media_kit_libs_ios_video/ios`)
  - media_kit_video (from `.symlinks/plugins/media_kit_video/ios`)
  - memory_info (from `.symlinks/plugins/memory_info/ios`)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - openinstall_flutter_plugin (from `.symlinks/plugins/openinstall_flutter_plugin/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - pasteboard (from `.symlinks/plugins/pasteboard/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - pointer_interceptor_ios (from `.symlinks/plugins/pointer_interceptor_ios/ios`)
  - record_darwin (from `.symlinks/plugins/record_darwin/ios`)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - tencent_cloud_chat_sdk (from `.symlinks/plugins/tencent_cloud_chat_sdk/ios`)
  - tencent_cloud_uikit_core (from `.symlinks/plugins/tencent_cloud_uikit_core/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - volume_controller (from `.symlinks/plugins/volume_controller/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - AliPlayerSDK_iOS
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - GoogleDataTransport
    - GoogleUtilities
    - HydraAsync
    - libOpenInstallSDK
    - libwebp
    - Mantle
    - MJExtension
    - MTPush
    - nanopb
    - NTESVerifyCode
    - PromisesObjC
    - PromisesSwift
    - SDWebImage
    - SDWebImageWebPCoder
    - SwiftyGif
    - TXIMSDK_Plus_iOS_XCFramework

EXTERNAL SOURCES:
  app_badge_plus:
    :path: ".symlinks/plugins/app_badge_plus/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  captcha_plugin_flutter:
    :path: ".symlinks/plugins/captcha_plugin_flutter/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  fc_native_video_thumbnail:
    :path: ".symlinks/plugins/fc_native_video_thumbnail/darwin"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  Flutter:
    :path: Flutter
  flutter_aliplayer:
    :path: ".symlinks/plugins/flutter_aliplayer/ios"
  flutter_avif_ios:
    :path: ".symlinks/plugins/flutter_avif_ios/ios"
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_image_gallery_saver:
    :path: ".symlinks/plugins/flutter_image_gallery_saver/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_plugin_engagelab:
    :path: ".symlinks/plugins/flutter_plugin_engagelab/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  image_gallery_saver_plus:
    :path: ".symlinks/plugins/image_gallery_saver_plus/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  media_kit_libs_ios_video:
    :path: ".symlinks/plugins/media_kit_libs_ios_video/ios"
  media_kit_video:
    :path: ".symlinks/plugins/media_kit_video/ios"
  memory_info:
    :path: ".symlinks/plugins/memory_info/ios"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  openinstall_flutter_plugin:
    :path: ".symlinks/plugins/openinstall_flutter_plugin/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  pasteboard:
    :path: ".symlinks/plugins/pasteboard/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  pointer_interceptor_ios:
    :path: ".symlinks/plugins/pointer_interceptor_ios/ios"
  record_darwin:
    :path: ".symlinks/plugins/record_darwin/ios"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  tencent_cloud_chat_sdk:
    :path: ".symlinks/plugins/tencent_cloud_chat_sdk/ios"
  tencent_cloud_uikit_core:
    :path: ".symlinks/plugins/tencent_cloud_uikit_core/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  volume_controller:
    :path: ".symlinks/plugins/volume_controller/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  AliPlayerSDK_iOS: 9598c53c095bb4aca8b956f698f4d9f4ceeadeec
  app_badge_plus: 9f9eb6c683a8993a64727c40bd19f0cab8a4b542
  audio_session: 9bb7f6c970f21241b19f5a3658097ae459681ba0
  camera_avfoundation: 04b44aeb14070126c6529e5ab82cc7c9fca107cf
  captcha_plugin_flutter: ae54599fbe7b3815f90f1ac2e82efcf7309677f2
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  device_info_plus: 71ffc6ab7634ade6267c7a93088ed7e4f74e5896
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  fc_native_video_thumbnail: b511cec81fad66be9b28dd54b9adb39d40fcd6cc
  file_picker: 9b3292d7c8bc68c8a7bf8eb78f730e49c8efc517
  Firebase: 1fe1c0a7d9aaea32efe01fbea5f0ebd8d70e53a2
  firebase_core: 2d4534e7b489907dcede540c835b48981d890943
  firebase_crashlytics: 961a0812ba79ed8f89a8d5d1e3763daa6267a87a
  FirebaseCore: 8344daef5e2661eb004b177488d6f9f0f24251b7
  FirebaseCoreExtension: 6f357679327f3614e995dc7cf3f2d600bdc774ac
  FirebaseCoreInternal: ef4505d2afb1d0ebbc33162cb3795382904b5679
  FirebaseCrashlytics: 84b073c997235740e6a951b7ee49608932877e5c
  FirebaseInstallations: 9980995bdd06ec8081dfb6ab364162bdd64245c3
  FirebaseRemoteConfigInterop: 85bdce8babed7814816496bb6f082bc05b0a45e1
  FirebaseSessions: 9b3b30947b97a15370e0902ee7a90f50ef60ead6
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_aliplayer: 01154455e07cb581b7b716ff48f640ebcc393aaa
  flutter_avif_ios: 2553d2aafda56339cf924e943da89ad3f40af2dd
  flutter_image_compress_common: 1697a328fd72bfb335507c6bca1a65fa5ad87df1
  flutter_image_gallery_saver: 0453c83412e9691abef94c04c8d180724f5083a8
  flutter_keyboard_visibility: 4625131e43015dbbe759d9b20daaf77e0e3f6619
  flutter_native_splash: 6cad9122ea0fad137d23137dd14b937f3e90b145
  flutter_plugin_engagelab: 0808a5c8e94e3aa1bd43b840ff85c4c1c1440710
  fluttertoast: 2c67e14dce98bbdb200df9e1acf610d7a6264ea1
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  HydraAsync: 8d589bd725b0224f899afafc9a396327405f8063
  image_gallery_saver: 14711d79da40581063e8842a11acf1969d781ed7
  image_gallery_saver_plus: e597bf65a7846979417a3eae0763b71b6dfec6c3
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  just_audio: 4e391f57b79cad2b0674030a00453ca5ce817eed
  libOpenInstallSDK: 6196a6524100858704591fb8fa4709fd18096530
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  media_kit_libs_ios_video: 5a18affdb97d1f5d466dc79988b13eff6c5e2854
  media_kit_video: 1746e198cb697d1ffb734b1d05ec429d1fcd1474
  memory_info: 0c8ecafff5f646e2957972aee37801131affa512
  MJExtension: e97d164cb411aa9795cf576093a1fa208b4a8dd8
  MTPush: 88322fc0a046d1bbc9babc7fb7c032d276107e63
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  NTESVerifyCode: 20c99a9620e4ba640dfcbbb2396ecdb0ac467a4e
  open_file_ios: 5ff7526df64e4394b4fe207636b67a95e83078bb
  openinstall_flutter_plugin: a316342ef16d60a902b5aac724370d4514e092b1
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  pasteboard: 49088aeb6119d51f976a421db60d8e1ab079b63c
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  photo_manager: d2fbcc0f2d82458700ee6256a15018210a81d413
  pointer_interceptor_ios: ec847ef8b0915778bed2b2cef636f4d177fa8eed
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  record_darwin: fb1f375f1d9603714f55b8708a903bbb91ffdb0a
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  sensors_plus: 6a11ed0c2e1d0bd0b20b4029d3bad27d96e0c65b
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  tencent_cloud_chat_sdk: f60b13e08e8aa0c1efb4e1929e100fdba0f4c7b1
  tencent_cloud_uikit_core: 137e8ae40882b1929508e688182b2818708cc078
  TXIMSDK_Plus_iOS_XCFramework: 0353712b504d2206ce0f4d94b0eb3357673e1cfe
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  volume_controller: 3657a1f65bedb98fa41ff7dc5793537919f31b12
  wakelock_plus: 04623e3f525556020ebd4034310f20fe7fda8b49
  webview_flutter_wkwebview: 44d4dee7d7056d5ad185d25b38404436d56c547c

PODFILE CHECKSUM: 7d612342e974ad9bcc07784768b9f7d959f16559

COCOAPODS: 1.16.2
