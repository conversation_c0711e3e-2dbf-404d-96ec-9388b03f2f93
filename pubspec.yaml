name: game_store
description: "永利娱乐"

publish_to: "none"

version: 1.2.3+20


environment:
  sdk: ">=3.4.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  cached_network_image: ^3.3.0
  carousel_slider: ^5.0.0
  flutter_spinkit: ^5.2.1
  flutter_screenutil: ^5.9.3
  marquee: ^2.2.3
  equatable: ^2.0.5
  dio: ^5.5.0+1
  dio_smart_retry: ^6.0.0 #网络请求重试拦截器
  http: ^1.3.0
  flutter_bloc: ^8.1.6
  get_it: ^8.0.2
  shared_preferences: ^2.2.3
  pull_to_refresh: ^2.0.0 #下拉刷新
  fluttertoast: ^8.2.5 #吐司 toast
  flutter_easyloading: ^3.0.5 #loading
  fluro: ^2.0.5 #路由管理 router manage
  logger: ^2.4.0 #日志
  easy_localization: ^3.0.7+1 #语言国际化
  keyboard_dismisser: ^3.0.0 #键盘隐藏
  flutter_svg: ^2.0.10+1 #svg控件
  webview_flutter: ^4.8.0 #webview
  webview_flutter_android: ^3.16.7 #解决安卓input='file'
  webview_flutter_wkwebview: 3.17.0 #解决ios视频内联播放 #3.17.0尝试解决ag点击闪退的问题[let result = listResponse[0] as! AuthenticationChallengeResponse]
  image: ^4.2.0 #Dart图像加载
  permission_handler: ^11.3.1 #权限
  auto_size_text: ^3.0.0 #自适应文字控件
  rxdart: ^0.27.7 # Rx
  r_dotted_line_border: ^0.1.1 #虚线边框
  url_launcher: ^6.3.1 #浏览器打开网页
  dotted_decoration: ^2.0.0 #虚线框
  path_provider: ^2.1.4 #文件系统
  draggable_float_widget: ^0.1.0 #拖拽按钮
  video_player: ^2.9.2 #视频播放
  video_player_android: ^2.4.12 #安卓
  flutter_widget_from_html: ^0.10.6
  video_player_web_hls: ^1.3.0 #video_player_web插件
  #  chewie: 1.8.5 #基于video_player封装的播放器
  flutter_html: ^3.0.0-beta.2 #加载html
  ntp: ^2.0.0 #ntp 用于获取当前时间
  timezone: ^0.9.4 #时区
  anydrawer: ^1.0.6 #侧边栏
  marquee_text: ^2.5.0+1 #文字滚动-跑马灯
  pointer_interceptor: ^0.10.1+2 #处理iframe手势拦截
  connectivity_plus: ^6.1.3 #网络状态监听
  app_badge_plus: ^1.2.0 #app 角标
  flutter_inner_shadow: ^0.0.1 #内阴影
  flutter_native_splash: 2.4.4

  #  tencent_im_sdk_plugin_web: ^0.3.26 #腾讯im for web增补插件
  openinstall_flutter_plugin: ^2.5.2 #openinstall app分发统计
  text_scroll: ^0.2.0 #文字滚动 用于充值渠道
  qr_flutter: ^4.1.0 #生成二维码
  #  image_gallery_saver: ^2.0.3 #保存图片至相册
  image_gallery_saver:
    git:
      url: https://github.com/hui-z/image_gallery_saver
      ref: master
  visibility_detector: ^0.4.0+2 #视图可见
  media_kit: ^1.1.11 # 视频播放
  media_kit_video: ^1.2.5 #media_kit 的子库，专门针对视频播放和处理功能进行优化。它提供了更细化的控制和优化，专注于视频的播放、控制、渲染和处理。
  media_kit_libs_video: ^1.0.5 # Native video dependencies.
  like_button: ^2.0.5 #视频点赞按钮
  anchor_scroll_controller: ^0.4.4 #listView滚动到指定index
  collection: ^1.18.0
  loading_animation_widget: ^1.3.0
  uuid: ^4.5.1 #设备唯一编码
  lunar: ^1.7.0 #农历
  memory_info: ^0.0.4
  flutter_aliplayer: ^6.19.1 #阿里视频播放器，仅用于iOS、Android
  #  ve_vod: ^1.44.3-3 #字节播放sdk
  captcha_plugin_flutter: ^1.1.3 #网易验证码
  flutter_keyboard_visibility: ^6.0.0 #解决键盘遮挡
  encrypt: ^5.0.3 #加解密
  basic_utils: ^5.7.0
  file_picker: ^8.1.4 #文件选择器
  wechat_assets_picker: ^9.4.2 #图片视频选择器
  wechat_camera_picker: ^4.3.6 #相机
  device_info_plus: ^10.1.2 #设备信息
  flutter_plugin_engagelab: 1.2.4 #engagelab推送
  shelf_static: ^1.1.3
  sticky_and_expandable_list: ^1.1.3 #悬浮表头，用于聊天联系人页

  #  flutter_sticky_header: ^0.7.0 #悬浮表头，用于聊天联系人页
  firebase_core: ^3.12.1 #google firebase
  firebase_crashlytics: ^4.3.4 #闪退收集
  scrollable_positioned_list: ^0.2.3
  flutter_avif: ^3.0.0 #图片avif


  #  CHAT DEPENDENCIES
  tencent_cloud_chat_uikit:
    path: packages/tencent/tencent_cloud_chat_uikit-3.1.0+1
  tim_ui_kit_sticker_plugin:
    path: packages/tencent/tim_ui_kit_sticker_plugin-3.2.0
  tencent_chat_i18n_tool: ^2.3.8
  tencent_cloud_chat_intl: ^2.1.0


  #  VERSION UPGRADES FOR CHAT
  package_info_plus: ^8.0.0
  image_picker: ^1.1.2 #相册
  flutter_cache_manager: ^3.3.1 # 图片缓存
  path: ^1.9.0
  flutter_staggered_animations: ^1.1.1
  flutter_animate: ^4.5.2
  animated_flip_counter: ^0.3.4
  open_file: ^3.5.10
  keyboard_avoider: ^0.2.0

#  super_player:
#    git:
#      url: https://github.com/LiteAVSDK/Player_Flutter
#      path: Flutter

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^3.0.0
  build_runner: ^2.4.11
  json_serializable: ^6.8.0

dependency_overrides:
  flutter_web_frame:
    path: packages/flutter_web_frame
  chewie:
    path: packages/chewie
  fading_edge_scrollview: ^4.1.1
  image_gallery_saver:
    git:
      url: https://github.com/knottx/image_gallery_saver.git
      ref: knottx-latest
  file: ^7.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/translations/
    - assets/json/
    - assets/images/
    - assets/images/ali_animal_game/
    - assets/images/alert/
    - assets/images/activity/
    - assets/images/activity/task/
    - assets/images/common/
    - assets/images/login/
    - assets/images/toolBar/
    - assets/images/mine/
    - assets/images/check_in/
    - assets/images/mine/v2/
    - assets/images/mine/wallet/
    - assets/images/mine/vip/
    - assets/images/mine/tutorial/
    - assets/images/mine/course_tutorial/
    - assets/images/mine/video_coupon/
    - assets/images/mine/recruitment/
    - assets/images/avatars/
    - assets/images/channel/
    - assets/images/order/
    - assets/images/settings/
    - assets/images/drawer/
    - assets/images/navigation_bar/
    - assets/images/home/
    - assets/images/home/<USER>/
    - assets/images/home/<USER>/
    - assets/images/home/<USER>/
    - assets/images/dialog/
    - assets/images/transact/
    - assets/images/logo/
    - assets/images/chat/
    - assets/images/chat/ui/
    - assets/images/welcome/
    - assets/images/tiktok/
    - assets/images/share/
    - assets/images/button/
    - assets/lottie/
    - assets/html/
    - assets/html/tutorials/withdrawal/
    - assets/html/tutorials/recharge/
    - assets/html/tutorials/transfer/
    - assets/html/tutorials/course/
    - assets/html/captcha/
    - assets/html/about/
    - assets/html/video/
    - assets/video/
    - assets/images/gif/
    - assets/images/mine/promotion_rewards/
    - shorebird.yaml
  fonts:
    - family: DINCond-Bold
      fonts:
        - asset: assets/fonts/DINCond-Bold.ttf
    - family: Impact
      fonts:
        - asset: assets/fonts/Impact.ttf
    - family: AoboshiOne
      fonts:
        - asset: assets/fonts/AoboshiOne-Regular.ttf
    - family: FZYONGKTJ
      fonts:
        - asset: assets/fonts/FZYONGKTJ-subset.ttf
