import 'package:flutter/material.dart';
import 'package:game_store/core/utils/font_size.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:game_store/shared/widgets/delay_button.dart';
import 'package:game_store/core/utils/screenUtil.dart';

class NetErrorWidget extends StatelessWidget {
  final String? title;
  final Function refreshMethod;

  const NetErrorWidget({super.key, this.title, required this.refreshMethod});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 1.sw,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 160.gw,
            height: 160.gw,
            child: Image.asset('assets/images/common/no_data_Normal.png'),
          ),
          Text(
            title ?? "网络错误",
            style: TextStyle(fontSize: 14.fs, color: const Color(0xFF999999)),
          ),
          DelayButton(
              width: 100.gw,
              height: 34.gw,
              alignment: Alignment.center,
              margin: EdgeInsets.only(top: 40.gw),
              padding: EdgeInsets.symmetric(
                horizontal: 18.gw,
              ),
              onTap: () {
                refreshMethod();
              },
              decoration: BoxDecoration(color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(17.gw)),
              mainWidget: Text(
                '重新加载',
                style: TextStyle(fontSize: 15.fs, color: Colors.white, fontWeight: FontWeight.w600),
              ))
        ],
      ),
    );
  }
}
