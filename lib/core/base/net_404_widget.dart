import 'package:flutter/material.dart';
import 'package:game_store/core/utils/font_size.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:game_store/core/utils/screenUtil.dart';

class Net404Widget extends StatelessWidget {
  final String title;
  const Net404Widget({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 1.sw,
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.only(top: 117.gw, bottom: 10.gw),
            width: 160.gw,
            height: 160.gw,
            child: Image.asset('assets/images/common/no_data_Normal.png'),
          ),
          Text(
            title,
            style: TextStyle(fontSize: 14.fs, color: const Color(0xFF999999)),
          ),
        ],
      ),
    );
  }
}
