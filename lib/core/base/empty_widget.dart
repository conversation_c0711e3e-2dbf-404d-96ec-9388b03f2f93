import 'package:flutter/material.dart';
import 'package:game_store/core/utils/screenUtil.dart';

class EmptyWidget extends StatelessWidget {
  final String title;

  const EmptyWidget({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 130.gw,
            height: 130.gw,
            child: Image.asset('assets/images/common/no_data_Normal.png'),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
