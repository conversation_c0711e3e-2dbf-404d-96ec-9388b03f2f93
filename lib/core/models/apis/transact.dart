import 'package:game_store/core/models/entities/commit_top_up_entity.dart';
import 'package:game_store/core/models/entities/jump_chat_pay_entity.dart';
import 'package:game_store/core/models/entities/third_party_wallet_info_entity.dart';
import 'package:game_store/core/models/entities/top_up_list_entity.dart';
import 'package:game_store/core/models/entities/top_up_record_entity.dart';
import 'package:game_store/core/models/entities/withdraw_record_entity.dart';
import 'package:game_store/core/models/entities/withdraw_user_bank_list_entity.dart';
import 'package:game_store/core/utils/http/https.dart';

enum TransactType { topUp, withdraw, statement } // topUp充值，withdraw提现, statement账变

enum WithdrawType {
  bankCard, // type = 1
  wallet, // type = 2
  manualChannel, // type = 3 人工通道
}

class TransactApi {
  /// top up list 获取充值线路列表
  static Future<List<TopUpListEntity>> fetchOnlinePaymentList() async {
    ResponseModel response = await Http().request<TopUpListEntityList>(ApiConstants.onlinePaymentList);
    if (response.isSuccess && response.data != null) {
      return response.data.list;
    } else {
      return [];
    }
  }

  /// top up submit 提交充值
  static Future<CommitTopUpEntity?> submitPayment({
    required int channelId,
    required double amount,
  }) async {
    ResponseModel response = await Http().request<CommitTopUpEntity>(ApiConstants.submitPayment, params: {
      "channelId": channelId,
      "amount": amount,
    });
    if (response.isSuccess && response.data != null) {
      return response.data;
    } else {
      return null;
    }
  }

  /// top up submit 提交线下充值
  static Future<JumpChatPayEntity?> submitOfflineOrder({
    bool isTopUp = true,
    required int channelId,
    required num amount,
    String? orderNo,
    String? fundPwd,
    int? userBankInfoId,
  }) async {
    ResponseModel response = await Http().request<JumpChatPayEntity>(ApiConstants.submitOfflineOrder, params: {
      "transactionType": isTopUp ? "1" : "2", // 1充值 2提现
      "channelId": channelId,
      "amount": amount,
      if (fundPwd != null) "fundPwd": fundPwd,
      if (orderNo != null) "orderNo": orderNo,
      if (userBankInfoId != null) "userBankInfoId": userBankInfoId,
    });
    if (response.isSuccess && response.data != null) {
      return response.data;
    } else {
      return null;
    }
  }

  /// 获取充值记录
  static Future<TopUpRecordEntity?> fetchTopUpHistoryList({
    required int pageNo,
    required String searchDate,
    required String endDate,
    required String wayCode,
    required String typeCode,
    String? userNo,
  }) async {
    ResponseModel response = await Http().request<TopUpRecordEntity>(ApiConstants.userTopUpHistoryList, params: {
      "pageNo": pageNo,
      "pageSize": 20,
      "startDate": searchDate,
      "endDate": endDate,
      "wayCode": wayCode,
      "typeCode": typeCode,
      if (userNo != null) "userNo": userNo,
    });
    if (response.isSuccess && response.data != null) {
      return response.data;
    } else {
      return null;
    }
  }

  /// 三方钱包信息，余额
  static Future<ThirdPartyWalletInfoEntity?> fetchThirdPartyWalletBalance({required int channelId}) async {
    ResponseModel response = await Http().request<ThirdPartyWalletInfoEntity>(
      ApiConstants.thirdPartyWalletBalance,
      params: {
        "channelId": channelId,
      },
      isFormUrlEncoded: true,
      needShowToast: false,
    );
    if (response.isSuccess && response.data != null) {
      return response.data;
    }
    return null;
  }

  /// 三方钱包大厅链接
  static Future<String?> fetchThirdPartyWalletLobbyLink({required int channelId}) async {
    ResponseModel response = await Http().request(
      ApiConstants.thirdPartyWalletLobbyLink,
      params: {
        "channelId": channelId,
      },
      isFormUrlEncoded: true,
      needShowToast: false,
    );
    if (response.isSuccess && response.data != null) {
      final Map<String, dynamic> data = response.data;
      if (data['status'] == true && data['content'] != null) {
        return data['content'];
      }
    }
    return null;
  }

  /// 三方钱包 充值
  static Future<bool> fetchThirdPartyWalletSubmitCharge({
    required int channelId,
    required double amount,
    required String password,
  }) async {
    ResponseModel response = await Http().request(
      ApiConstants.thirdPartyWalletSubmitCharge,
      params: {
        "channelId": channelId,
        'amount': amount,
        'password': password,
      },
    );
    return response.isSuccess;
  }

  /// ---------------------------- Withdraw 提现 ----------------------------

  /// 获取用户银行卡列表, type 1:银行卡  2:钱包
  static Future<List<WithdrawUserBankBrief>?> fetchCashOutUserBankList(int type, {int? wldType}) async {
    ResponseModel response = await Http().request<WithdrawUserBankBriefList>(
      ApiConstants.cashOutUserBankInfoList,
      params: {
        'type': type,
        if (wldType != null) 'wldType': wldType,
      },
      isFormUrlEncoded: true,
    );
    if (response.isSuccess) {
      return response.data.list;
    } else {
      return null;
    }
  }

  /// 获取提现-人工通道 走客服聊天
  static Future<List<WithdrawManualChannelEntity>?> fetchCashOutManualChannelList() async {
    ResponseModel res = await Http().request<WithdrawManualChannelList>(
      ApiConstants.cashOutManualChannelList,
      
      isFormUrlEncoded: true,
    );
    if (res.isSuccess && res.data != null) {
      return res.data.list;
    } else {
      return null;
    }
  }

  /// 获取是否可提现
  static Future<WithdrawStatusEntity> fetchCashOutAvailable() async {
    ResponseModel response =
        await Http().request<WithdrawStatusEntity>(ApiConstants.cashOutAvailable, needShowToast: false);
    if (response.isSuccess && response.data != null) {
      return WithdrawStatusEntity()
        ..status = response.data.status
        ..errorMessage = response.data.errorMessage
        ..statusCode = response.code;
    } else if (response.code == "999") {
      return WithdrawStatusEntity()
        ..errorMessage = response.msg
        ..statusCode = response.code;
    } else {
      return WithdrawStatusEntity();
    }
  }

  /// 获取提现用户银行卡详情
  static Future<WithdrawUserBankInfoEntity?> fetchCashOutUserBankInfo({int? userBankInfoId}) async {
    ResponseModel response =
        await Http().request<WithdrawUserBankInfoEntity>(ApiConstants.cashOutUserBankInfo, params: {
      if (userBankInfoId != null) "userBankInfoId": userBankInfoId,
      "isOffline": userBankInfoId == null,
      },
      isFormUrlEncoded: true,);
    if (response.isSuccess && response.data != null) {
      return response.data;
    } else {
      return null;
    }
  }

  /// 提交银行卡提现申请
  static Future<bool> submitCashOutByBank({
    required double amount,
    required String fundPwd,
    required int userBankInfoId,
  }) async {
    ResponseModel response = await Http().request(ApiConstants.cashOutSubmitByBank,
        params: {"amount": amount, "fundPwd": fundPwd, "userBankInfoId": userBankInfoId});
    return response.isSuccess;
  }

  /// 提交虚拟货币提现申请
  static Future<bool> submitCashOutByVirtualCurrency() async {
    ResponseModel response = await Http().request(ApiConstants.cashOutSubmitByVirtualCurrency);
    return response.isSuccess;
  }

  /// 获取提现记录列表
  static Future<WithdrawRecordEntity?> fetchWithdrawHistoryList({
    int pageNo = 1,
    required String searchDate,
    required String endDate,
    String wayCode = '',
    String typeCode = '',
    String? userNo,
    bool checkUnread = false,
  }) async {
    ResponseModel response = await Http().request<WithdrawRecordEntity>(ApiConstants.userWithdrawHistoryList, params: {
      "pageNo": pageNo,
      "pageSize": 20,
      "startDate": searchDate,
      "endDate": endDate,
      "wayCode": wayCode,
      "typeCode": typeCode,
      if (checkUnread) "readStatus": 1,
      if (userNo != null) "userNo": userNo,
    });
    if (response.isSuccess && response.data != null) {
      return response.data;
    } else {
      return null;
    }
  }

  // 标记提现记录已读
  static Future<bool> operateWithdrawRecordRead(orderNo) async {
    ResponseModel response =
        await Http().request(ApiConstants.operateWithdrawRecordRead, params: {"transactionNo": orderNo});
    return response.isSuccess;
  }

  /// 充值/提现记录筛选-方式列表
  static Future<List<TransactFilterWay>> fetchTransactFilterWay({
    required TransactType type,
  }) async {
    var url =
        type == TransactType.topUp ? ApiConstants.userTopUpHistoryFilterWay : ApiConstants.userWithdrawHistoryFilterWay;
    ResponseModel response = await Http().request<TransactFilterWayList>(url);
    if (response.isSuccess && response.data != null) {
      return response.data.list;
    } else {
      return [];
    }
  }

  /// 充值/提现记录筛选-类型列表
  static Future<List<TransactFilterType>> fetchTransactFilterType({required TransactType type}) async {
    var url = type == TransactType.topUp
        ? ApiConstants.userTopUpHistoryFilterType
        : ApiConstants.userWithdrawHistoryFilterType;
    ResponseModel response = await Http().request<TransactFilterTypeList>(url);
    if (response.isSuccess && response.data != null) {
      return response.data.list;
    } else {
      return [];
    }
  }
}
