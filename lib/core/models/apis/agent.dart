import 'package:game_store/core/models/entities/commission_record_entity.dart';
import 'package:game_store/core/models/entities/invite_code_entity.dart';
import 'package:game_store/core/utils/http/https.dart';

import '../entities/customer_service_config_entity.dart';
import '../entities/subordinate_info_entity.dart';

class AgentApi {
  static Future<InviteCodeEntity?> fetchInviteCodeInfo() async {
    ResponseModel response =
        await Http().request<InviteCodeEntity>(ApiConstants.inviteCodeInfo);
    if (response.isSuccess) {
      return response.data;
    } else {
      return null;
    }
  }

  static Future<SubordinateInfoEntity?> fetchSubordinateInfo() async {
    ResponseModel response = await Http().request<SubordinateInfoEntity>(
      ApiConstants.subordinateInfo,
    );
    if (response.isSuccess) {
      return response.data;
    }
    return null;
  }

  static Future<CommissionRecordEntity?> fetchCommissionRecords({
    required int pageNo,
    required int pageSize,
  }) async {
    ResponseModel response = await Http().request<CommissionRecordEntity>(
      ApiConstants.friendCommissionRecords,
      params: {
        'pageNo': pageNo,
        'pageSize': pageSize,
      },
    );
    if (response.isSuccess) {
      return response.data;
    }
    return null;
  }

  static Future<CustomerServiceConfigEntity?>
      fetchCustomerServiceConfig() async {
    ResponseModel response = await Http().request<CustomerServiceConfigEntity>(
        ApiConstants.customerServiceConfig,
        needSignIn: true);
    if (response.isSuccess) {
      return response.data;
    } else {
      return null;
    }
  }
}
