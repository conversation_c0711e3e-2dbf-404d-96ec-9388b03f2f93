import 'package:game_store/core/models/entities/vip_model_entity.dart';
import 'package:game_store/core/utils/http/https.dart';

class AccountManagementApi {
  static Future<List<VipModel>> fetchVipList() async {
    ResponseModel? res = await Http().request<VipModelListEntity>(
      ApiConstants.vipList,
    );
    if (res.isSuccess && res.data != null) {
      return res.data.list;
    } else {
      return [];
    }
  }

  // static Future<List<PromotionBannerList>> fetchBanner() async {
  //   ResponseModel? res = await Http().request<PromotionBannerEntity>(
  //     ///TODO: Implement API request
  //     method: HttpConfig.mock,
  //     'assets/json/promotion_banners.json',
  //   );
  //   if (res.code == "0" && res.data != null) {
  //     return res.data.list;
  //   } else {
  //     return [];
  //   }
  // }
}
