import 'package:dio/dio.dart';
import 'package:game_store/core/constants/constants.dart';
import 'package:game_store/core/models/entities/app_version_entity.dart';
import 'package:game_store/core/utils/http/https.dart';

class ConfigApi {
  /// 检查app版本
  static Future<AppVersionEntity?> checkAppVersion() async {
    ResponseModel? res = await Http().request<AppVersionEntity>(
      ApiConstants.checkAppVersion,
      needSignIn: false,
      needShowToast: false,
    );
    if (res.isSuccess) {
      return res.data;
    }
    return null;
  }

  /// Downloads a file from the given URL and saves it to the specified path
  /// Returns a Future that completes when the download is finished
  /// The onProgress callback provides download progress updates
  static Future<void> download({
    required String url,
    required String savePath,
    void Function(int progress)? onProgress,
    CancelToken? cancelToken,
  }) async {
    try {
      final response = await <PERSON>o().download(
        url,
        savePath,
        cancelToken: cancelToken,
        onReceiveProgress: (count, total) {
          if (total != -1) {
            final progress = (count / total * 100).toInt();
            onProgress?.call(progress);
          }
        },
      );

      if (response.statusCode != 200) {
        throw Exception('下载失败，状态码：${response.statusCode}');
      }

      final contentType = response.headers.value('content-type');
      if (contentType == null) {
        throw Exception('无法获取文件类型');
      }

      // 支持的文件类型列表
      final validTypes = [
        'application/vnd.android.package-archive', // APK
        'application/octet-stream',               // 通用二进制
        'application/zip',                        // ZIP
        'application/x-zip-compressed',           // ZIP
      ];

      if (!validTypes.contains(contentType.toLowerCase())) {
        throw Exception('不支持的文件类型：$contentType');
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.cancel) {
        throw Exception('下载已取消');
      }
      throw Exception('下载出错：${e.message}');
    } catch (e) {
      throw Exception('下载出错：$e');
    }
  }
}
