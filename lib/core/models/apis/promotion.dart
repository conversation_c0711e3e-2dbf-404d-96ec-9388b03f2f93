import 'package:game_store/core/models/entities/commission_overview_entity.dart';

import '../../utils/http/https.dart';
import '../entities/commission_bet_entity.dart';
import '../entities/commission_details_entity.dart';
import '../entities/commission_recharge_entity.dart';
import '../entities/my_team_entity.dart';
import '../entities/team_details_entity.dart';
import '../entities/team_entity.dart';
import '../entities/team_members_entity.dart';

enum TeamType {
  bet, //0
  recharge, //1
}

enum DayType {
  yesterday, // 1
  thisMonth, // 2
  allTime, // 3
}

class PromotionApi {
  /// fetch team level
  static Future<TeamEntity?> fetchTeam() async {
    ResponseModel response = await Http().request<TeamEntity>(
      ApiConstants.team,
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data;
      return model;
    } else {
      return null;
    }
  }

  /// fetch my team info
  static Future<MyTeamEntity?> fetchMyTeam() async {
    ResponseModel response = await Http().request<MyTeamEntity>(
      ApiConstants.myTeam,
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data;
      return model;
    } else {
      return null;
    }
  }

  /// fetch commission info
  static Future<CommissionOverviewEntity?> fetchCommission() async {
    ResponseModel response = await Http().request<CommissionOverviewEntity>(
      ApiConstants.commission,
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data;
      return model;
    } else {
      return null;
    }
  }

  /// fetch team member info
  static Future<TeamMembersEntity?> fetchTeamMemberInfo({
    required int pageNo,
    String? childUserId,
  }) async {
    final params = {
      "pageNo": pageNo,
      "pageSize": 20,
      "childUserId": childUserId,
    };
    ResponseModel response = await Http().request<TeamMembersEntity>(
      ApiConstants.myTeamMembers,
      params: _removeNullValues(params),
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data;
      return model;
    } else {
      return null;
    }
  }

  /// fetch my team detail info
  static Future<TeamDetailsEntity?> fetchMyTeamDetail({
    required int pageNo,
    required TeamType type,
    String? childUserId,
  }) async {
    final params = {
      "pageNo": pageNo,
      "pageSize": 20,
      "childUserId": childUserId,
      "type": type.index,
    };
    ResponseModel response = await Http().request<TeamDetailsEntity>(
      ApiConstants.myTeamDetail,
      params: _removeNullValues(params),
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data;
      return model;
    } else {
      return null;
    }
  }

  /// fetch commission detail info
  static Future<CommissionDetailsEntity?> fetchCommissionDetail({
    required TeamType type,
    required DayType dateType,
    required int pageNo,
    String? childUserId,
  }) async {
    final params = {
      "type": type.index,
      "dateType": dateType.index + 1,
      "pageNo": pageNo,
      "pageSize": 20,
      "childUserId": childUserId,
    };
    ResponseModel response = await Http().request<CommissionDetailsEntity>(
      ApiConstants.commissionDetail,
      params: _removeNullValues(params),
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data;
      return model;
    } else {
      return null;
    }
  }

  /// fetch commission plan
  static Future<CommissionRechargeEntity?> fetchCommissionPlan() async {
    ResponseModel response = await Http().request<CommissionRechargeEntity>(
      ApiConstants.promotionCashInConfig,
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data;
      return model;
    } else {
      return null;
    }
  }

  /// fetch promotion bet config
  static Future<CommissionBetEntity?> fetchPromotionBetConfig() async {
    ResponseModel response = await Http().request<CommissionBetEntity>(
      ApiConstants.promotionBetConfig,
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data;
      return model;
    } else {
      return null;
    }
  }

  static Future<bool?> commissionReceive() async {
    ResponseModel response = await Http().request<bool>(
      ApiConstants.commissionReceive,
    );
    if (response.isSuccess) {
      return true;
    } else {
      return false;
    }
  }

  /// 获取用户邀请链接
  static Future<String?> fetchUserInviteLink() async {
    ResponseModel response = await Http().request<String>(
      ApiConstants.userInviteLink,
      needShowToast: false,
    );
    if (response.isSuccess && response.data != null) {
      final model = response.data;
      return model;
    } else {
      return null;
    }
  }
}

Map<String, dynamic> _removeNullValues(Map<String, dynamic> input) {
  return Map.fromEntries(input.entries.where((e) => e.value != null));
}
