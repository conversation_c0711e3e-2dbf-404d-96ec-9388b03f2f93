import 'package:game_store/generated/json/base/json_field.dart';
import 'package:game_store/generated/json/customer_service_config_entity.g.dart';
import 'dart:convert';
export 'package:game_store/generated/json/customer_service_config_entity.g.dart';

@JsonSerializable()
class CustomerServiceConfigEntity {
	List<CustomerServiceConfigData>? list = [];

	CustomerServiceConfigEntity();

	factory CustomerServiceConfigEntity.fromJson(Map<String, dynamic> json) => $CustomerServiceConfigEntityFromJson(json);

	Map<String, dynamic> toJson() => $CustomerServiceConfigEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class CustomerServiceConfigData {
	int id = 0;
	String logo = '';
	String name = '';
	String description = '';
	String link = '';
	int type = 2; // 1內跳  2外跳

	CustomerServiceConfigData();

	factory CustomerServiceConfigData.fromJson(Map<String, dynamic> json) => $CustomerServiceConfigDataFromJson(json);

	Map<String, dynamic> toJson() => $CustomerServiceConfigDataToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}