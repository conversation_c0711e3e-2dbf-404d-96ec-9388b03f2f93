import 'package:equatable/equatable.dart';
import 'package:game_store/generated/json/base/json_field.dart';
import 'package:game_store/generated/json/video_list_entity.g.dart';
import 'dart:convert';
import 'package:logger/logger.dart';

final logger = Logger();

@JsonSerializable()
class VideoListEntity {
  late List<VideoListRecords> records = [];
  late int total = 0;
  late int size = 0;
  late int current = 0;
  late int pages = 0;

  VideoListEntity();

  factory VideoListEntity.fromJson(Map<String, dynamic> json) => $VideoListEntityFromJson(json);

  Map<String, dynamic> toJson() => $VideoListEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class VideoListRecords with EquatableMixin {
  late int id = 0;
  late String videoImage = '';
  late String videoTitle = '';
  late String videoTime = '';
  late String videoYear = '';
  late String videoCategory = '';
  late int videoType = 0;
  late String videoTags = '';
  late String videoCountry = '';
  late String videoClarity = '';
  late String videoBottomTag = '';
  dynamic playCount;
  late int hide = 0;
  dynamic createTime;

  VideoListRecords();

  factory VideoListRecords.fromJson(Map<String, dynamic> json) => $VideoListRecordsFromJson(json);

  Map<String, dynamic> toJson() => $VideoListRecordsToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  @override
  List<Object?> get props => [id];
}

@JsonSerializable()
class VideoDetailEntity {
  late int id = 0;
  late int videoId = 0;
  late String lineTitle = '';
  late String lineUrl = '';
  late String videoTags = '';
  late String videoTitle = '';
  late String videoCategory = '';
  late String videoYear = '';
  late int playCount = 0;
  late int likes = 0;
  late bool isLiked = false;
  late List<VideoListRecords> recommendList = [];

  VideoDetailEntity();

  factory VideoDetailEntity.fromJson(Map<String, dynamic> json) => $VideoDetailEntityFromJson(json);

  Map<String, dynamic> toJson() => $VideoDetailEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
