import 'package:game_store/generated/json/base/json_field.dart';
import 'package:game_store/generated/json/winning_entity.g.dart';
import 'dart:convert';
export 'package:game_store/generated/json/winning_entity.g.dart';

@JsonSerializable()
class WinningEntityList {
  late List<WinningEntity> list = [];

  WinningEntityList();

  factory WinningEntityList.fromJson(Map<String, dynamic> json) => $WinningEntityListFromJson(json);

  Map<String, dynamic> toJson() => $WinningEntityListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class WinningEntity {
	String gameName = '';
	int profile = 0;
	String nickName = '';

	WinningEntity();

	factory WinningEntity.fromJson(Map<String, dynamic> json) => $WinningEntityFromJson(json);

	Map<String, dynamic> toJson() => $WinningEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}