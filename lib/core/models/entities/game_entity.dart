import 'package:equatable/equatable.dart';
import 'package:game_store/core/constants/constants.dart';
import 'package:game_store/core/models/entities/lottery_entity.dart';
import 'package:game_store/core/models/entities/popular_and_collection_game_entity.dart';
import 'package:game_store/generated/json/base/json_field.dart';
import 'package:game_store/generated/json/game_entity.g.dart';
import 'dart:convert';
export 'package:game_store/generated/json/game_entity.g.dart';

@JsonSerializable()
class GameListEntity {
  late List<PlatformInfo> thirdPlatformList = [];
  late List<GameType> gameList = [];

  GameListEntity();

  factory GameListEntity.fromJson(Map<String, dynamic> json) => $GameListEntityFromJson(json);

  Map<String, dynamic> toJson() => $GameListEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class PlatformInfo {
  late String platformName = '';
  late String platformCode = '';
  late String iconUrl = '';

  PlatformInfo();

  factory PlatformInfo.fromJson(Map<String, dynamic> json) => $PlatformInfoFromJson(json);

  Map<String, dynamic> toJson() => $PlatformInfoToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class GameType {
  late String name = '';
  late String code = '';
  late List<GamePlatform> data = [];
  late List extraList = [];
  late bool isHot = false;

  double sectionHeight = 0;

  GameType();

  factory GameType.fromJson(Map<String, dynamic> json) => $GameTypeFromJson(json);

  Map<String, dynamic> toJson() => $GameTypeToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class GamePlatform {
  late String name = '';
  late String platformCode = '';
  late bool isLogin = true;
  late String gameClassCode = '';
  late int gameType = 0; /// 1彩票 2三方游戏


  late String iconUrl = '';
  late List<Game> data = [];
  dynamic extraData;

  /// 用于处理自营彩票

  GamePlatform();

  factory GamePlatform.fromJson(Map<String, dynamic> json) => $GamePlatformFromJson(json);

  Map<String, dynamic> toJson() => $GamePlatformToJson(this);

  GamePlatform clone() {
    return GamePlatform()
      ..name = name
      ..platformCode = platformCode
      ..isLogin = isLogin
      ..iconUrl = iconUrl
      ..data = data.map((e) => e.clone()).toList()
      ..extraData = extraData;
  }

  factory GamePlatform.fromLotteryGroup(LotteryGroup group) {
    GamePlatform model = GamePlatform();
    model.name = group.groupName;
    model.platformCode = group.id.toString();
    model.iconUrl = getPlatformCGUrl(model.name);
    model.extraData = group;
    model.isLogin = group.isLogin;
    model.data = group.list.map((Lottery e) {
      Game model = Game();
      model.id = e.id;
      model.gameType = 1;
      model.gameName = e.lotteryName;
      model.gameClassCode = e.gameClassCode;
      model.gameCode = e.lotteryCode;
      model.iconUrl = e.imgUrl;
      model.categoryCode = e.gameCategoryCode;
      model.thirdPlatformId = group.id;
      return model;
    }).toList();
    return model;
  }

  factory GamePlatform.fromPopularVenue(PopularVenue model) {
    final platform = GamePlatform()
      ..name = model.name
      ..platformCode = model.gameType == 1 ? model.platformId.toString() : model.platformCode.toString()
      ..iconUrl = model.iconUrl
      ..isLogin = model.isLogin // 默认为true，表示需要登录
      ..gameType = model.gameType
      ..gameClassCode = model.gameType == 1 ? "CP" : model.gameClassCode
      ..data = [Game()..thirdPlatformId = model.platformId];

    return platform;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class Game extends Equatable {
  late int id = 0;
  late String gameClassCode = '';
  late String gameClassName = '';
  late String categoryCode = '';
  late String categoryName = '';
  late String gameCode = '';
  late String gameName = '';
  late int thirdPlatformId = 0;
  late String thirdPlatformName = '';
  late bool isDisplay = false;
  late bool isAegis = false;
  late int weight = 0;
  late String iconUrl = '';
  late bool savour = false;

  int gameType = 2; // 1.彩票 2.三方, 自定义字段，接口无返回

  Game();

  factory Game.fromJson(Map<String, dynamic> json) => $GameFromJson(json);

  factory Game.fromPopularGame(PopularGame model) {
    final game = Game()
      ..id = model.id
      ..gameType = model.gameType
      ..gameClassCode = model.gameType == 1 ? "CP" : model.gameClassCode
      ..gameClassName = model.gameClassName
      ..categoryCode = model.categoryCode
      ..gameCode = model.gameCode
      ..gameName = model.gameName
      ..thirdPlatformId = model.platformId
      ..thirdPlatformName = model.platformName
      ..iconUrl = model.iconUrl
      ..isDisplay = true // 默认值
      ..isAegis = false // 默认值
      ..weight = 0 // 默认值
      ..savour = model.savour; // 默认值

    return game;
  }

  Map<String, dynamic> toJson() => $GameToJson(this);

  Game clone() {
    return Game()
      ..id = id
      ..gameClassCode = gameClassCode
      ..gameClassName = gameClassName
      ..categoryCode = categoryCode
      ..categoryName = categoryName
      ..gameCode = gameCode
      ..gameName = gameName
      ..thirdPlatformId = thirdPlatformId
      ..thirdPlatformName = thirdPlatformName
      ..isDisplay = isDisplay
      ..isAegis = isAegis
      ..weight = weight
      ..iconUrl = iconUrl
      ..savour = savour
      ..gameType = gameType;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

  @override
  List<Object?> get props => [
        id,
        savour,
      ];
}
