import 'package:game_store/generated/json/base/json_field.dart';
import 'package:game_store/generated/json/withdraw_user_bank_list_entity.g.dart';
import 'dart:convert';
export 'package:game_store/generated/json/withdraw_user_bank_list_entity.g.dart';

@JsonSerializable()
class WithdrawUserBankBriefList {
	late List<WithdrawUserBankBrief> list = [];

	WithdrawUserBankBriefList();

	factory WithdrawUserBankBriefList.fromJson(Map<String, dynamic> json) => $WithdrawUserBankBriefListFromJson(json);

	Map<String, dynamic> toJson() => $WithdrawUserBankBriefListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class WithdrawUserBankBrief {
	late int id = 0;
	late int userId = 0;
	late String userNo = '';
	late String bankCode = '';
	late String bankName = '';
	dynamic province;
	dynamic city;
	late String branchBankAddress = '';
	late String realName = '';
	late String cardNo = '';
	late String createTime = '';
	late double withdrawnCount = 0;
	late int useStatus = 0; // 0.启用 1.停用
	dynamic mainNetwork;

	WithdrawUserBankBrief();

	factory WithdrawUserBankBrief.fromJson(Map<String, dynamic> json) => $WithdrawUserBankBriefFromJson(json);

	Map<String, dynamic> toJson() => $WithdrawUserBankBriefToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class WithdrawUserBankInfoEntity {
	late double fundAmount;
	late double withdrawalAmount;
	late String bankName = '';
	late String cardNo = '';
	late String bankCode = '';
	late int currentServiceChargeRate = 0;
	late int firstServiceChargeRate = 0;
	late int serviceChargeRate = 0;
	late int serviceChargeMaxLimit = 0;
	late int greaterThanEqualAmount = 0;
	late int thanServiceChargeRate = 0;
	late double amountMaxLimit = 0;
	late double amountMinLimit = 0;
	late int repeatedHours = 0;
	late int freeTimes = 0;
	late String remark = '';
	late double virtualCurrencyRate;
	late int userBankInfoId = 0;
	late int useStatus = 0; // 0.启用 1.停用
	late int type = 1;

	WithdrawUserBankInfoEntity();

	factory WithdrawUserBankInfoEntity.fromJson(Map<String, dynamic> json) =>
			$WithdrawUserBankInfoEntityFromJson(json);

	Map<String, dynamic> toJson() => $WithdrawUserBankInfoEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class WithdrawManualChannelList {
	late List<WithdrawManualChannelEntity> list = [];

	WithdrawManualChannelList();

	factory WithdrawManualChannelList.fromJson(Map<String, dynamic> json) => $WithdrawManualChannelListFromJson(json);

	Map<String, dynamic> toJson() => $WithdrawManualChannelListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class WithdrawManualChannelEntity {
	late String icon = '';
	late String channelName = '';
	late int channelId = 0;
  late double rate;

	WithdrawManualChannelEntity();

	factory WithdrawManualChannelEntity.fromJson(Map<String, dynamic> json) => $WithdrawManualChannelEntityFromJson(json);

	Map<String, dynamic> toJson() => $WithdrawManualChannelEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}