import 'package:game_store/generated/json/base/json_field.dart';
import 'package:game_store/generated/json/vip_model_entity.g.dart';
import 'dart:convert';
export 'package:game_store/generated/json/vip_model_entity.g.dart';

@JsonSerializable()
class VipModelListEntity {
  late List<VipModel> list = [];

  VipModelListEntity();

  factory VipModelListEntity.fromJson(Map<String, dynamic> json) => $VipModelListEntityFromJson(json);

  Map<String, dynamic> toJson() => $VipModelListEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class VipModel {
  late int id = 0;
  late int vipLevel = 0;
  late String vipTitle = '';
  late int growthIntegral = 0;
  late double upgradeReward;
  late double skipgradeReward;
  late String createTime = '';

  VipModel();

  factory VipModel.fromJson(Map<String, dynamic> json) => $VipModelFromJson(json);

  Map<String, dynamic> toJson() => $VipModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
