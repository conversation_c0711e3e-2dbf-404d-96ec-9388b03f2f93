
import 'package:dio/dio.dart';

import 'models/response_model.dart';

extension NetExceptionExt on NetException {
  ResponseModel toResponseModel<T>() {
    return ResponseModel<T>(
      data: null,
      code: code,
      msg: message,
    );
  }
}

/// 请求过程中抛出的异常
enum NetException {
  network(10000, '网络异常，请稍后重试！'),
  invalidUrl(10001, '请求地址异常'),
  server(10002, '服务器异常，请稍后重试！'),
  notFound(10003, '接口未找到'),
  unacceptableContentType(10004, 'Content-Type 异常'),
  unacceptableStatusCode(10005, '响应状态异常'),
  dataNotFound(10006, '无响应结果'),
  jsonSerialization(10007, 'JSON 序列化异常'),
  objectFailed(10008, '对象序列化异常'),
  illegal(10009, '缺省状态节点'),
  executeFail(10010, '请求执行失败');

  final int code;
  final String message;

  const NetException(this.code, this.message);
}


const _kEnableAesEncrypt = 'dio_enable_aes_encrypt';
extension RequestOptionsX on RequestOptions {
  bool get enableAesEncrypt => (extra[_kEnableAesEncrypt] as bool?) ?? false;
  set enableAesEncrypt(bool value) => extra[_kEnableAesEncrypt] = value;
}

extension OptionsEncrypt on Options {
  bool get enableAesEncrypt => (extra?[_kEnableAesEncrypt] as bool?) ?? false;

  set enableAesEncrypt(bool value) {
    extra = Map.of(extra ??= <String, dynamic>{});
    extra![_kEnableAesEncrypt] = value;
  }
}