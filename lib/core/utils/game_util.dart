import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:game_store/core/models/apis/channel.dart';
import 'package:game_store/core/models/apis/lottery.dart';
import 'package:game_store/core/models/entities/game_entity.dart';
import 'package:game_store/core/models/entities/lottery_entity.dart';
import 'package:game_store/core/models/entities/platform_amount_entity.dart';
import 'package:game_store/core/models/entities/popular_and_collection_game_entity.dart';
import 'package:game_store/core/singletons/user_cubit.dart';
import 'package:game_store/core/utils/auth_util.dart';
import 'package:game_store/core/utils/file_cache_manager/file_cache_manager.dart';
import 'package:game_store/core/utils/game_home_util.dart';
import 'package:game_store/core/utils/game_recently_util.dart';
import 'package:game_store/features/routers/app_router.dart';
import 'package:game_store/features/routers/navigator_utils.dart';
import 'package:game_store/injection_container.dart';
import 'package:game_store/shared/widgets/easy_loading.dart';

import '../constants/constants.dart';
import 'log_util.dart';

class GameUtil {
  static final notShowPlatformTab = ["SX", "TY", "DJ", "CP", "YLQP"];

  static final GameUtil _instance = GameUtil._internal();

  static List<int> favGameIdList = [];

  factory GameUtil() => _instance;

  GameUtil._internal();

  Map<String, String> platformInfoMap = {};

  List<GameType> gameList = [];
  List<LotteryGroup> lotteryGroupList = [];
  PopularAndCollectionGameEntity? popularEntity;

  Future<List<GameType>>? _fetchListFuture;

  /// 获取游戏列表
  Future<List<GameType>> fetchGameList() async {
    // 如果已经有一个未完成的 Future，则直接返回它
    if (_fetchListFuture != null) {
      return _fetchListFuture!;
    }

    try {
      _fetchListFuture = _fetchGameList();
      final result = await _fetchListFuture!;
      _fetchListFuture = null; // 清除缓存，允许后续调用重新请求
      return result; // 类型安全转换
    } catch (e) {
      _fetchListFuture = null; // 清除缓存，避免后续调用卡住
      // 打印错误日志，便于排查
      LogE("fetchGameList error: $e");
      rethrow; // 继续抛出异常
    }
  }

  Future<PopularAndCollectionGameEntity?> fetchPopularAndFavList() async {
    try {
      final res = await ChannelApi.fetchPopularAndFavGameList();

      favGameIdList = res?.userSavourGame.map((e) => e.id).toList() ?? [];

      /// 游戏列表匹配已收藏游戏
      for (final gameType in gameList) {
        for (final platform in gameType.data) {
          for (final game in platform.data) {
            game.savour = favGameIdList.contains(game.id);
          }
        }
      }

      /// 热门列表匹配已收藏游戏
      if (res != null) {
        /// 热门
        for (final popularGame in res.popularGame) {
          popularGame.savour = favGameIdList.contains(popularGame.id);
        }

        /// 最近
        for (final game in GameRecentlyUtil().gameList) {
          game.savour = favGameIdList.contains(game.id);
        }

        /// 收藏
        for (final game in res.userSavourGame) {
          game.savour = true;
        }
      }
      popularEntity = res;
      return res;
    } catch (_) {
      return null;
    }
  }

  Future<List<GameType>> _fetchGameList() async {
    // 获取游戏列表
    final gameListResult = await ChannelApi.fetchGameList();
    if (gameListResult.errorStr == null) {
      platformInfoMap = gameListResult.platformMap;
      List<GameType> newGames = List.from(gameListResult.game);

      for (GameType gameType in newGames) {
        if (gameType.code == 'YLCP' || gameType.code == 'CP') {
          final list = await fetchLotteryList();
          if (list.isNotEmpty) {
            gameType.data =
                list.map((e) => GamePlatform.fromLotteryGroup(e)).toList();
          }
        }

        /// 计算模块高度
        gameType.sectionHeight = GameHomeUtil.calculateSectionHeight(
            itemsLength: gameType.data.length, gameTypeCode: gameType.code);
      }
      gameList = List.of(newGames);

      try {
        final jsonString =
            json.encode(gameList.map((game) => game.toJson()).toList());
        FileCacheManager().saveCache(kHomeCacheKey, jsonString);
      } catch (e) {
        LogD("缓存HomeData失败: $e");
      }
      return newGames;
    }

    return [];
  }

  Future<List<LotteryGroup>> fetchLotteryList() async {
    final res = await LotteryApi.fetchHomeLotteryList();
    if (res.isNotEmpty) {
      lotteryGroupList = res;
    }
    return res;
  }

  Future<List<GameType>> getGameListCache() async {
    final res = await FileCacheManager().getCache(kHomeCacheKey);
    if (res != null) {
      try {
        final List<dynamic> jsonList = json.decode(res);
        gameList = jsonList.map((json) => GameType.fromJson(json)).toList();
      } catch (e) {
        LogD("获取缓存HomeData失败: $e");
      }
    }
    return gameList;
  }

  bool isFetchGameLogin = false;

  /// 获取游戏登录地址
  fetchGameLoginData({required int gameId, required int platformId}) async {
    if (isFetchGameLogin) return;
    isFetchGameLogin = true;
    GSEasyLoading.showLoading();
    try {
      // 获取游戏登录地址
      final result = await ChannelApi.fetchGameLoginUrl(
          gameId: gameId, thirdPlatformId: platformId);
      if (result != null) {
        switch (result.type) {
          case 1:
            GameUtil.openGameWebView(
                url: result.content, platformId: platformId);
            break;
          case 2:
            GameUtil.openGameHtmlView(
                content: result.content, platformId: platformId);
            break;
          default:
            break;
        }
        sl<UserCubit>().fetchUserBalance();
      }
    } finally {
      isFetchGameLogin = false;
      GSEasyLoading.dismiss();
    }
  }

  static openGameWebView({required String url, required int platformId}) async {
    await sl<NavigatorService>()
        .push(AppRouter.gameWebView, arguments: {'url': url});
    GameUtil.transferOutFromLoginPlatform(platformIds: [platformId]);
  }

  static openGameHtmlView(
      {required String content, required int platformId}) async {
    await sl<NavigatorService>()
        .push(AppRouter.commonHtmlView, arguments: {'content': content});
    GameUtil.transferOutFromLoginPlatform(platformIds: [platformId]);
  }

  /// 转出已登录三方平台余额
  static transferOutFromLoginPlatform({List<int>? platformIds}) async {
    if (!sl<UserCubit>().state.isLogin) return;
    var tmpList =
        platformIds ?? List<int>.from(sl<UserCubit>().loggedInPlatformIds);
    for (int e in tmpList) {
      if (!sl<UserCubit>().state.isLogin) break;
      double amount = await ChannelApi.getPlatformAmount(platformId: e) ?? 0;
      if (amount > 0) {
        bool flag = await ChannelApi.transferGameAmount(AmountTransferType.out,
            amount: amount, platformId: e);
        if (flag) {
          sl<UserCubit>().removePlatformId(e);
        }
      }
    }
    await sl<UserCubit>().fetchUserBalance();
  }

  /// 转出所有三方平台余额
  static transferOutAllPlatform() async {
    final list = await ChannelApi.getAllPlatformAmount();
    List<int> existAmountPlatformIds = [];
    for (PlatformAmountEntity model in list) {
      if (model.amount > 0) {
        existAmountPlatformIds.add(model.platformId);
      }
    }
    if (existAmountPlatformIds.isNotEmpty) {
      await GameUtil.transferOutFromLoginPlatform(
          platformIds: existAmountPlatformIds);
    }
    await sl<UserCubit>().fetchUserBalance();
  }

  /// 查找彩票信息
  Future<({LotteryGroup? group, Lottery? lottery})> _findLotteryInfo(
    GamePlatform? platform,
    String gameCode,
  ) async {
    // 获取彩票列表
    List<LotteryGroup> lotteryGroupList = GameUtil().lotteryGroupList;
    if (lotteryGroupList.isEmpty) {
      lotteryGroupList = await GameUtil().fetchLotteryList();
    }

    // 如果列表为空，直接返回空结果
    if (lotteryGroupList.isEmpty) {
      return (group: null, lottery: null);
    }

    // 从指定平台查找
    if (platform != null) {
      final group = platform.extraData as LotteryGroup;
      final lottery = group.list.firstWhereOrNull(
        (lottery) => lottery.lotteryCode == gameCode,
      );
      return (group: group, lottery: lottery);
    }

    // 从所有平台查找
    for (final group in lotteryGroupList) {
      final lottery = group.list.firstWhereOrNull(
        (lottery) => lottery.lotteryCode == gameCode,
      );
      if (lottery != null) {
        return (group: group, lottery: lottery);
      }
    }

    return (group: null, lottery: null);
  }

  /// 处理彩票跳转
  Future<void> _handleLotteryNavigation(
      Lottery lottery, LotteryGroup group) async {
    // 记录点击
    await LotteryApi.submitClickLottery(id: lottery.id, groupId: group.id);

    // 特殊彩票处理
    if (lottery.lotteryCode == 'DWYDH') {
      await sl<NavigatorService>()
          .push(AppRouter.animalGame, arguments: {'gameId': lottery.id});
      return;
    }

    // 普通彩票处理
    await sl<NavigatorService>().push(
      AppRouter.lotteryDetail,
      arguments: {
        'list': GameUtil().lotteryGroupList,
        'model': lottery,
      },
    );
  }

  /// 处理彩票游戏点击
  Future<void> handleLotteryGame(
      {GamePlatform? platform, required Game game}) async {
    try {
      final result = await _findLotteryInfo(platform, game.gameCode);

      if (result.lottery != null && result.group != null) {
        await _handleLotteryNavigation(result.lottery!, result.group!);
      } else {
        GSEasyLoading.showToast("数据异常,请重试");
      }
    } catch (e) {
      LogE("处理彩票游戏出错: $e");
      GSEasyLoading.showToast("操作失败,请重试");
    }
  }

  bool isHandleGameCellClick = false;

  onClickGameCell({required Game game, GamePlatform? platform}) async {
    if (isHandleGameCellClick) return;
    isHandleGameCellClick = true;
    try {
      if (game.gameType == 1) {
        await handleLotteryGame(platform: platform, game: game);
      } else {
        await fetchGameLoginData(
            gameId: game.id, platformId: game.thirdPlatformId);
      }
      GameRecentlyUtil().addRecentGame(game);
    } finally {
      isHandleGameCellClick = false;
    }
  }

  ({GameType? gameType, GamePlatform? platform}) _getGameTypeAndPlatform({
    required String platformCode,
    required int thirdPlatformId,
  }) {
    // 先找到目标平台
    for (GameType gameType in gameList) {
      final platform = gameType.data.firstWhereOrNull(
        (platform) {
          return platform.platformCode == platformCode;
        },
      );

      // 如果找到平台并且包含目标游戏，直接返回
      if (platform != null &&
          platform.data
              .any((game) => game.thirdPlatformId == thirdPlatformId)) {
        return (gameType: gameType, platform: platform);
      }
    }

    return (gameType: null, platform: null);
  }

  void onClickPlatformCellByGameType({
    required String gameTypeCode,
    required String platformCode,
  }) {
    AuthUtil.checkIfLogin(() {
      GameType? gameType =
          gameList.firstWhereOrNull((e) => e.code == gameTypeCode);
      if (gameType == null) {
        GSEasyLoading.showToast("解析游戏分类失败");
      }
      final platform = gameType!.data.firstWhereOrNull(
        (platform) {
          return platform.platformCode == platformCode;
        },
      );

      if (platform == null) {
        GSEasyLoading.showToast("数据异常,请重试");
      }

      GameRecentlyUtil().addRecentPlatform(platform!);
      onClickPlatformCell(gameType: gameType, platform: platform);
    });
  }

  void onClickPlatformCellBy({
    required String platformCode,
    required int thirdPlatformId,
  }) {
    AuthUtil.checkIfLogin(() {
      LogD(
          "onClickPlatformCellBy...platformCode: $platformCode,  thirdPlatformId: $thirdPlatformId");
      final result = _getGameTypeAndPlatform(
          platformCode: platformCode, thirdPlatformId: thirdPlatformId);

      if (result.gameType == null || result.platform == null) {
        GSEasyLoading.showToast("数据异常,请重试");
        return;
      }
      GameRecentlyUtil().addRecentPlatform(result.platform!);
      onClickPlatformCell(
          gameType: result.gameType!, platform: result.platform!);
    });
  }

  /// 点击场馆cell
  onClickPlatformCell(
      {required GameType gameType, required GamePlatform platform}) {
    LogD(
        "onClickPlatformCell >>> gameType：$gameType, result.platform>>>> ${platform.toJson()}");
    AuthUtil.checkIfLogin(() {
      if (GameUtil.notShowPlatformTab.contains(gameType.code) &&
          platform.data.isNotEmpty) {
        final model = platform.data.first;
        GameUtil().fetchGameLoginData(
            gameId: model.id, platformId: model.thirdPlatformId);
      } else {
        sl<NavigatorService>().push(AppRouter.gameList,
            arguments: {"gameType": gameType, "gamePlatform": platform});
      }
      GameRecentlyUtil().addRecentPlatform(platform);
    });
  }

  /// 点击游戏收藏
  Future<bool> onClickGameFav({required bool isFav, required Game game}) async {
    LogD("onClickGameFav>>>isFav: $isFav");
    final flag = await ChannelApi.operateGameFav(
        isFav: isFav, gameId: game.id, gameType: game.gameType);
    if (flag) {
      if (isFav) {
        favGameIdList.add(game.id);
      } else {
        favGameIdList.remove(game.id);
      }
    }
    return flag;
  }

  PlatformCell getPlatformCellData(String gameCode) {
    if (kChannel != 'YL') {
      return PlatformCell(
          bgImage: 'assets/images/home/<USER>',
          logoImage: 'assets/images/home/<USER>',
          textColor: const Color(0xff9f856d));
    }
    switch (gameCode) {
      case 'DZ':
        return PlatformCell(
            bgImage:
                'assets/images/home/<USER>',
            logoImage: 'assets/images/home/<USER>',
            textColor: const Color(0xffb78c55));

      case 'YLCP':
        return PlatformCell(
            bgImage:
                'assets/images/home/<USER>',
            logoImage: 'assets/images/home/<USER>',
            textColor: const Color(0xff907cb0));
      case 'QP':
        return PlatformCell(
            bgImage:
                'assets/images/home/<USER>',
            logoImage: 'assets/images/home/<USER>',
            textColor: const Color(0xffaf675e));
      case 'SX':
        return PlatformCell(
            bgImage:
                'assets/images/home/<USER>',
            logoImage: 'assets/images/home/<USER>',
            textColor: const Color(0xff9f856d));
      case 'TY':
        return PlatformCell(
            bgImage:
                'assets/images/home/<USER>',
            logoImage: 'assets/images/home/<USER>',
            textColor: const Color(0xff6492b6));
      case 'BY':
        return PlatformCell(
            bgImage:
                'assets/images/home/<USER>',
            logoImage: 'assets/images/home/<USER>',
            textColor: const Color(0xff588cb4));
      default:
        return PlatformCell(
            bgImage: 'assets/images/home/<USER>',
            logoImage: 'assets/images/home/<USER>',
            textColor: const Color(0xff9f856d));
    }
  }
}

class PlatformCell {
  final String bgImage;
  final String logoImage;
  final Color? textColor;

  PlatformCell(
      {required this.bgImage, required this.logoImage, this.textColor});

  factory PlatformCell.fromJson(Map<String, dynamic> json) {
    return PlatformCell(
        bgImage: json['bgImage'],
        logoImage: json['logoImage'],
        textColor: json['textColor']);
  }
}
