{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983e28bde9831873aa0cb6f16015f308f7", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989661a8d0b3dabcf8108c02a9983b675c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9863f0acca2af88b9b3dc710812cb688b5", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dfa862461e744a2b292ae2ef5987159f", "name": "Debug-YL"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9863f0acca2af88b9b3dc710812cb688b5", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a046df7b04ba0121d380bbdce9bf414b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9863f0acca2af88b9b3dc710812cb688b5", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989353823e4c3a8327292245394a6a91cb", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9863f0acca2af88b9b3dc710812cb688b5", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9892dcb67492274bd06bc4e873cb4cdd7d", "name": "Release-JS"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9863f0acca2af88b9b3dc710812cb688b5", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981a5558783da2c92865c59c1116cc64d4", "name": "Release-JS2"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9863f0acca2af88b9b3dc710812cb688b5", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9838ab224c88b22384b3cf4013471dc82c", "name": "Release-YL"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac46504455f87ae035da69f7850ce8cc", "guid": "bfdfe7dc352907fc980b868725387e9898c08869d99f76fb0da8e84603ebe559"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc0d0a1b9eb9c99988c292beb884c670", "guid": "bfdfe7dc352907fc980b868725387e98accede2415903f399a8a1c0e21da40f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd779870aaa75484ef8c0bc2fc7eab0a", "guid": "bfdfe7dc352907fc980b868725387e98a1877600fa7d44ee281407da74cb39af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b2d37a5ec6c3e02859db5b7283c010d", "guid": "bfdfe7dc352907fc980b868725387e98c381bea9c25d9e139d8c8dffe4f24400"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa14b76c8bb8ddfb1ae67833f1df4c08", "guid": "bfdfe7dc352907fc980b868725387e98f8b02a03eb35cd5acf81150af190b956"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873de6f859ab4cfff07c1e1c0c89da765", "guid": "bfdfe7dc352907fc980b868725387e983df258151d81a2dbfc63be54538557b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a575abb8c3e189b3ad6d94bef85bc6d", "guid": "bfdfe7dc352907fc980b868725387e98da13bb32126abd6804dd4797924df6b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896f076dde1bc8343a04207740cc0bc50", "guid": "bfdfe7dc352907fc980b868725387e98cd7bf1aae83b4c30fc3ee556352b2bc4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3e849e6eeab1787f3ccb2d6398e3e41", "guid": "bfdfe7dc352907fc980b868725387e98bfc69727d478b7c5ae13d8f43f20e7ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98193b8b3d7c7824ccfcca1f9af04afac3", "guid": "bfdfe7dc352907fc980b868725387e9841c27592d5d7fefd6a255af5613c4089"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f336d0436390b50590583869fa066541", "guid": "bfdfe7dc352907fc980b868725387e9850d2843f52e9dd2b452deb4e2731f2f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e4ee882b5ceea353ebc28334f35149a", "guid": "bfdfe7dc352907fc980b868725387e986a3b6d9f39fbbdf09652a32aa4d2a63b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e8886d644fa6e57bcad3a02f1eb66f8", "guid": "bfdfe7dc352907fc980b868725387e98bf386174af880efe3e75fa4b42b7a0ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982abac6e39c28f09ce2c4f9e02cef0579", "guid": "bfdfe7dc352907fc980b868725387e98b963e24ad07d2dc692152946d6b2d90f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f96efb63829b6b2099f9d7696d4fdd7a", "guid": "bfdfe7dc352907fc980b868725387e984ba3f5909f164cdca266a369bc2b0d21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cc8cc5240eecda0c590023bba46d568", "guid": "bfdfe7dc352907fc980b868725387e984c0f512596cd4d1f939684353f93cb71", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afc24957d20198225bb4ea6334ef3bf0", "guid": "bfdfe7dc352907fc980b868725387e9812bc838ec6ea367ed35d94bf588beb88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e5f90e3dc86211e9c540cd82116bbf3", "guid": "bfdfe7dc352907fc980b868725387e9829506e1298259e957b213cca10bed509"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b79c01391ada881dd2d61a3b4635305b", "guid": "bfdfe7dc352907fc980b868725387e9861bcd3f1764460dc2680846b92601a2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a2c0e4a136dce73f444e75e0c09a2de", "guid": "bfdfe7dc352907fc980b868725387e98d3e4774e5e44c73267306151f516f5c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2bbca392a283e255b23b8d9767bcd7b", "guid": "bfdfe7dc352907fc980b868725387e98fe514f14c92d162e064a710f6d308b5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885cb8dc34d78ec7a4df66a01ec6a8cc8", "guid": "bfdfe7dc352907fc980b868725387e98929b5ee258864a070dae600ab2f0b771"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894dbd840bbe2fb9f4ada6744e942efdd", "guid": "bfdfe7dc352907fc980b868725387e986a82f57ad9cde8edf92a99890a9c1fc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e42f0c686eda766d9e71e5cada05fc77", "guid": "bfdfe7dc352907fc980b868725387e98908b9930fe7d899131f5ff9d885814f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ef2ee0fba319e173d5c5e6907e33e26", "guid": "bfdfe7dc352907fc980b868725387e989ad0ffd8f60cbb62ec3667b21e49f39c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb96e38184bd1306289ebaffcd659751", "guid": "bfdfe7dc352907fc980b868725387e98cdeefb0650d648c2caa02234d6cb6328"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a587bd110be7bc2ac807734a28e4a24", "guid": "bfdfe7dc352907fc980b868725387e982efd079d8d733f4ee8ca624a4e9043e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838620594c0d781e73f0c85b649b77af6", "guid": "bfdfe7dc352907fc980b868725387e986aeba560406b29c3c23e5f60df5fa480"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fd935826a73cbb926dc3599c7493457", "guid": "bfdfe7dc352907fc980b868725387e9815d73b7faf858c4d7f7c32f06e1bff16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98613e64494ca3c7682e319ce5473398f0", "guid": "bfdfe7dc352907fc980b868725387e98341993cf587ac83447749811426d558e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bc89aed9254e4149ebd51695d45528d", "guid": "bfdfe7dc352907fc980b868725387e98cf937c00403cb9a1e131baeb36a5f075"}], "guid": "bfdfe7dc352907fc980b868725387e98dc9402cbb74bad17f9ac1841768b59fd", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98209c5e28e5fdb8a4df24712fac4362e0", "guid": "bfdfe7dc352907fc980b868725387e98facfb630ffc581f8086e6e99618fd9b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98009a3db3a647340fcef436171a1fe3df", "guid": "bfdfe7dc352907fc980b868725387e986aa8199c62e00d83e00bd68b8d84b6d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a796cb666eaa08b847be350028f3509", "guid": "bfdfe7dc352907fc980b868725387e981b3d7ef10c1146016ac99346247f2011"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839df22d6b228c443d3f93f78689b356a", "guid": "bfdfe7dc352907fc980b868725387e9899314f91112fe15f50bd69d9f4cda324"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a99d8d20b329473e65ec6b2cdc4bf72a", "guid": "bfdfe7dc352907fc980b868725387e98b450da61ed6551df1c7206d198b2a3bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b25590fbd2866e8deb499423b5ba494", "guid": "bfdfe7dc352907fc980b868725387e982d72f9af1f5e66d22d8153be65c8283c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cdc79b786ec79ef44d88d5ef25cd049", "guid": "bfdfe7dc352907fc980b868725387e98d312ebf654a2dd3ca6daa519cbe247d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982805fdafe63da52739ed67a2a0bda247", "guid": "bfdfe7dc352907fc980b868725387e98c6d1a78d4a1fc5a00dc3e290b2bdc222"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ddd26ca020a9f3d5f26c6fda47f3141", "guid": "bfdfe7dc352907fc980b868725387e989509456aa84d49b8ed3fd02142f7d651"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f2f50227ea815fd3cbd06b600ee4de3", "guid": "bfdfe7dc352907fc980b868725387e98b87f1687436fea880d75b5411ecfc760"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6e738f65532e1624b31dfd2dbb79085", "guid": "bfdfe7dc352907fc980b868725387e98c3d7a687d8da7aac42e8cfd27a6e273d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98695e854fe121c6cbf93ab95b3cdc04fa", "guid": "bfdfe7dc352907fc980b868725387e981322070e90bd0620c764764c40909355"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98139fde59d8e2eed2efe343780ebec4a2", "guid": "bfdfe7dc352907fc980b868725387e989b6765f8cbf55012e27ae0b4f0d89d79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df07a1e1f4f456674b82448f599f6f5a", "guid": "bfdfe7dc352907fc980b868725387e98ef79e4bc957f5a4bc3bcf5e1e9f4f12b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1ecb3284bd9886096c36eb2ca9af446", "guid": "bfdfe7dc352907fc980b868725387e9841e22140ade2cbdc3658b32b5cdb7d6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847f2c2d40bc75c8b1991c5210f307127", "guid": "bfdfe7dc352907fc980b868725387e98b8694e94fb7ff126a335886cd17cc3af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0a1ce9b2bab45fa318e50455a6c0dc8", "guid": "bfdfe7dc352907fc980b868725387e980fad6a9429adf0ab49117037ac8fd7e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a1f5aed195c2a0e6692ada22f078c1f", "guid": "bfdfe7dc352907fc980b868725387e98fcb34961d5fff9515b20542607def2c3"}], "guid": "bfdfe7dc352907fc980b868725387e98f0c97c980a13bf99e86f0c5080aec21b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988338730910fc8b3bf8d9a66916e6c151", "guid": "bfdfe7dc352907fc980b868725387e98acb9d4ede5e9b1f3d2086a9be02fe72a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a5d76f350d9b3b98a3653f2a6736643", "guid": "bfdfe7dc352907fc980b868725387e983ab7c7901f3e9840be7626582b053fdf"}], "guid": "bfdfe7dc352907fc980b868725387e98986cc151593add88a332af5c46e66e5d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e980b22ca7159ef0bf8f25d2a22f0e86fbd", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e980c508740be2ca10a55e5dfac6147edac", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-YL", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS2", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-YL", "provisioningStyle": 1}], "type": "standard"}