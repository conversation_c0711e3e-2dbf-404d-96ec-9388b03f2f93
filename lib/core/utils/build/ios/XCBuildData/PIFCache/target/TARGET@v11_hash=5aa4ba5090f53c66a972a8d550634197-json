{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984596503e17232a990fee4f504f6f1240", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981bf0d139d89a1487e87f2cd2d1da5e98", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989362f0d0bf4f36145f99353d9e57e74f", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ce32c1d277710ef15b5a1a6a0fbf9886", "name": "Debug-YL"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989362f0d0bf4f36145f99353d9e57e74f", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bbdc1f8e5d72750c50c238827f4e9b00", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989362f0d0bf4f36145f99353d9e57e74f", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986b3ff4b00b6645995578381f9c781eaa", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989362f0d0bf4f36145f99353d9e57e74f", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98351de4c2e0263e6fffdd829bcba00938", "name": "Release-JS"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989362f0d0bf4f36145f99353d9e57e74f", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982650f14011c33f41d611346eb95e8018", "name": "Release-JS2"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989362f0d0bf4f36145f99353d9e57e74f", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98681fe7b981be50bc696f32a2721095eb", "name": "Release-YL"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e239bf6997d8949ccf11d8c4b6d2c3b3", "guid": "bfdfe7dc352907fc980b868725387e98e3ed99a87da10af5341fb8109d673965", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98069fd1c5fa552a6f6206d32bef3d7fbd", "guid": "bfdfe7dc352907fc980b868725387e98b1c337b0ca67d89ab42c8e3153d69821", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899d48ea543babc698fda41fc5a999cba", "guid": "bfdfe7dc352907fc980b868725387e986f96b1af0092d310a704823c5b4b4396", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c910d5aaa4b1dc2df614f793bc675f0", "guid": "bfdfe7dc352907fc980b868725387e984b9e9788f3bd544ec02ae13463a64a6b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895bfb8a6da4ea3a84893b2e25dec20d0", "guid": "bfdfe7dc352907fc980b868725387e983b4986219c98305bcb6f26b039dbb006", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856f11f8f50972e64f1277d7dc393b95d", "guid": "bfdfe7dc352907fc980b868725387e98c090973249d4879013151c061a754504", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c899759064d414a0e55b2f92d147f829", "guid": "bfdfe7dc352907fc980b868725387e98c8c4162f963fb79748fd9561b79f84f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98333e28f67d6eb70a347e92bef06d7180", "guid": "bfdfe7dc352907fc980b868725387e98b04cbd7c86ff54932876665b2eabd589", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd6bb593d085d97d5e09ee24eaa62172", "guid": "bfdfe7dc352907fc980b868725387e989fa966526e2ca0ed799aa8f6e3aaaae2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b3877af82f53ff95eab475399b148fd", "guid": "bfdfe7dc352907fc980b868725387e982104cf3af4712b4b96afdea67380fd4c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddfd4198d3046ae09255ceaf3440bc03", "guid": "bfdfe7dc352907fc980b868725387e9865709e240d7c7a086ae17b390b4527a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98672c900537859e2c84c33c9ff636b4ea", "guid": "bfdfe7dc352907fc980b868725387e98822dc36250cf762f0d4780271e200bd2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982136330fcd0dc2215730fa98e722c0a2", "guid": "bfdfe7dc352907fc980b868725387e98725ae500e2520f6735b9f91a011572e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875defc3d1a83bf70dbca6e736b08f0c2", "guid": "bfdfe7dc352907fc980b868725387e980e7b557e79584f556593374161a8b7ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b415eeae127c5b1cd0059a890d5dd499", "guid": "bfdfe7dc352907fc980b868725387e982281bb1930bf535b21365fb4fe674039", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2131a7707063a47da5e14e3816eabed", "guid": "bfdfe7dc352907fc980b868725387e98e01257102609f4a25ed4266448328572", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832722f2c7125325ef7132536d77b5124", "guid": "bfdfe7dc352907fc980b868725387e98ebf5f502bd10383e0f884a95c9520704", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e57ea292f31ef45e6a55b6ff3c21ce34", "guid": "bfdfe7dc352907fc980b868725387e98c3953df78a93577cbda8ce952f6ceb74", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98421c1cdd93b44b42129d6b333032e959", "guid": "bfdfe7dc352907fc980b868725387e98dfafce47ce72be0ff221301d09af8858", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826c7e1519436b54a222afae89b5104ad", "guid": "bfdfe7dc352907fc980b868725387e989cd4ffbbdc7b5b4574d2b57a5251e220", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896a3bd0cdeed78a84ff3bf27220deb36", "guid": "bfdfe7dc352907fc980b868725387e9822599c0bdc0be2a05233460d7c71bf07", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989eca0b5f2ef8dea23e90dae329f160af", "guid": "bfdfe7dc352907fc980b868725387e981e8869a4e82278353bae68fa4560d16b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98931304aef6ad9580a550fd3d3b0d6f27", "guid": "bfdfe7dc352907fc980b868725387e982e1330b2a8922561537ede235f4e0341", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875f86323a3d40a08c7148e32d67f34a7", "guid": "bfdfe7dc352907fc980b868725387e9810cd463bbcfa34c0e6411d56b6718f5e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982146f82d05cb9fe662b9551f33351276", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f74d0626c6e9d1ac503a90277751a2c8", "guid": "bfdfe7dc352907fc980b868725387e982161f3f5e996392a830ee55592f95596"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ebacd5d07d904a5c0b150f08400f522", "guid": "bfdfe7dc352907fc980b868725387e985293a61d76716bc77f2a40640d1fd9c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0707ca17b43e14d93d0025d91524cc1", "guid": "bfdfe7dc352907fc980b868725387e98ee10ab6be7c8494aa2e3844aadc7a3ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc9798161d987959912a8ff2c1c38514", "guid": "bfdfe7dc352907fc980b868725387e98180934e84f8a1b4468e086b75a49abb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d752412ef905faa72686ad72018a3803", "guid": "bfdfe7dc352907fc980b868725387e983eeda30d56691ab16857c571f1e54f51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c83cc238204eff4e780e592a84f16a44", "guid": "bfdfe7dc352907fc980b868725387e98d7bd507a54ee64d27417828352904ff6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822087ec3fab33872d2e7881f5845d69a", "guid": "bfdfe7dc352907fc980b868725387e982a342e144669fb79cbad3360b7ab4ed6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987efa96c3f5c72061c602c60b99f41fec", "guid": "bfdfe7dc352907fc980b868725387e98aa711e483ecb34fa1e5f9b55cd5a1070"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d7eafc461abd906a6f1dd9de648c8c9", "guid": "bfdfe7dc352907fc980b868725387e982c1013e120195619b2a91c5fd23feee9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98922b5373a32867a72b186ff5fbb5a97e", "guid": "bfdfe7dc352907fc980b868725387e98b0a0f4553610d672ffc3dae8ce131ee1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864687001de3f1a0d1459cf81b757335a", "guid": "bfdfe7dc352907fc980b868725387e984acf40f5fe31aab43ee0dd2832678aaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f28c6e655ef7682348ee6e2badf96cd", "guid": "bfdfe7dc352907fc980b868725387e9891309593a0c92de49a828a37b39b43d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd9fcd162f6d270ce9653d7aec39543d", "guid": "bfdfe7dc352907fc980b868725387e98507cbfdb3c8b82b836f207e3461b5ce8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98528079a3eb821642755826c2d7a808da", "guid": "bfdfe7dc352907fc980b868725387e98d6f07325227329ca5bc2d87dfcfe2574"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885a846f2c01a3b8089c5d0a41757af26", "guid": "bfdfe7dc352907fc980b868725387e98ada50361ffe2727446f8e368a69979b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866120cf64dee05563e4b7eecf129bb53", "guid": "bfdfe7dc352907fc980b868725387e9829f0c5fe524ffdfbc82b08a4063b5ef3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eb7b35410b60b03dcb7e88e3abdf3d2", "guid": "bfdfe7dc352907fc980b868725387e98417a18688999922e647a5fa876880b4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe0086814bb68900a2375f132d2813b6", "guid": "bfdfe7dc352907fc980b868725387e9828b7e9afedc947c5650639b8b620d992"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987508c97b0ef8484adf89c486f7678ee9", "guid": "bfdfe7dc352907fc980b868725387e9876887f7e8b6fb5960c2760b70f4d2ccd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f3fef06d3321ceaae8056fef9039e5f", "guid": "bfdfe7dc352907fc980b868725387e985be044b00902a0e0a9dc7ef55233daed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981af2cf0361830fb597cc16ddbe4b1bd4", "guid": "bfdfe7dc352907fc980b868725387e98e9a456e8c88f10e144098103157cdfd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aedbc6c3f8a7448ad3cecd49b5bd251a", "guid": "bfdfe7dc352907fc980b868725387e984f140a9c059253cb8d8b70fac70be126"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbd89fe7884ecc619532cb12975df25c", "guid": "bfdfe7dc352907fc980b868725387e98553b46ce56ba90d21ac35a6840aad7f5"}], "guid": "bfdfe7dc352907fc980b868725387e98312dbcdc8deecd7c81e131753184a3ec", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988338730910fc8b3bf8d9a66916e6c151", "guid": "bfdfe7dc352907fc980b868725387e985dabf8d6815ae93726b6f948086a2595"}], "guid": "bfdfe7dc352907fc980b868725387e98890bce1442046e691230ca134efb2b11", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98890e9fa3f0060aa65e2770c1600e4935", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e9862d8131a8ca30b5fe074405a5cd41021", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-YL", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS2", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-YL", "provisioningStyle": 1}], "type": "standard"}