{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b6af1a4ef57184961d5b05ef9a986088", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987c24baaa287129ad725a5e890967a9b3", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f7f41fd11f5b2093db700b92d6117f13", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9807a6bf99a73e301f163bf86dcb5a89b9", "name": "Debug-YL"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f7f41fd11f5b2093db700b92d6117f13", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98db4e774e9b01db6b0205aa88f5b81bda", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f7f41fd11f5b2093db700b92d6117f13", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9807770b3b77eed8a8a84888d5d3c02f9b", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f7f41fd11f5b2093db700b92d6117f13", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e312b1c483b565eeb51cc52fa6ab8624", "name": "Release-JS"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f7f41fd11f5b2093db700b92d6117f13", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f7d4831d6da928fa99b529420f27661e", "name": "Release-JS2"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f7f41fd11f5b2093db700b92d6117f13", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98eabde468cd92f0e14e91dbeeb1244e12", "name": "Release-YL"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98efda225799e8ec4936e0b5ddca24b816", "guid": "bfdfe7dc352907fc980b868725387e98f6ed99d44a0630301a2786a93d2fe064", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb8c09c5d8df70d3d137970182d647d0", "guid": "bfdfe7dc352907fc980b868725387e9886641b607db904f2f13fd09c21b0a244", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98040e5b98ef690bfe3fb324949ff0901b", "guid": "bfdfe7dc352907fc980b868725387e98fb4f84f4559c5cc08711b97ee19d87ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbc90a189e85e53b3c8b0b95a21e7212", "guid": "bfdfe7dc352907fc980b868725387e9886675d716042a9fc3319d243c3c49f0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ae241875b0d3164a633a221568716cc", "guid": "bfdfe7dc352907fc980b868725387e98803b5349725a911be5de3cfd55f85b8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810b44d8e63875a62ee29e925b381c9cb", "guid": "bfdfe7dc352907fc980b868725387e984be8c320ee30d468782c371cfe562494", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b404a04cec47c3b9d0e7cd209c7c1c5", "guid": "bfdfe7dc352907fc980b868725387e98236daa0445279d904e7423e3d396583a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805ec4d706404080957d2e3ab19fff9a6", "guid": "bfdfe7dc352907fc980b868725387e98ad35e5fb38789050165a05210a4387bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f12fb343d1b9137bb64abc0ece53ac06", "guid": "bfdfe7dc352907fc980b868725387e985e321617339b912a0963a2d20b4d4b7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98318b3765acebbafa69c1025cf85d6723", "guid": "bfdfe7dc352907fc980b868725387e98590d3425e351989b0f5ba57c923e776f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98375c7b91e9df1c34dd5afa5830e944f6", "guid": "bfdfe7dc352907fc980b868725387e987d83bc7f84338479c8d244a35e569c65", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfd395aa4248c27ec18fa7042378677d", "guid": "bfdfe7dc352907fc980b868725387e98b11bbca0693dbf94159023f010192962", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98beaa7ef2a8224c0d0830f51841216279", "guid": "bfdfe7dc352907fc980b868725387e9802a8ae85dd12210464b842a3a5825698", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3d00d268d382eba9528a3121ff31047", "guid": "bfdfe7dc352907fc980b868725387e9821ddd51b3102278876246d74a098bd98", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861aeb7ec62eba241e79704f06b8be756", "guid": "bfdfe7dc352907fc980b868725387e98441d21edbe0d671c663de4ab6f78b6a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98316e43fdb66eccca60f06ce82af23fc0", "guid": "bfdfe7dc352907fc980b868725387e98963aeb43f56a62f7d40ecdd4753f9bd1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98888d0ee2a5120427f22296ed30f34799", "guid": "bfdfe7dc352907fc980b868725387e98e46b15bb42c4be2ce66dce03dc3d299a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98558227aa6188828815c0f6070aaf1886", "guid": "bfdfe7dc352907fc980b868725387e9815fd2ba23e56bc5f0fc6e082b948488b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847d3874d644ae7acdb059edcdfdbebdd", "guid": "bfdfe7dc352907fc980b868725387e98a41f799b17321e85a9278475e3dbae9f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98105c41e4de619f040dd1ae7873522bbf", "guid": "bfdfe7dc352907fc980b868725387e9893cc933fa2411deda718879d423a1b09", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0926215d214b5694ee6174cda207839", "guid": "bfdfe7dc352907fc980b868725387e98f9f83fcfed8df11f1b23e037fbc28395", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9fe1fdd078217617e4bee4b08e86f6e", "guid": "bfdfe7dc352907fc980b868725387e98dbf199c990f6e09a608677306e5c7603", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e987c3b8840e60fd8b8139f91743cdc7d31", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983f7e09fb39c2fdc947e2c722d6530cae", "guid": "bfdfe7dc352907fc980b868725387e98ceb1e0d91df5724b13beb9235b25c269"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a23e35f270d67c05deea270164d15d23", "guid": "bfdfe7dc352907fc980b868725387e98f836be8e7e37c2d06a17031f72c53b8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3d0236ae113f76dae1e49cc0f6b7ef5", "guid": "bfdfe7dc352907fc980b868725387e988dcd91114191adb127725290cf636334"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866c440c6d85aa4793d313d85f1a11048", "guid": "bfdfe7dc352907fc980b868725387e9883eb048aaa003332af69f545be8bbea6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e11e7a009a5349dce632a187d04b01d", "guid": "bfdfe7dc352907fc980b868725387e98573c09468aaa4f5857947ca8e03924ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1c72de7393cf23a849c31a74c1684cf", "guid": "bfdfe7dc352907fc980b868725387e982ccbf0eb1a2756ce13a3384998e02a2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c764b83daef3941b86a9776a88176f4", "guid": "bfdfe7dc352907fc980b868725387e98fdce20ef9a71fac9cbab43f4d9a033b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1751c497c5a75ff1b557c3544c225be", "guid": "bfdfe7dc352907fc980b868725387e988f69219e3de54278e8610c5d1055c6ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813c31b183a5d2441ea9e6c513b11910e", "guid": "bfdfe7dc352907fc980b868725387e985db220a4952012f5bf58e351fce18717"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0f18a4056ebc088a004fb3493908a25", "guid": "bfdfe7dc352907fc980b868725387e987ca76d4088156b788248056559caff33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a87e7c151941ad0c44e502cf2f9308fb", "guid": "bfdfe7dc352907fc980b868725387e9809f0d2fe9d7d7eeb9584c948509151b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98948c33c6eb2252ff0923da7be4a704db", "guid": "bfdfe7dc352907fc980b868725387e98182f617b6fc9d8eb09442c51f14ae424"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980494811cb80b7adf30890f04625ab061", "guid": "bfdfe7dc352907fc980b868725387e9832de0b77660a3d535733ac289b81ba7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0b46d82797baa241ae03010881af26f", "guid": "bfdfe7dc352907fc980b868725387e9838e5048736afba826c2d3300d33d9ae3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a1238a6c94f0d82a5f61b168beaa047", "guid": "bfdfe7dc352907fc980b868725387e98ded8ad38e0adfd874eb14f3b9c9bd3c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efaa483100c7f0dbd71106b3c1a132d9", "guid": "bfdfe7dc352907fc980b868725387e98af813b31d48dd6e30d7a126dc119e062"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bb10d223a760d538d52c1601eb831f8", "guid": "bfdfe7dc352907fc980b868725387e9851557e560ed610d39bc5cdcbe2c18fa1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ff1966628ef0657fca32f3134cd3586", "guid": "bfdfe7dc352907fc980b868725387e98580a32d8c510f91db2170ddcd413cea9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883eebe2f621f310f1f8167d0fd3fba23", "guid": "bfdfe7dc352907fc980b868725387e988a96e2165e1a6692a832295f11589f89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f7aa2a33e798f41ddf8685e66cdbb2f", "guid": "bfdfe7dc352907fc980b868725387e98e12f3da63bd185f61f3045bd94b04817"}], "guid": "bfdfe7dc352907fc980b868725387e9843104e61d64918cd87be5e0aecee54fc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988338730910fc8b3bf8d9a66916e6c151", "guid": "bfdfe7dc352907fc980b868725387e98e3b649d2fbf627f69b8af003140385a3"}], "guid": "bfdfe7dc352907fc980b868725387e986059b19bed01a21014edf3a9c323714e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9878c9f5f42e86a56fff1b14121a147625", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e9805d8229943421d0012ee0eaadae86d90", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-YL", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS2", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-YL", "provisioningStyle": 1}], "type": "standard"}