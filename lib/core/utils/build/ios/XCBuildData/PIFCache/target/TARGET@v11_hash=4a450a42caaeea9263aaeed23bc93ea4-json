{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e0043bff45d3ab25b516e8c46d09a199", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f66dc72610d0203680983d9cb26bbfca", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9869f84048e7147c07193e707ec3e65367", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988c21ef7cd898509b1b179f60a5b60338", "name": "Debug-YL"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9869f84048e7147c07193e707ec3e65367", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9816bac1e7cce1bb851e00c367a7574ba2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9869f84048e7147c07193e707ec3e65367", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985994e8d2dc67abdd6507a9a183bae418", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9869f84048e7147c07193e707ec3e65367", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c0ff147270727460853a755b174c77d9", "name": "Release-JS"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9869f84048e7147c07193e707ec3e65367", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98841a3125feae8e2a101a53f7ba15f2a1", "name": "Release-JS2"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9869f84048e7147c07193e707ec3e65367", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983cd1e62a86bdb228526e6fa78d3730fc", "name": "Release-YL"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ad56ee48df3a2cbfddb8b27518a8ab53", "guid": "bfdfe7dc352907fc980b868725387e98d7e0b1c3d1d3bb94ed6724058d30ac76", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98bbb26e8647f7009f12d170db753898cd", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c8d506e68e154a1509b6a009947a1024", "guid": "bfdfe7dc352907fc980b868725387e98365ccd75faa9d109c8f2bea01d2cfefa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcf2dd6da55325c6d5cc1f60b79b8afc", "guid": "bfdfe7dc352907fc980b868725387e9854df1720576f1e359ae01c18caa1b99a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887497c9b69f89f80397173a59a35b0b4", "guid": "bfdfe7dc352907fc980b868725387e98fb3ddbcc00fe9576bde85e65443638b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98004d911db68a0059d40efef78078ec32", "guid": "bfdfe7dc352907fc980b868725387e98bfb6732962e7937447aef41353c9e476"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fe6d545563c6369b98c1576b403be4c", "guid": "bfdfe7dc352907fc980b868725387e98f9f36be7442a10e08834a369965249c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ada1ce345f969222ac81407e9f8f3ba0", "guid": "bfdfe7dc352907fc980b868725387e98ca7622ba8dd9e6ce05ff5d8efbff8be2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98408cd29902f2e60340089518bd0b50b0", "guid": "bfdfe7dc352907fc980b868725387e987fefd98c3c411ad85e982e0c1da5b5bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0906275ca7fe9d6f6379a2487ee0b79", "guid": "bfdfe7dc352907fc980b868725387e989fbacf8b29f19cb3d19e24fe9d58cacb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984668a21cb266e656d1a073358ef51b8a", "guid": "bfdfe7dc352907fc980b868725387e9841ba41a1a85eb700c1a219a0dc713296"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ee9a44ab5926a2dcaa714b2d346595c", "guid": "bfdfe7dc352907fc980b868725387e9858b77ad927b8f85efb6f1bf5709fcc85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816915bf8bdba3d079b179185bf05b3ad", "guid": "bfdfe7dc352907fc980b868725387e9835964a35adbbd091fae1084f06ccb04e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc4bca1048bb72c74d455b1d57167187", "guid": "bfdfe7dc352907fc980b868725387e9872397711bca6e1c50f490fd40970c7d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c5ba3af882600790d39ed941111ac4f", "guid": "bfdfe7dc352907fc980b868725387e98c689607e29aefc49ba9b135e5147b482"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825de6c9fd5948cc4967932ab03a8ebe0", "guid": "bfdfe7dc352907fc980b868725387e98b99d1f135b9992998f8edee577c9f46d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b37d9f381230b23d9d50e8a487beb15e", "guid": "bfdfe7dc352907fc980b868725387e987b9c66a52b3eac0a28c79fd7b70d4a32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f38673e363e4013f65a4c73ffe1d84f7", "guid": "bfdfe7dc352907fc980b868725387e987e2b41a352ae075b85c87812eff9100d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a6e26d6f23b52669fefea547d71a2fb", "guid": "bfdfe7dc352907fc980b868725387e98edd6a1440c3732ef8fba5bc88435d744"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ded96974573e593a9c0c46bb093c6ad9", "guid": "bfdfe7dc352907fc980b868725387e98e187093ed2aa1bc48066c6a5071b0065"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817a3932367c6ce8fb1c5074a4a40c5ca", "guid": "bfdfe7dc352907fc980b868725387e98a8607b56bcd83fcc69dedb62541eff19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98392ca9726658810ffac7f52e9acc3632", "guid": "bfdfe7dc352907fc980b868725387e98f90d80c27de82b48585a3dba9964181f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893713c58b6dccf275d3e4a4aeeead6f6", "guid": "bfdfe7dc352907fc980b868725387e9889b0509cc5ea9abeb68f67c8c9b6c9ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da43b82790dc9c23b88f98b58e7ef53f", "guid": "bfdfe7dc352907fc980b868725387e98f82bbec498800ea8f3d5af81017023a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884ecabea82809f83195fb91118ccb59b", "guid": "bfdfe7dc352907fc980b868725387e984ccab71fd3569c671f815905529be89a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d46c6381d0b882cfb7381550d4e6747", "guid": "bfdfe7dc352907fc980b868725387e9830cbe9af59d60350fc143df3a1e239a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e3549a436a0ab894b6ff42b061edf93", "guid": "bfdfe7dc352907fc980b868725387e983d8656645020909cd6ffb4705311ee09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984115d4084e00cecec1437e25b4ffc881", "guid": "bfdfe7dc352907fc980b868725387e98a71c79c6f6ef95363afe911f62823411"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a78c6f0e94ee43736e8b25d17ea2b99d", "guid": "bfdfe7dc352907fc980b868725387e980c76541ff79c2df51dbefb97d66e1130"}], "guid": "bfdfe7dc352907fc980b868725387e98f5e00d02e851ef3a3efdf609c373003d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98496cd76f3685b505a2eb66ae846bf0a3", "guid": "bfdfe7dc352907fc980b868725387e983491da142851c32a45c9b58f1165cc36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879c75b4c8b9f54183f8ca8fe4095c8b3", "guid": "bfdfe7dc352907fc980b868725387e98566ca348540dae8bd722132be36be864"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988338730910fc8b3bf8d9a66916e6c151", "guid": "bfdfe7dc352907fc980b868725387e98828bfc722b5177a3c68644bdd6adeb0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989738302f2e2d846c3298e633aef5b938", "guid": "bfdfe7dc352907fc980b868725387e983ace2eba5a2a91540e9d8442fbc227fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870148b0a96bb4f7c7fd953ab7ead94ea", "guid": "bfdfe7dc352907fc980b868725387e9800d1e4813d7a170fbffd82b37ddc1ca7"}], "guid": "bfdfe7dc352907fc980b868725387e9852527a08a0463dd0185e1c457b6b8eac", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98707f2d76ca7e3151b37ed8c3a9cbf445", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98b4a4c3538e8461a530b987a1ae584e1b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-YL", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS2", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-YL", "provisioningStyle": 1}], "type": "standard"}