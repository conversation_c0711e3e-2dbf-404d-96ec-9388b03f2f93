{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982351a08188f5e3d871f09d582722ca53", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/MJExtension/MJExtension-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/MJExtension/MJExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/MJExtension/MJExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "MJExtension", "PRODUCT_NAME": "MJExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98639e1ca37b170444dab36c80e421f598", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b488d2da6c0bbf19d7b6ca2431518a82", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/MJExtension/MJExtension-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/MJExtension/MJExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/MJExtension/MJExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "MJExtension", "PRODUCT_NAME": "MJExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a0297669f8ee5fe55a817760294481b1", "name": "Debug-YL"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b488d2da6c0bbf19d7b6ca2431518a82", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/MJExtension/MJExtension-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/MJExtension/MJExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/MJExtension/MJExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "MJExtension", "PRODUCT_NAME": "MJExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9892ea9b05cf43b2057a05123c6de876b4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b488d2da6c0bbf19d7b6ca2431518a82", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/MJExtension/MJExtension-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/MJExtension/MJExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/MJExtension/MJExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "MJExtension", "PRODUCT_NAME": "MJExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cf5531f74adbefda7b6f9dee56814030", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b488d2da6c0bbf19d7b6ca2431518a82", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/MJExtension/MJExtension-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/MJExtension/MJExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/MJExtension/MJExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "MJExtension", "PRODUCT_NAME": "MJExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98dc13bec35f799cee71cdfa52096b9ac0", "name": "Release-JS"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b488d2da6c0bbf19d7b6ca2431518a82", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/MJExtension/MJExtension-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/MJExtension/MJExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/MJExtension/MJExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "MJExtension", "PRODUCT_NAME": "MJExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cacf7f74f544e79f11b3c3712ecd73de", "name": "Release-JS2"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b488d2da6c0bbf19d7b6ca2431518a82", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/MJExtension/MJExtension-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/MJExtension/MJExtension-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/MJExtension/MJExtension.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "MJExtension", "PRODUCT_NAME": "MJExtension", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a8e4145aa735e869cd7ec242ab0a7349", "name": "Release-YL"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9897766ddea2d99ab52c6656bfa58b651e", "guid": "bfdfe7dc352907fc980b868725387e98684b0f5a1caac0c37863d6ef790a47c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fefd245a9fa613aac9aaf5507e040c9", "guid": "bfdfe7dc352907fc980b868725387e981e386fddb2020c6fc47f257bb04530bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830484a8c999b2438595e58a21275fd51", "guid": "bfdfe7dc352907fc980b868725387e98ab043678fd0574f8a7b157ed9b8a8b7e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987def15ac467e397cd661d140b4aa4632", "guid": "bfdfe7dc352907fc980b868725387e9833c85d2e21954fe80f82b15e3cef9edb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e4ed9af3437926828b5787fc8e376d9", "guid": "bfdfe7dc352907fc980b868725387e98f7d4c744e45ef04a6155bfbdaad2c697", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871a55abe4abbad797a99fd836dbd88c8", "guid": "bfdfe7dc352907fc980b868725387e98b01c34fc06216de54606883b8dd93dcd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982db2940e3ba55590e97d91fd69f88ce0", "guid": "bfdfe7dc352907fc980b868725387e98e7106524ce19c8452f1281608886885a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c63d29de10966a0d6f5ad7998f166e96", "guid": "bfdfe7dc352907fc980b868725387e982ec4e2693c95ff4c6631af0d4b210dd4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d660ebc9ccd3f74b7306f4664f9c1ae7", "guid": "bfdfe7dc352907fc980b868725387e9879fc76938de17b1ff02f65522f794288", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6fbb9e66654e59ff600ddca4a6345ab", "guid": "bfdfe7dc352907fc980b868725387e98429912499eadbdc7de214cb2d59dc9d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8b0c4288d0aac4b14f0573e3ed39ad2", "guid": "bfdfe7dc352907fc980b868725387e9808e2163512bf01c3e91fe3696c12f3be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98033f1f92b89703b5b33d99432da66e74", "guid": "bfdfe7dc352907fc980b868725387e98f51fc5d6603baca984e70edc322ef049", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989bcc27805da48d5e368c04adc8b55af4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9843dcf86dbd7d1489edf076a1b28f2df6", "guid": "bfdfe7dc352907fc980b868725387e98526cf5b1e4cbba4a8ce5c8a9fa13caa6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98602995fe48162e0524666b2a4cd16b80", "guid": "bfdfe7dc352907fc980b868725387e98080caaa8fd9a490c00e3d413ce1b14fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd7259fdbef0ad608ce70eb3a597d948", "guid": "bfdfe7dc352907fc980b868725387e98af6f34ba987f5199b1d84d15d8f3889e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d1c916d53e4dd2cffaa2621b7199d83", "guid": "bfdfe7dc352907fc980b868725387e9818be3f11ffe6ac783a37fca74dca7139"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989146f5ef5f762007f80df09383258805", "guid": "bfdfe7dc352907fc980b868725387e987de494da49e01fc27d766baed1100ba7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983924e14a9fddffcc4ca9a32c07c8d8c2", "guid": "bfdfe7dc352907fc980b868725387e98182d9600505d49b30d9e950f666711b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989842d45d9c1b952f68f0a6d1e4ad118f", "guid": "bfdfe7dc352907fc980b868725387e983b47e32f9a5be9d83950aaf817b2fd24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e2a8bf66f5ea0b13934abf995f0cf16", "guid": "bfdfe7dc352907fc980b868725387e9816f860868a78c5af6a34fbdf24ac7266"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d39b6d14d3fe9e263790e4852408315", "guid": "bfdfe7dc352907fc980b868725387e9819745a7acc03fcd07216fbe4371f9311"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988925de9de119019030eb84bd98585e9a", "guid": "bfdfe7dc352907fc980b868725387e982c5ad0672c9f23fd2676f1d59da16011"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823faa3e55ed37a28a18b74197ac8d6ac", "guid": "bfdfe7dc352907fc980b868725387e9826e35874a5f83c6f012a8341fcead913"}], "guid": "bfdfe7dc352907fc980b868725387e98598a0b42968a6b8dc65028bdf390deb7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988338730910fc8b3bf8d9a66916e6c151", "guid": "bfdfe7dc352907fc980b868725387e98e296cf7bf07f55d9cabe542d7dac66bd"}], "guid": "bfdfe7dc352907fc980b868725387e98945747a6ba04ad1cc78f310d65d337ad", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98cd8d7d677a0d556bae3fc7af6891ef9d", "targetReference": "bfdfe7dc352907fc980b868725387e98495986e992c7341ac84f6507424d5b6b"}], "guid": "bfdfe7dc352907fc980b868725387e980f42e3ed04cc3913e83fba60328c7194", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98495986e992c7341ac84f6507424d5b6b", "name": "MJExtension-MJExtension"}], "guid": "bfdfe7dc352907fc980b868725387e98fb9b69652dcc61b73b668789cc187643", "name": "MJExtension", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982ff64032b0e010de9000c15e0b9180ed", "name": "MJExtension.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-YL", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS2", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-YL", "provisioningStyle": 1}], "type": "standard"}