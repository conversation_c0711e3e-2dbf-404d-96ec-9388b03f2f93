{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98499ce69ef99c059b194b6c966fc38cab", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f3d538c0ab56ab4f36d65689e5131c5d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981367559b67f3239f140c5a05b2f49acf", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a71b309d7b4a3778350de864112cb724", "name": "Debug-YL"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981367559b67f3239f140c5a05b2f49acf", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e262fa19accd89cb1c1b879656571380", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981367559b67f3239f140c5a05b2f49acf", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986d2e6e4c58e3eb5aae5098b85db64722", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981367559b67f3239f140c5a05b2f49acf", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984f214ef1505e90a30493db0e56121e09", "name": "Release-JS"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981367559b67f3239f140c5a05b2f49acf", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989960fd207b17a67e3c67405575b51900", "name": "Release-JS2"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981367559b67f3239f140c5a05b2f49acf", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9855bbf4184525e598e0ce010f1ef35a94", "name": "Release-YL"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988b86d48b8c45272b255dccf6b3a8be18", "guid": "bfdfe7dc352907fc980b868725387e9868675332de81aec67932af79432c533e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c042de9766c9fa5f67501ec2f88f336f", "guid": "bfdfe7dc352907fc980b868725387e982c8d21121a6de0af618f889b21503289", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2235579a442a3457b89043d3cb559ac", "guid": "bfdfe7dc352907fc980b868725387e98dc7fbff65380666d27e13b3d9f636ca9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e47a11e99fdbe01a9a764d870b87b91d", "guid": "bfdfe7dc352907fc980b868725387e980f306714c79059b9fe9a413c9bb6f949", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988012425c759004e0304da498cc9c17d4", "guid": "bfdfe7dc352907fc980b868725387e9862a579e63673ba9ad52859aaeb4b9fe1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0124d7bca282429eeee575437c7b36e", "guid": "bfdfe7dc352907fc980b868725387e9822b7e8de32a606a14f282e8c61bca747", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b089942976471a5c6ee492d3754404a9", "guid": "bfdfe7dc352907fc980b868725387e98ab04c4aebb5f7c2f6b15cab0ca4ca884", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f27e9d786391956f9c80a46ae450fded", "guid": "bfdfe7dc352907fc980b868725387e980b5446f86b1185d4d6af2b5a782a2cb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0ed09bcd917d10484b72abe33a794d1", "guid": "bfdfe7dc352907fc980b868725387e989064734548ca9dcbad931142b34d602b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98893c7c8ace99fa0602393dcb36ba2c1f", "guid": "bfdfe7dc352907fc980b868725387e989dee3ae8725f4b04be2dc549104e9d5b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e41c21ba80a5d1d83c1f6d9f0d1571b", "guid": "bfdfe7dc352907fc980b868725387e987a720c8854ef9997be5258cfba5601c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834c6c3eadb35573f2fd6f3415da1239d", "guid": "bfdfe7dc352907fc980b868725387e982ce7936533dd3021a91cf510f72afbdc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889e9c35f00a58681a9d9a92341c295db", "guid": "bfdfe7dc352907fc980b868725387e98fd87c47af9681c76ebf9ba214657c060", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a38c87be3a73ffd12b4aafdf11394b7b", "guid": "bfdfe7dc352907fc980b868725387e9813c9aa375e08e9b87a0c6b113140be36", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816229011810d1d422715e30e0f0e1030", "guid": "bfdfe7dc352907fc980b868725387e987086b2d69892be6e4a334552e5f5feb4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c75ced5f682c36f686598a345037db5e", "guid": "bfdfe7dc352907fc980b868725387e98d4c2a6a530270ad5d72aaf65d736c1ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb242c7e213064eb5094a8ffe215b80a", "guid": "bfdfe7dc352907fc980b868725387e98e4f42cf13db58ededa4d1660bae2ebb6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98501a1cabb405f7c7db37a24fbf9fa35a", "guid": "bfdfe7dc352907fc980b868725387e98ffdab3a180ed4f3db4d581a6b9d8a2d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1cc2510fc2ccd8056e523e17866ed06", "guid": "bfdfe7dc352907fc980b868725387e98fc623d9d2ff3126ead1339692bb345af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2ecb9686aa2897c100c6c889a510697", "guid": "bfdfe7dc352907fc980b868725387e98aaa9f7609e2fbd823339e8eaaacdaea5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae7381d8663faca3b5c2cb8df8a69457", "guid": "bfdfe7dc352907fc980b868725387e98e0fd8bc197e115ac7e62ca08d2b2fa14", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b576caf7cc3fa9447f1cf280344c7bd6", "guid": "bfdfe7dc352907fc980b868725387e9837841e828383e322d30b028093760e99", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826e6b77b012af5e97fb21b80872784d1", "guid": "bfdfe7dc352907fc980b868725387e98c96233142fbf3eb872cf817616cdc246", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9802ce0c2619e9005e0c905c700aefa82f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e953244fe5b0cfac42695321521bde14", "guid": "bfdfe7dc352907fc980b868725387e987aa8abe7dc8286ad263dd97f6acf8306"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eba15b37d57f4cee78c335186070a01", "guid": "bfdfe7dc352907fc980b868725387e98f0d82b6f386efcaccc64a815822cf3fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984666148e8b3fbe7299b2e17d73211a12", "guid": "bfdfe7dc352907fc980b868725387e982b88bc5c5d5a02661d79944c7e023327"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a123cbc1d6fdb177cebe77ff0df070a", "guid": "bfdfe7dc352907fc980b868725387e98c2ad9a6992a8059ce5f6522e7321a752"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ea25867b6d7da3bf9814544ba7bc0ce", "guid": "bfdfe7dc352907fc980b868725387e9800b99400151a8584fdd1f5c0d38dde4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831b403834d2e7a99cecbdb2afb9cfabf", "guid": "bfdfe7dc352907fc980b868725387e98104888fe2574b3f90f9da5bd70d886df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fba9bd1055b4aedcac3bef6d561a4e5d", "guid": "bfdfe7dc352907fc980b868725387e9874649f19943d4f8635edd6b95c524499"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859acc39790e919d012a134c715ddcc7a", "guid": "bfdfe7dc352907fc980b868725387e98e31862b7889ff0ff5e0ba5078a2a3d72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864b3c6e43cd5ee551b573e6f8c5107df", "guid": "bfdfe7dc352907fc980b868725387e98b3bde1abc72f14a26c61622d23dce47f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98656586c0782e2f240a0004012b17ef93", "guid": "bfdfe7dc352907fc980b868725387e98bc9d2496b87c0a362bdc3644811354ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d75796a75daccbe9f39a949026fc4ed6", "guid": "bfdfe7dc352907fc980b868725387e98e23aa72146f2d9fea64ae3d1646e2a8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6857002d5c61cbbc37d0cd484c04f23", "guid": "bfdfe7dc352907fc980b868725387e98199be1778ac88f337f2b10a19ae3195a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a95e10326080162a09e6d6aad198d048", "guid": "bfdfe7dc352907fc980b868725387e98d8702cfe32f14a74c1288c303d1be3dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b41013727ffea9a317faa24baa66830", "guid": "bfdfe7dc352907fc980b868725387e9834dc15a1ce9ce91f19d3995c38a21d29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcbb73a69d94072b7cf2e9a2d271ba13", "guid": "bfdfe7dc352907fc980b868725387e9870091d3eae4ee362ec05a3fce764183b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc3dd3a7202098f313521d07e9e8270c", "guid": "bfdfe7dc352907fc980b868725387e980dc41bffee8d0ca32fbf1e0f0a135b61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98715f2ad3a8667d2f2f46bc9500164b93", "guid": "bfdfe7dc352907fc980b868725387e98c1ba0188e6d4f581a60039032b6483eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7f0dcb1304f8bd293e4f597d4c3611b", "guid": "bfdfe7dc352907fc980b868725387e9842c764c14ac847c9e8dae502b699c806"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bbdccd06a6e1e39587b93cc7433874a", "guid": "bfdfe7dc352907fc980b868725387e980c0cf3a5c191d90bd569455495a61e8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9462e6511507adf6acfe7d0a5ee7d51", "guid": "bfdfe7dc352907fc980b868725387e98b81c20d2770341868ff422f9a8880833"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ea56881b3efb2f8b8943daf23c5b17e", "guid": "bfdfe7dc352907fc980b868725387e98da4f1c2a5c25c3dd0fb3c64cd6984044"}], "guid": "bfdfe7dc352907fc980b868725387e989ccd4f48d7db3d3ad5988b955e1ccad0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988338730910fc8b3bf8d9a66916e6c151", "guid": "bfdfe7dc352907fc980b868725387e98ed3ab512ac80c128b966be815c4d97ff"}], "guid": "bfdfe7dc352907fc980b868725387e9866aaeb82d53811a19b9cbd3dcafeebd2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98368719dd201eeb74a96f3d3715b527b3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-YL", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS2", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-YL", "provisioningStyle": 1}], "type": "standard"}