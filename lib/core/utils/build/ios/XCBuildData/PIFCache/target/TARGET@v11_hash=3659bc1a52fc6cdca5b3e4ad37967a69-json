{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ca2b84e882911b886455ba066225e320", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983e806421e95f5dd4575985db05ebd928", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818fa2b0dcf7323063555db3e90b36efe", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981f30220a7e02b709f8283d7f83d38dc5", "name": "Debug-YL"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818fa2b0dcf7323063555db3e90b36efe", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c0ae015de0bbe23e96bab9338b57808e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818fa2b0dcf7323063555db3e90b36efe", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98403ac7492b44ce70f8b248db5ce28eeb", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818fa2b0dcf7323063555db3e90b36efe", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b9c72852a9ede2a8896f65becaace867", "name": "Release-JS"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818fa2b0dcf7323063555db3e90b36efe", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980c83402748e1f49d17940ea76cff63fa", "name": "Release-JS2"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818fa2b0dcf7323063555db3e90b36efe", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985823084dcdc8ba3cf2fcd57b7a95094f", "name": "Release-YL"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987dc2130844757bde8048150fcaf848a4", "guid": "bfdfe7dc352907fc980b868725387e9828b04e07e1a806314e560f831be5fd1b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5477eb53b747c93469a467b0552b69d", "guid": "bfdfe7dc352907fc980b868725387e98b0a048f105a5ef11cf1c71900f44e613", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de76e455512e87bc967a3f93e85a6c6f", "guid": "bfdfe7dc352907fc980b868725387e98f1a9fa3bb44759222e75506108fcfe3e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98bbcf5a80af40e81c303900e6bb02db5b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f42f52c5cf19097605a8bd18a13c63c9", "guid": "bfdfe7dc352907fc980b868725387e985b953b212a802ff400e5e9ce45d4e479"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cae91a348f2a7982f9761d6867f41da", "guid": "bfdfe7dc352907fc980b868725387e9801734effd19b4ba50cc543f317b34295"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f3cbf288818a82512daab532741e2c6", "guid": "bfdfe7dc352907fc980b868725387e9811f64447cb5446a6b07326810c20fc39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc90eea77304bed77781280109b40b5e", "guid": "bfdfe7dc352907fc980b868725387e985a417d71df9b587b05ba086b3e71681b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc6fee54efb566f385434693aa4cf161", "guid": "bfdfe7dc352907fc980b868725387e98759ec01c413f6fb17e3674a6f5a6b5d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c50fd31411872214594d1840774b593", "guid": "bfdfe7dc352907fc980b868725387e98a002d26c34bcfed6018a9720d79e4bb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8e41bf86b6105e4c0817a9b17a564a0", "guid": "bfdfe7dc352907fc980b868725387e9879467cdc52a92114cd7c3bddd84f297e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ecfecc4828d54c8af1996a73ddae554", "guid": "bfdfe7dc352907fc980b868725387e98d53b32aa9b1392bddfcde76b2cfef87f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecadd41f2a0a10f4316c1a7175fb6821", "guid": "bfdfe7dc352907fc980b868725387e985286e54ae5fa29a4b82a5ec7293e8a2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c4c4f2e5400feebe4dc3fa86925d76f", "guid": "bfdfe7dc352907fc980b868725387e98fd0e542f14f1ec9d5aa489fbf8a4f358"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840eac9765f9aa29f4701818eefbebf50", "guid": "bfdfe7dc352907fc980b868725387e983a6924aa08ff2748e67a6b5bb4dcd7da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885c1aeb8683e09933876d97995a86e57", "guid": "bfdfe7dc352907fc980b868725387e98222b948c2fc65777fb1e03cfc7f9930c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98556774b8f1ca2f3b7bb1d071c48c254b", "guid": "bfdfe7dc352907fc980b868725387e98acfa4ae1b4c1d6b8962475dc7b93ebf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d423bf82971542cfb5970b79a4579ce", "guid": "bfdfe7dc352907fc980b868725387e98707c005db67493e5e86a488e5e1bc2ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4eb5add0756a00d334199e2b9827f58", "guid": "bfdfe7dc352907fc980b868725387e98b4ba2ca18cc406413dc7aa8b46cb20ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd6de093e466bcce08a30714e06036c4", "guid": "bfdfe7dc352907fc980b868725387e9803f00969f7fa707612977b7a458b1eb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0f4597930ce3d6486e2e7f0232fdc8d", "guid": "bfdfe7dc352907fc980b868725387e9826278acfe467ea920477b5f9482de4f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fa11e3ee2e794af65fb46cae0a806f3", "guid": "bfdfe7dc352907fc980b868725387e98efd17ef5fd9da7af8ca5cb6205ee0adb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98807f35c92e80c30a20a3f88b824f0a5e", "guid": "bfdfe7dc352907fc980b868725387e983a9ac59c67eeb3a15555956d84afa6b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ccd668d99b6255349e5796621a976cd", "guid": "bfdfe7dc352907fc980b868725387e98ffea5905c5e888e43b3500a8e9560475"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845ba36b9d8d174197cab4615ad8a9fb3", "guid": "bfdfe7dc352907fc980b868725387e98531a73fa333a8b96b991a6899ed988d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986090f09a38b78a61298af270ae1ab042", "guid": "bfdfe7dc352907fc980b868725387e9836a1b8c42de46130a04ef823efb930a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988af073e25108c40e6e5ada3d6d259244", "guid": "bfdfe7dc352907fc980b868725387e986089f8fa7dad57f7b0026e7cd9b0653c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98972620632c762f9b9cd74103c984c9ff", "guid": "bfdfe7dc352907fc980b868725387e988a7335ba3765bb1b25817fbeaf821ec8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98365adf433c87a8452e74cca62c001bee", "guid": "bfdfe7dc352907fc980b868725387e98a695ae0ee985f03e25b1618cc1e53cec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a76b7f305b47fea40eb60771a715974b", "guid": "bfdfe7dc352907fc980b868725387e98322ea1345c72153df15918db054a0bcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8a77d0088e6a1f5baceb5051a47f35a", "guid": "bfdfe7dc352907fc980b868725387e98919815f84fa61c7c0301a84c93488062"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98286f29ce733dd6bef3bf66c62d4e8242", "guid": "bfdfe7dc352907fc980b868725387e9828b3c5448b036f05902caab9b8a42c0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981acef15ab19e2d5d2bb2febfb56c401c", "guid": "bfdfe7dc352907fc980b868725387e98f65dca93c945080080bea3c84d346ed2"}], "guid": "bfdfe7dc352907fc980b868725387e98ab024c6304320f6d5271438b78ed2897", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988338730910fc8b3bf8d9a66916e6c151", "guid": "bfdfe7dc352907fc980b868725387e9844f866ed41218bc77b11c35e9e967a7e"}], "guid": "bfdfe7dc352907fc980b868725387e9861400889b92ca4598128dea7f8b6683d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9852a054d422d30675e3476d3bda9f9664", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e98424a0579f05b8aa7b116a0e1ae14c72d", "name": "FirebaseSessions", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a41ba860aa6fc56673ac239987133d67", "name": "FirebaseSessions.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-YL", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS2", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-YL", "provisioningStyle": 1}], "type": "standard"}