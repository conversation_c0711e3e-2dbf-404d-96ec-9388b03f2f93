
import 'dart:core';

import 'package:game_store/core/constants/constants.dart';
import 'package:game_store/core/utils/global_config.dart';
import 'package:game_store/core/utils/screenUtil.dart';
import 'package:game_store/shared/widgets/lottery/lottery_game_filter_drawer.dart';

import 'log_util.dart';

class GameHomeUtil {
  static get paddingH => 14.gw;

  static get leftTabListViewWidth => 57.gw + paddingH + 10.gw;

  static get jackyPotHeight => 95.gw;

  // 每行个数
  static get gameGridViewCrossAxisCount => switch (kChannel) {
        'JS' => 1,
        'JS2' => 2,
        'YL' => 3,
        _ => 2,
      };

  // 子项宽高比
  static get gameGridViewAspectRatio => switch (kChannel) {
        'JS' => 281 / 107,
        'JS2' => 138 / 172,
        'YL' => 89 / 114,
        _ => 138 / 172,
      };

  // 网格间水平间距
  static get gameGridViewCrossAxisSpacing => switch (kChannel) {
        'JS' => 0.0,
        'JS2' => 10.gw,
        'YL' => 10.gw,
        _ => 10.gw,
      };

  // 网格间垂直间距
  static get gameGridViewMainAxisSpacing => switch (kChannel) {
        'JS' => 9.gw,
        'JS2' => 6.gw,
        'YL' => 8.gw,
        _ => 6.gw,
      };

  /// ************************************************ 首页热门/最近/收藏 start ***
  // 热门游戏单行个数
  static get popularGameGridViewCrossAxisCount => switch (kChannel) {
        'JS' => 3,
        'YL' => 3,
        _ => 3,
      };

  // 热门游戏子项宽高比
  static get popularGameGridViewAspectRatio => switch (kChannel) {
        'JS' => 89 / 114,
        'YL' => 89 / 114,
        _ => 89 / 114,
      };

  // 热门游戏网格间水平间距
  static get popularGameGridViewCrossAxisSpacing => switch (kChannel) {
        'JS' => 10.gw,
        'YL' => 10.gw,
        _ => 10.gw,
      };

  // 热门游戏网格间垂直间距
  static get popularGameGridViewMainAxisSpacing => switch (kChannel) {
        'JS' => 8.gw,
        'YL' => 8.gw,
        _ => 8.gw,
      };

  /// ************************************************ 首页热门/最近/收藏 end ***

  // 需要显示奖金池的gameCode List
  static List needJackyPotGameCodeList = ["DZ", "BY"];

  static bool needShowJackPot(String gameTypeCode) =>
      GlobalConfig.needShowHomeJackpotWidget() && needJackyPotGameCodeList.contains(gameTypeCode);

  /// 计算 `GridView` 高度 + `Jackpot` 高度 + 额外间距
  static double calculateSectionHeight({required int itemsLength, required String gameTypeCode}) {
    final double rightContentViewWidth = GSScreenUtil().screenWidth - leftTabListViewWidth - paddingH;

    // 顶部分类标题高度
    final sectionTitleHeight = gameGridViewCrossAxisCount == 1 ? 40.gw : 0;

    // 计算子项宽度
    final double itemWidth = (rightContentViewWidth - (gameGridViewCrossAxisCount - 1) * gameGridViewCrossAxisSpacing) /
        gameGridViewCrossAxisCount;

    // 计算子项高度
    final double itemHeight = itemWidth / gameGridViewAspectRatio;

    // 计算总行数
    final int rowCount = (itemsLength / gameGridViewCrossAxisCount).ceil();

    // 计算 `GridView` 高度
    final double gridViewHeight = (rowCount * itemHeight) + ((rowCount - 1) * gameGridViewMainAxisSpacing);

    // 是否需要 `Jackpot` 组件
    final double jackpotHeight = needShowJackPot(gameTypeCode) ? jackyPotHeight : 0;

    // 底部分割线高度
    final double dividerHeight = gameGridViewCrossAxisCount == 1 ? 0 : paddingH;
 final bottomPadding = gameTypeCode == 'BY' ? 50.gw : 0;
    // 计算最终总高度
    return sectionTitleHeight + gridViewHeight + jackpotHeight + dividerHeight + bottomPadding;
  }

  /// 计算 `GridView` 高度 + `Jackpot` 高度 + 额外间距
  static double calculatePopularSectionHeight({required int gameLength, required int venueLength}) {
    final double rightContentViewWidth = GSScreenUtil().screenWidth - leftTabListViewWidth - paddingH;

    // 顶部分类标题高度
    final sectionTitleHeight = 27.gw + 1;

    // 计算游戏单个宽度
    final double gameItemWidth =
        (rightContentViewWidth - (popularGameGridViewCrossAxisCount - 1) * popularGameGridViewCrossAxisSpacing) /
            popularGameGridViewCrossAxisCount;

    // 计算游戏单行高度
    final double gameItemHeight = gameItemWidth / popularGameGridViewAspectRatio;

    // 计算游戏总行数
    final int gameRowCount = (gameLength / popularGameGridViewCrossAxisCount).ceil();


    // 计算 `游戏GridView` 高度
    double gameGridViewHeight =
        (gameRowCount * gameItemHeight) + ((gameRowCount - 1) * popularGameGridViewMainAxisSpacing);

    if (gameRowCount > 0) {
      gameGridViewHeight += 41.gw; // 20.gw 子标题高度 + 13.gw paddingTop + 8.gw paddingBottom
    } else {
      gameGridViewHeight = 0;
    }

    // 计算场馆宽度
    final double venueWidth =
        (rightContentViewWidth - (gameGridViewCrossAxisCount - 1) * gameGridViewCrossAxisSpacing) /
            gameGridViewCrossAxisCount;

    // 计算场馆列表高度
    final double venueHeight = venueWidth / gameGridViewAspectRatio;

    // 计算总行数
    final int venueRowCount = (venueLength / gameGridViewCrossAxisCount).ceil();

    // 计算 `场馆GridView` 高度
    double venueGridViewHeight = (venueRowCount * venueHeight) + ((venueRowCount - 1) * gameGridViewMainAxisSpacing);

    if (venueRowCount > 0) {
      venueGridViewHeight += 41.gw; // 20.gw 子标题高度 + 13.gw paddingTop + 8.gw paddingBottom
    } else {
      venueGridViewHeight = 0;
    }

    // 底部分割线高度
    final double dividerHeight = gameGridViewCrossAxisCount == 1 ? 0 : paddingH;

    if (gameRowCount == 0 && venueGridViewHeight == 0) {
      return sectionTitleHeight + 100.gw + dividerHeight;
    }
    // 计算最终总高度
    return sectionTitleHeight + gameGridViewHeight + venueGridViewHeight + dividerHeight;
  }
}
