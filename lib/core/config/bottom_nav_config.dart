import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:game_store/core/constants/assets.dart';
import 'package:game_store/core/singletons/user_cubit.dart';
import 'package:game_store/core/utils/global_config.dart';
import 'package:game_store/features/page/0_tiktok/video_home_view.dart';
import 'package:game_store/features/page/1_game_home/game_home_view.dart';
import 'package:game_store/features/page/2_activity/activity_list_view.dart';
import 'package:game_store/features/page/3_transact/transact_view.dart';
import 'package:game_store/features/page/4_mine/mine_v2_view.dart';
import 'package:game_store/injection_container.dart';

import '../constants/constants.dart';

/// 底部导航栏页面类型
enum BottomNavType {
  gameHome, // 游戏首页
  videoHome, // 视频首页
  activity, // 活动页
  transact, // 交易页
  mine, // 我的页
}

/// 底部导航栏页面配置
class BottomNavConfig {
  final BottomNavType type;
  final String title;
  final String normalIcon;
  final String blackIcon;
  final String selectIcon;
  final Widget page;
  final bool isVisible;

  BottomNavConfig({
    required this.type,
    required this.title,
    required this.normalIcon,
    required this.blackIcon,
    required this.selectIcon,
    required this.page,
    this.isVisible = true,
  });

  /// 获取所有可见的页面配置
  static List<BottomNavConfig> getVisibleConfigs(context,{bool? tiktokTabVisible}) {
    final isVisible = tiktokTabVisible ?? sl<UserCubit>().state.userInfo?.tiktokTabVisible ?? true;
    return allConfigs(context).where((config) {
      if (!config.isVisible) return false;

      switch (config.type) {
        case BottomNavType.videoHome:
          return isVisible;
        default:
          return true;
      }
    }).toList();
  }

  /// 获取所有页面配置
  static List<BottomNavConfig> allConfigs(context) {
    final List<BottomNavConfig> configs = [
      BottomNavConfig(
        type: BottomNavType.gameHome,
        title: tr("game", context: context),
        normalIcon: Assets.gameIcon,
        blackIcon: Assets.gameGrayIcon,
        selectIcon: Assets.gameSelectedIcon,
        page: const GameHomePage(),
        isVisible: true,
      ),
      BottomNavConfig(
        type: BottomNavType.videoHome,
        title: tr("home", context: context),
        normalIcon: Assets.homeIcon,
        blackIcon: Assets.homeGrayIcon,
        selectIcon: Assets.homeSelectedIcon,
        page: const VideoHomePage(),
        isVisible: GlobalConfig.needShowVideoPage(),
      ),
      BottomNavConfig(
        type: BottomNavType.activity,
        title: tr("activities", context: context),
        normalIcon: Assets.activityIcon,
        blackIcon: Assets.activityGrayIcon,
        selectIcon: Assets.activitySelectedIcon,
        page: const ActivityListPage(),
        isVisible: true,
      ),
      BottomNavConfig(
        type: BottomNavType.transact,
        title: tr("transact", context: context),
        normalIcon: Assets.topUpIcon,
        blackIcon: Assets.topUpGrayIcon,
        selectIcon: Assets.topUpSelectedIcon,
        page: const TransactPage(),
        isVisible: true,
      ),
      BottomNavConfig(
        type: BottomNavType.mine,
        title: tr("nav_account", context: context),
        normalIcon: Assets.profileIcon,
        blackIcon: Assets.profileGrayIcon,
        selectIcon: Assets.profileSelectedIcon,
        page: const MineV2Page(),
        isVisible: true,
      ),
    ];

    if (kChannel == 'YL') {
      return [configs[1], ...configs.sublist(0, 1), ...configs.sublist(2)];
    }
    return configs;
  }

  /// 根据类型获取配置
  static BottomNavConfig? getConfigByType(context, {required BottomNavType type}) {
    return allConfigs(context).firstWhere((config) => config.type == type);
  }

  /// 获取页面索引
  static int getPageIndex(context, {required BottomNavType type}) {
    final visibleConfigs = getVisibleConfigs(context);
    return visibleConfigs.indexWhere((config) => config.type == type);
  }

  /// 获取页面总数
  static int getPageCount(context) {
    return getVisibleConfigs(context).length;
  }
}
