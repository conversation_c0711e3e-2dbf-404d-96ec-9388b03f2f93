import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:game_store/core/base/empty_widget.dart';
import 'package:game_store/core/theme/app_colors.dart';
import 'package:game_store/core/utils/font_size.dart';
import 'package:game_store/core/utils/screenUtil.dart';
import 'package:game_store/shared/widgets/app_image.dart';

import '../../../../core/base/base_state.dart';
import '../../../../core/constants/assets.dart';
import '../../../../core/utils/system_util.dart';
import '../../../../shared/widgets/common_button.dart';
import 'agent_recruitment_cubit.dart';
import 'agent_recruitment_state.dart';

/// 代理招募
class AgentRecruitmentView extends StatefulWidget {
  const AgentRecruitmentView({super.key});

  @override
  State<AgentRecruitmentView> createState() => _AgentRecruitmentViewState();
}

class _AgentRecruitmentViewState extends State<AgentRecruitmentView> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) => Scaffold(
        appBar: AppBar(
          centerTitle: true,
          title: Text(
            "partnership".tr(),
            style: TextStyle(
              fontSize: 16.fs,
              fontWeight: FontWeight.w500,
              color: const Color(0xff3B4165),
            ),
          ),
          backgroundColor: Colors.white,
          elevation: 0.5,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios, color: Colors.black87),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.all(10.gw),
            child: Column(
              children: [
                // Banner section
                _buildBanner(),

                // Service cards
                AnimationLimiter(
                  child: Column(
                    children: AnimationConfiguration.toStaggeredList(
                      duration: const Duration(milliseconds: 600),
                      childAnimationBuilder: (widget) => SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: widget,
                        ),
                      ),
                      children: [
                        // Online service cards
                        _buildService(),
                        SizedBox(height: 14.gw),
                        // Feature cards
                        _buildFeature(),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );

  Widget _buildService() => BlocBuilder<AgentRecruitmentCubit, AgentRecruitmentState>(
        builder: (context, state) {
          if (state.netState == NetState.loadingState) {
            return _buildShimmerLoading();
          }
          if (state.netState == NetState.dataSuccessState) {
            final data = state.data;
            if (data == null || data.isEmpty) {
              return const EmptyWidget(title: '暂无数据');
            }
            return ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: data.length,
              separatorBuilder: (context, index) => SizedBox(height: 8.gw),
              itemBuilder: (context, index) {
                final model = data[index];
                return _buildServiceCard(
                  imageUrl: model.logo,
                  title: model.name,
                  subtitle: model.description,
                  onTap: () {
                    if (model.type == 1) { // 內跳
                      SystemUtil.openCommonWebView(title: model.name, url: model.link);
                    } else if (model.type == 2){ // 外跳
                      SystemUtil.openUrlOnSystemBrowser(url: model.link, needOpenNewTag: true);
                    }
                  },
                );
              },
            );
          }
          return const SizedBox.shrink();
        },
      );

  Widget _buildFeature() => Column(children: [
        _buildFeatureCard(
          image: Assets.iconProfite,
          title: "高额收益躺赚",
          subtitle: "超高分成机制+精细化数据支持，让代理轻松拓客、坐享收益，真正躺赚无忧",
        ),
        SizedBox(height: 8.gw),
        _buildFeatureCard(
          image: Assets.iconEntertainment,
          title: "专业娱乐平台",
          subtitle: "多元娱乐平台覆盖体育、真人、彩票等全品类，满足多元娱乐需求，一站式体验更省心",
        ),
        SizedBox(height: 8.gw),
        _buildFeatureCard(
          image: Assets.iconChannel,
          title: "多端互通畅玩",
          subtitle: "全平台打通Web、H5、APP，支持多端畅玩，兼容市面主流设备系统",
        ),
        SizedBox(height: 8.gw),
        _buildFeatureCard(
          image: Assets.iconSafe,
          title: "安全稳定省心",
          subtitle: "自研系统控盘金融级防护架构，覆盖全球的多元资金结算方式，平台高效运营，省时省力省心",
        ),
      ]);

  Widget _buildBanner() {
    return Column(
      children: [
        AppImage(imageUrl: Assets.recruitmentTitle, height: 82.gw),

        SizedBox(height: 20.gw),
        Text(
          'consult_platform_service'.tr(),
          style: TextStyle(
            fontSize: 14.fs,
            color: const Color(0xff3B4165),
          ),
        ),
        SizedBox(height: 16.gw),
      ],
    );
  }

  Widget _buildServiceCard({
    required String imageUrl,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) =>
      Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.gw),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 6.gw),
          child: Row(
            children: [
              AppImage(
                radius: 6,
                imageUrl: imageUrl,
                width: 40.gw,
                height: 40.gw,
              ),
              SizedBox(width: 12.gw),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16.fs,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xff3B4165),
                      ),
                    ),
                    SizedBox(height: 2.gw),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12.fs,
                        color: const Color(0xff6B7280),
                      ),
                    ),
                  ],
                ),
              ),
              _buildConsultButton(onTap: onTap),
            ],
          ),
        ),
      );

  Widget _buildFeatureCard({
    required String image,
    required String title,
    required String subtitle,
  }) =>
      Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.gw),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.all(12.gw),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 40.gw,
                height: 40.gw,
                decoration: BoxDecoration(
                  color: AppColors.primaryLight.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: Image.asset(
                  image,
                  width: 24.gw,
                  height: 24.gw,
                ),
              ),
              SizedBox(width: 12.gw),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16.fs,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xff3B4165),
                      ),
                    ),
                    SizedBox(height: 4.gw),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12.fs,
                        color: const Color(0xff6B7280),
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );

  Widget _buildConsultButton({
    required VoidCallback onTap,
  }) =>
      CommonButton(
        title: 'consult_now'.tr(),
        bgImgPath: 'assets/images/button/bg_button.png',
        titlePadding: EdgeInsets.only(top: 1.gw, bottom: 6.gw),
        backgroundColor: Colors.transparent,
        height: 36.gw,
        width: 87.gw,
        fontSize: 14.fs,
        fontWeight: FontWeight.w400,
        radius: 0,
        onPressed: onTap,
      );

  Widget _buildShimmerLoading() => Column(
        children: List.generate(3, (index) => _buildShimmerCard()),
      );

  Widget _buildShimmerCard() => Container(
        margin: EdgeInsets.only(bottom: 8.gw),
        padding: EdgeInsets.all(16.gw),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.gw),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 24.gw,
              height: 24.gw,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4.gw),
                gradient: LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: [
                    Colors.grey[300]!,
                    Colors.grey[100]!,
                    Colors.grey[300]!,
                  ],
                  stops: const [0.0, 0.5, 1.0],
                ),
              ),
              child: TweenAnimationBuilder<double>(
                tween: Tween<double>(begin: -2.0, end: 2.0),
                duration: const Duration(milliseconds: 1500),
                builder: (context, value, child) {
                  return Transform.translate(
                    offset: Offset(value * 100, 0),
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: [
                            Colors.white.withOpacity(0.0),
                            Colors.white.withOpacity(0.5),
                            Colors.white.withOpacity(0.0),
                          ],
                          stops: const [0.0, 0.5, 1.0],
                        ),
                      ),
                    ),
                  );
                },
                onEnd: () {
                  setState(() {});
                },
              ),
            ),
            SizedBox(width: 12.gw),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 120.gw,
                    height: 16.gw,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4.gw),
                      gradient: LinearGradient(
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                        colors: [
                          Colors.grey[300]!,
                          Colors.grey[100]!,
                          Colors.grey[300]!,
                        ],
                        stops: const [0.0, 0.5, 1.0],
                      ),
                    ),
                    child: TweenAnimationBuilder<double>(
                      tween: Tween<double>(begin: -2.0, end: 2.0),
                      duration: const Duration(milliseconds: 1500),
                      builder: (context, value, child) {
                        return Transform.translate(
                          offset: Offset(value * 100, 0),
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: [
                                  Colors.white.withOpacity(0.0),
                                  Colors.white.withOpacity(0.5),
                                  Colors.white.withOpacity(0.0),
                                ],
                                stops: const [0.0, 0.5, 1.0],
                              ),
                            ),
                          ),
                        );
                      },
                      onEnd: () {
                        setState(() {});
                      },
                    ),
                  ),
                  SizedBox(height: 8.gw),
                  Container(
                    width: 200.gw,
                    height: 12.gw,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4.gw),
                      gradient: LinearGradient(
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                        colors: [
                          Colors.grey[300]!,
                          Colors.grey[100]!,
                          Colors.grey[300]!,
                        ],
                        stops: const [0.0, 0.5, 1.0],
                      ),
                    ),
                    child: TweenAnimationBuilder<double>(
                      tween: Tween<double>(begin: -2.0, end: 2.0),
                      duration: const Duration(milliseconds: 1500),
                      builder: (context, value, child) {
                        return Transform.translate(
                          offset: Offset(value * 100, 0),
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: [
                                  Colors.white.withOpacity(0.0),
                                  Colors.white.withOpacity(0.5),
                                  Colors.white.withOpacity(0.0),
                                ],
                                stops: const [0.0, 0.5, 1.0],
                              ),
                            ),
                          ),
                        );
                      },
                      onEnd: () {
                        setState(() {});
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
}
