import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:game_store/core/base/base_state.dart';
import 'package:game_store/core/config/bottom_nav_config.dart';
import 'package:game_store/core/constants/constants.dart';
import 'package:game_store/core/models/apis/channel.dart';
import 'package:game_store/core/models/apis/video.dart';
import 'package:game_store/core/models/entities/game_entity.dart';
import 'package:game_store/core/models/entities/short_video_entity.dart';
import 'package:game_store/core/models/entities/user_vip_entity.dart';
import 'package:game_store/core/models/entities/winner_entity.dart';
import 'package:game_store/core/singletons/user_cubit.dart';
import 'package:game_store/core/utils/connectivity_util.dart';
import 'package:game_store/core/utils/game_util.dart';
import 'package:game_store/core/utils/log_util.dart';
import 'package:game_store/core/utils/polling_services/polliing_services.dart';
import 'package:game_store/core/utils/system_util.dart';
import 'package:game_store/features/page/main/screens/main_screen_cubit.dart';
import 'package:game_store/features/routers/app_router.dart';
import 'package:game_store/features/routers/route_tracker.dart';
import 'package:game_store/injection_container.dart';

import 'package:game_store/shared/widgets/tiktok/home/<USER>/tik_tok_home_page.dart';

import 'video_home_state.dart';

class VideoHomeCubit extends Cubit<VideoHomeState> {
  late StreamSubscription<UserVipEntity?> _vipStream;
  late StreamSubscription<NetworkStatus> _connectivitySubscription;
  bool _initialDataFetched = false;

  final GlobalKey<TikTokHomePageState> shortVideoKey = GlobalKey();
  final GlobalKey<TikTokHomePageState> blackVideoKey = GlobalKey();

  List<int> watchedVideoIdList = [];

  VideoHomeCubit() : super(VideoHomeState()) {
    /// 监听网络状态
    _initializeConnectivity();
  }

  // 初始化网络监听
  void _initializeConnectivity() {
    _connectivitySubscription = ConnectivityUtil().connectivityStream.listen((status) {
      if (status.hasConnection && !_initialDataFetched) {
        _initialDataFetched = true;
        _fetchInitialData();
        SystemUtil.checkAppUpdate();
      }

      // 可以在这里处理 WiFi 状态变化
      // 例如：在非 WiFi 环境下显示提示或调整视频质量
      _handleWifiStatusChange(status.isWifi);
    });

    // 检查当前状态
    final currentStatus = ConnectivityUtil().status;
    if (currentStatus.hasConnection && !_initialDataFetched) {
      _initialDataFetched = true;
      _fetchInitialData();
    }
    _handleWifiStatusChange(currentStatus.isWifi);
  }

  void _handleWifiStatusChange(bool isWifi) {
    // 处理 WiFi 状态变化的逻辑
    // 例如：
    if (!isWifi) {
      // 可以发出非 WiFi 环境的提示
      // 或者调整视频质量设置等
    }
  }

  // 获取初始数据
  void _fetchInitialData() {
    /// 请求首页数据
    if (GameUtil().gameList.isEmpty) {
      fetchGameListData();
    }
  }

  resetData() async {
    if (!sl<UserCubit>().state.isLogin) {
      _updateVideoListState(true, [], NetState.loadingState);
      _updateVideoListState(false, [], NetState.loadingState);
      shortVideoKey.currentState?.cleanVideoList();
      blackVideoKey.currentState?.cleanVideoList();
    }

    final list = await fetchShortVideoList(isNormal: true);
    if (list.isNotEmpty) {
      shortVideoKey.currentState?.addNewVideoList(list);
    }
    final listBlack = await fetchShortVideoList(isNormal: false);
    if (listBlack.isNotEmpty) {
      blackVideoKey.currentState?.addNewVideoList(listBlack);
    }
  }

  onChangePlayerStatus(HomeTikTokPlayerStatus status) {
    if (state.playerStatus != status) {
      emit(state.copyWith(playerStatus: status));
    }
  }

  Future<List<ShortVideoEntity>> fetchShortVideoList({required bool isNormal, bool forceRefresh = false}) async {
    final currentList = isNormal ? state.normalVideoList : state.blackVideoList;
    final List<ShortVideoEntity> list = List.from(currentList);

    final isVideoVip = (sl<UserCubit>().state.videoVipInfo?.days ?? 0) > 0;
    final isOverWatchLimit = list.length >= sl<UserCubit>().shortVideoCountLimit;
    if (!isVideoVip && isOverWatchLimit) {
      final res = await VideoApi.fetchRemainingVipDays();
      if (res != null && res.days == 0) {
        /// 显示解锁banner
        emit(state.copyWith(isShowUnlockVideoBanner: true));
        return [];
      }
    }

    final res = await VideoApi.fetchShortVideoList(
      isNormal: isNormal,
      forceRefresh: forceRefresh,
    );

    /// 显示登录按钮
    emit(state.copyWith(isShowUnlockVideoBanner: false));
    if (res.isNotEmpty) {
      // 使用 Set 去重，基于对象的 id
      Set<int> originalIds = list.map((item) => item.videoId).toSet();
      Set<int> newIds = res.map((item) => item.videoId).toSet();
      // 找出新增的元素
      Set<int> addedIds = newIds.difference(originalIds);
      // 获取新增的对象
      List<ShortVideoEntity> addedList = res.where((item) => addedIds.contains(item.videoId)).toList();

      LogD("addedList.length: ${addedList.length}");
      // 将新增的对象添加到原始列表中
      list.addAll(addedList);

      if (kDebug) {
        for (int i = 0; i < list.length; i++) {
          list[i].testIndex = i.toString();
          // list[i].lineUrl = 'https://media.w3.org/2010/05/sintel/trailer.mp4';
        }
      }

      _updateVideoListState(isNormal, list, NetState.dataSuccessState);
      return addedList;
    } else {
      _updateVideoListState(isNormal, list, NetState.errorShowRefresh);
      return [];
    }
  }

  void _updateVideoListState(bool isNormal, List<ShortVideoEntity> list, NetState netState) {
    emit(state.copyWith(
      normalVideoList: isNormal ? list : null,
      blackVideoList: isNormal ? null : list,
      normalListNetState: isNormal ? netState : null,
      blackListNetState: isNormal ? null : netState,
    ));
  }

  /// 请求游戏列表数据
  fetchGameListData() async {
    emit(state.copyWith(netState: NetState.loadingState));
    try {
      // 请求数据
      final gameList = GameUtil().gameList;
      
      final list = state.gameTypeList.isEmpty && gameList.isNotEmpty ? gameList : await GameUtil().fetchGameList();

      /// 设置热门平台数据
      emit(state.copyWith(
        gameTypeList: list,
        sectionHeights: list.map((e) {
          if (e.name == "热门") return 0.0;
          return e.sectionHeight;
        }).toList(),
      ));
    } catch (e) {
      emit(state.copyWith(netState: NetState.errorShowRefresh));
    } finally {
      emit(
          state.copyWith(netState: state.gameTypeList.isEmpty ? NetState.errorShowRefresh : NetState.dataSuccessState));
      if (state.gameTypeList.isEmpty) {
        // 如果缓存数据存在，直接更新状态
        if (GameUtil().gameList.isNotEmpty) {
          emit(state.copyWith(
            netState: NetState.dataSuccessState,
            gameTypeList: GameUtil().gameList,
          ));
        }
      }
    }
  }

  // 点击喜欢/取消喜欢
  Future<bool> onClickVideoLike({required int index, required bool isNormal, bool? isLike}) async {
    List<ShortVideoEntity> list = List.from(isNormal ? state.normalVideoList : state.blackVideoList);
    if (list.length < index) return false;
    ShortVideoEntity model = list[index];
    if (isLike == true && model.isLiked) return false;

    bool flag = await VideoApi.operaVideoLike(isLike: !model.isLiked, videoId: model.videoId);
    if (flag) {
      model.isLiked = !model.isLiked;
      if (model.isLiked) {
        model.likes += 1;
      } else {
        model.likes -= 1;
      }
    }
    return flag;
  }

  void startWinnerListPolling() async {
    if (kChannel == 'JS') return;

    sl<PollingService>().startPolling(
      keepAlive: true,
      id: kGSVideoWinnerList,
      onPoll: () async {
        var res = await ChannelApi.fetchWinnerList();
        if (res.isNotEmpty) {
          List<WinnerEntity> currentList = List.from(state.winnerList);
          currentList.addAll(res);
          emit(state.copyWith(winnerList: currentList));
        }
        return true;
      },
      interval: const Duration(seconds: 10),
      shouldPause: () =>
          RouteTracker().getCurrentRouteName() != AppRouter.nav ||
          sl<MainScreenCubit>().state.currentTabType != BottomNavType.videoHome,
    );
  }

  /// 记录已观看的视频
  recordVideoWatched(int videoId, {required bool isNormal}) {
    if (watchedVideoIdList.contains(videoId)) return;
    watchedVideoIdList.add(videoId);
    VideoApi.submitWatchedVideoId(videoId: videoId, isNormal: isNormal);
  }

  @override
  Future<void> close() {
    _vipStream.cancel();
    _connectivitySubscription.cancel();
    return super.close();
  }
}
