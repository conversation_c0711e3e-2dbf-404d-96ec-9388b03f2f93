import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:game_store/core/base/base_state.dart';
import 'package:game_store/core/base/net_error_widget.dart';
import 'package:game_store/core/models/entities/game_entity.dart';
import 'package:game_store/core/singletons/user_cubit.dart';
import 'package:game_store/core/singletons/user_state.dart';
import 'package:game_store/core/utils/auth_util.dart';
import 'package:game_store/core/utils/game_util.dart';
import 'package:game_store/core/utils/screenUtil.dart';
import 'package:game_store/core/utils/system_util.dart';
import 'package:game_store/features/page/0_tiktok/video_library/video_library_view.dart';
import 'package:game_store/features/page/1_game_home/game_home_cubit.dart';
import 'package:game_store/features/page/main/screens/main_screen_cubit.dart';
import 'package:game_store/injection_container.dart';
import 'package:game_store/shared/widgets/home/<USER>';
import 'package:game_store/shared/widgets/indicator/video_filckr_indicator.dart';
import 'package:game_store/shared/widgets/tiktok/home/<USER>/tik_tok_home_page.dart';
import 'package:game_store/shared/widgets/tiktok/home_tiktok_header.dart';

import '../../../core/constants/constants.dart';
import '../../../core/utils/global_config.dart';
import '../../../shared/widgets/notification/notification_badge.dart';
import '../0_home/home_drawer/home_drawer_cubit.dart';
import '../0_home/home_drawer/home_drawer_view.dart';
import 'video_home_cubit.dart';
import 'video_home_state.dart';
import 'package:game_store/core/utils/connectivity_util.dart';

import 'video_library/popular_video/popular_video_view.dart';

class VideoHomePage extends StatefulWidget {
  const VideoHomePage({super.key});

  @override
  State<StatefulWidget> createState() => _VideoHomePageState();
}

class _VideoHomePageState extends State<VideoHomePage> with TickerProviderStateMixin {
  final GlobalKey<HomeExpandGameWidgetState> _expandKey = GlobalKey<HomeExpandGameWidgetState>();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  HomeTikTokPlayerStatus playerStatus = HomeTikTokPlayerStatus.play;
  late AnimationController _balanceRefreshController;
  final tabList = ["黑料", "短视频", "午夜影院", "热门电影"];

  final int initialPage = 1;
  late TabController _tabController;
  late final PageController _pageController = PageController(initialPage: initialPage);
  late int _selectedMainTabIndex = initialPage;
  TikTokHomePage? normalTikTokPage;
  TikTokHomePage? blackTikTokPage;
  int animationCount = 0;
  bool _isDataInitialized = false;
  late StreamSubscription<NetworkStatus> _connectivitySubscription;

  TikTokHomePageState? get _currentTikTokHomePageState {
    final cubit = BlocProvider.of<VideoHomeCubit>(context);
    if (_selectedMainTabIndex == 0) {
      return cubit.blackVideoKey.currentState;
    } else if (_selectedMainTabIndex == 1) {
      return cubit.shortVideoKey.currentState;
    }

    return null;
  }

  @override
  void initState() {

    /// 开始赢家列表轮询
    sl<VideoHomeCubit>().startWinnerListPolling();

    _initializeMainTabController();
    _initConnectivityAndData();
    showMuteToast();
    SystemUtil.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);

    _balanceRefreshController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    );

    // 添加监听器
    _pageController.addListener(() {
      double? page = _pageController.page;

      // 检测整数页面变化
      if (page != null && page % 1 == 0) {
        _selectedMainTabIndex = page.toInt();
        _tabController.index = _selectedMainTabIndex;
        SystemChrome.setSystemUIOverlayStyle(
            page == 2 || page == 3 ? SystemUiOverlayStyle.dark : SystemUiOverlayStyle.light);
        sl<MainScreenCubit>().isTabBarWhiteColorChanged(page == 2 || page == 3);
        sl<VideoHomeCubit>()
            .onChangePlayerStatus(page == 2 || page == 3 ? HomeTikTokPlayerStatus.pause : HomeTikTokPlayerStatus.play);

        if (mounted) {
          setState(() {});
        }
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleMainTabSelection);
    _balanceRefreshController.dispose();
    _tabController.dispose();
    _pageController.dispose();
    _connectivitySubscription.cancel();
    super.dispose();
  }


  void showMuteToast() async {
    // await Future.delayed(const Duration(seconds: 10));
    // if (GlobalConfig().isShortVideoMute) {
    //   GSEasyLoading.showToast("当前视频处于静音状态，点击右下角音量键可恢复");
    // }
  }

  void _initializeMainTabController() {
    _tabController = TabController(vsync: this, length: tabList.length, initialIndex: initialPage);
    _tabController.addListener(_handleMainTabSelection);
    if (mounted) {
      setState(() {});
    }
  }

  void _handleMainTabSelection() async {
    setState(() {
      _selectedMainTabIndex = _tabController.index;
      _pageController.jumpToPage(_selectedMainTabIndex);
    });

    final cubit = BlocProvider.of<VideoHomeCubit>(context);
    if (_selectedMainTabIndex == 0) {
      cubit.shortVideoKey.currentState?.pause();
      cubit.blackVideoKey.currentState?.play();
    } else if (_selectedMainTabIndex == 1) {
      cubit.shortVideoKey.currentState?.play();
      cubit.blackVideoKey.currentState?.pause();
    } else if (_selectedMainTabIndex == 2) {
      cubit.shortVideoKey.currentState?.pause();
      cubit.blackVideoKey.currentState?.pause();
    }
  }

  void _initConnectivityAndData() {
    if (ConnectivityUtil().status.hasConnection && !_isDataInitialized) {
      _initializeData();
    }

    _connectivitySubscription = ConnectivityUtil().connectivityStream.listen((status) {
      if (status.hasConnection && !_isDataInitialized) {
        _initializeData();
      }
    });
  }

  void _initializeData() {
    _isDataInitialized = true;
    initNormalShortVideoPage();
    initBlackShortVideoPage();
  }

  initNormalShortVideoPage({bool forceRefresh = false}) async {
    if (normalTikTokPage != null) return;
    final cubit = context.read<VideoHomeCubit>();
    final res = await cubit.fetchShortVideoList(isNormal: true, forceRefresh: forceRefresh);
    if (res.isNotEmpty) {
      normalTikTokPage = TikTokHomePage(
          key: cubit.shortVideoKey,
          videoDataList: res,
          onClickLike: (index) async {
            AuthUtil.checkIfLogin(() {});
            return cubit.onClickVideoLike(index: index, isNormal: true);
          },
          onDoubleTapLike: (index) async {
            AuthUtil.checkIfLogin(() {});
            return cubit.onClickVideoLike(index: index, isNormal: true, isLike: true);
          },
          videoProvider: (index, playerList) async {
            return await cubit.fetchShortVideoList(isNormal: true);
          });
      setState(() {});
    }
  }

  Future initBlackShortVideoPage({bool forceRefresh = false}) async {
    final cubit = context.read<VideoHomeCubit>();
    final res = await cubit.fetchShortVideoList(isNormal: false, forceRefresh: forceRefresh);
    if (res.isNotEmpty) {
      blackTikTokPage = TikTokHomePage(
          key: cubit.blackVideoKey,
          videoDataList: res,
          onClickLike: (index) async {
            AuthUtil.checkIfLogin(() {});
            return cubit.onClickVideoLike(index: index, isNormal: false);
          },
          onDoubleTapLike: (index) async {
            AuthUtil.checkIfLogin(() {});
            return cubit.onClickVideoLike(index: index, isNormal: true, isLike: true);
          },
          videoProvider: (index, playerList) async {
            return await cubit.fetchShortVideoList(isNormal: false);
          });
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final cubit = BlocProvider.of<VideoHomeCubit>(context);
    return MultiBlocListener(
      listeners: [
        BlocListener<VideoHomeCubit, VideoHomeState>(listener: (context, state) {
          if (playerStatus != state.playerStatus) {
            playerStatus = state.playerStatus;

            if (playerStatus == HomeTikTokPlayerStatus.play) {
              _currentTikTokHomePageState?.play();
            } else if (playerStatus == HomeTikTokPlayerStatus.pause) {
              _currentTikTokHomePageState?.pause();
            }
          }
        }),
        BlocListener<UserCubit, UserState>(
            listenWhen: (previous, current) => current.isLogin != previous.isLogin,
            listener: (context, state) {
            cubit.resetData();
        })
      ],
      child: Scaffold(
        backgroundColor: Colors.black,
        key: _scaffoldKey,
        drawer: BlocProvider(
          create: (BuildContext context) => HomeDrawerCubit(),
          child: const HomeDrawer(),
        ),
        body: Stack(
          alignment: Alignment.center,
          children: [
            Positioned.fill(
                child: PageView.builder(
                    controller: _pageController,
                    itemCount: tabList.length,
                    onPageChanged: (value) {
                      if (value == 2) {
                        setState(() {
                          animationCount++;
                        });
                        _expandKey.currentState?.closeExpand();
                      }
                    },
                    itemBuilder: (context, index) {
                      final currentTitle = tabList[index];
                      if (currentTitle == "午夜影院") {
                        return const VideoLibraryPage(animationCount: 0);
                      }
                      if (currentTitle == "热门电影") {
                        return const PopularVideoPage(
                          animationCount: 0,
                        );
                      }
                      final isNormalVideo = currentTitle == "短视频";
                      return Stack(
                        children: [
                          isNormalVideo
                              // ? (normalTikTokPageA ?? const VideoFlickrIndicator())
                              ? (normalTikTokPage ?? const VideoFlickrIndicator())
                              : (blackTikTokPage ?? const VideoFlickrIndicator()),
                          BlocBuilder<VideoHomeCubit, VideoHomeState>(
                              buildWhen: (previous, current) => isNormalVideo
                                  ? previous.normalListNetState != current.normalListNetState
                                  : previous.blackListNetState != current.blackListNetState,
                              builder: (context, state) {
                                final hasError = isNormalVideo
                                    ? state.normalListNetState == NetState.errorShowRefresh
                                    : state.blackListNetState == NetState.errorShowRefresh;

                                if (hasError) {
                                  return Positioned.fill(
                                    child: Container(
                                      color: Colors.black,
                                      child: NetErrorWidget(
                                          refreshMethod: () => isNormalVideo
                                              ? initNormalShortVideoPage(forceRefresh: true)
                                              : initBlackShortVideoPage(forceRefresh: true)),
                                    ),
                                  );
                                }
                                return const SizedBox.shrink();
                              }),
                        ],
                      );
                    })),
            if (_tabController.index != 2 && _tabController.index != 3 && GlobalConfig.needShowTikTokExpandBar()) ...[
              /// 『玩会游戏』， 展开视图
              BlocBuilder<VideoHomeCubit, VideoHomeState>(builder: (context, state) {
                return Positioned(
                  top: 130.gw,
                  child: Center(
                    child: HomeExpandGameWidget(
                      key: _expandKey,
                      gameTypeList: state.gameTypeList,
                      onTapRefreshBalance: () async {
                        _balanceRefreshController.repeat();
                        await GameUtil.transferOutAllPlatform();
                        _balanceRefreshController.reset();
                      },
                      onTapPlatformCell: (GameType typeModel, GamePlatform platformModel) {
                        if (typeModel.name == "热门") {
                          AuthUtil.checkIfLogin(() {
                            GameUtil().onClickPlatformCellBy(
                              platformCode: platformModel.platformCode,
                              thirdPlatformId: platformModel.data.first.thirdPlatformId,
                            );
                          });
                        } else {
                          context.read<GameHomeCubit>().onClickPlatformCell(gameType: typeModel, platform: platformModel);
                        }
                      },
                      controller: _balanceRefreshController,
                    ),
                  ),
                );
              }),
            ],
            Container(
              alignment: Alignment.topCenter,
              padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top + 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  HomeTiktokHeader(
                    tabs: tabList,
                    tabController: _tabController,
                  ),
                  // if (_tabController.index == 3) ...[
                  Padding(
                    padding: EdgeInsets.only(right: 10.gw),
                    child: InkWell(
                      onTap: () => _scaffoldKey.currentState?.openDrawer(),
                      child: NotificationBadge(
                        child: Image.asset(
                          "assets/images/home/<USER>",
                          width: 20.gw,
                          height: 20.gw,
                        ),
                      ),
                    ),
                  ),
                  // ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
