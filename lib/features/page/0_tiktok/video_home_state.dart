import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:game_store/core/base/base_state.dart';
import 'package:game_store/core/models/entities/game_entity.dart';
import 'package:game_store/core/models/entities/short_video_entity.dart';
import 'package:game_store/core/models/entities/winner_entity.dart';

import '../../../core/models/entities/bonus_pool_entity.dart';

enum HomeTikTokPlayerStatus {
  play,
  pause,
}

class VideoHomeState extends BaseState with EquatableMixin {
  final List<GameType> gameTypeList;
  final List<double> sectionHeights;
  final bool isShowUnlockVideoBanner;
  TabController? mainTabController;
  String jumpTabTitle;
  final List<ShortVideoEntity> normalVideoList;
  final NetState normalListNetState;
  final List<ShortVideoEntity> blackVideoList;
  final NetState blackListNetState;
  final HomeTikTokPlayerStatus playerStatus;
  final List<WinnerEntity> winnerList;

  VideoHomeState({
    this.gameTypeList = const [],
    this.sectionHeights = const [],
    NetState netState = NetState.initializeState,
    this.isShowUnlockVideoBanner = false,
    this.mainTabController,
    this.jumpTabTitle = '',
    this.normalVideoList = const [],
    this.normalListNetState = NetState.loadingState,
    this.blackVideoList = const [],
    this.blackListNetState = NetState.loadingState,
    this.playerStatus = HomeTikTokPlayerStatus.play,
    this.winnerList = const [],
  }) {
    this.netState = netState;
  }

  VideoHomeState copyWith({
    List<GameType>? gameTypeList,
    List<double>? sectionHeights,
    NetState? netState,
    bool? isShowUnlockVideoBanner,
    TabController? mainTabController,
    String? jumpTabTitle,
    List<ShortVideoEntity>? normalVideoList,
    NetState? normalListNetState,
    List<ShortVideoEntity>? blackVideoList,
    NetState? blackListNetState,
    HomeTikTokPlayerStatus? playerStatus,
    List<WinnerEntity>? winnerList,
  }) {
    return VideoHomeState(
      gameTypeList: gameTypeList ?? this.gameTypeList,
      sectionHeights: sectionHeights ?? this.sectionHeights,
      netState: netState ?? this.netState,
      isShowUnlockVideoBanner: isShowUnlockVideoBanner ?? this.isShowUnlockVideoBanner,
      mainTabController: mainTabController ?? this.mainTabController,
      jumpTabTitle: jumpTabTitle ?? this.jumpTabTitle,
      normalVideoList: normalVideoList ?? this.normalVideoList,
      normalListNetState: normalListNetState ?? this.normalListNetState,
      blackVideoList: blackVideoList ?? this.blackVideoList,
      blackListNetState: blackListNetState ?? this.blackListNetState,
      playerStatus: playerStatus ?? this.playerStatus,
      winnerList: winnerList ?? this.winnerList,
    );
  }

  @override
  List<Object?> get props => [
        gameTypeList,
        sectionHeights,
        netState,
        isShowUnlockVideoBanner,
        mainTabController,
        jumpTabTitle,
        normalVideoList,
        normalListNetState,
        blackVideoList,
        blackListNetState,
        playerStatus,
        winnerList,
      ];
}
