import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/video_hot_movies_entity.dart';

VideoHotMoviesEntity $VideoHotMoviesEntityFromJson(Map<String, dynamic> json) {
  final VideoHotMoviesEntity videoHotMoviesEntity = VideoHotMoviesEntity();
  final Map<String, List<VideoHotMovies>>? list =
  (json['list'] as Map<String, dynamic>?)?.map(
          (k, e) =>
          MapEntry(k, (e as List<dynamic>)
              .map(
                  (e) =>
              jsonConvert.convert<VideoHotMovies>(e) as VideoHotMovies)
              .toList()));
  if (list != null) {
    videoHotMoviesEntity.list = list;
  }
  return videoHotMoviesEntity;
}

Map<String, dynamic> $VideoHotMoviesEntityToJson(VideoHotMoviesEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list;
  return data;
}

extension VideoHotMoviesEntityExtension on VideoHotMoviesEntity {
  VideoHotMoviesEntity copyWith({
    Map<String, List<VideoHotMovies>>? list,
  }) {
    return VideoHotMoviesEntity()
      ..list = list ?? this.list;
  }
}

VideoHotMovies $VideoHotMoviesFromJson(Map<String, dynamic> json) {
  final VideoHotMovies videoHotMovies = VideoHotMovies();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    videoHotMovies.id = id;
  }
  final String? videoImage = jsonConvert.convert<String>(json['videoImage']);
  if (videoImage != null) {
    videoHotMovies.videoImage = videoImage;
  }
  final String? videoTitle = jsonConvert.convert<String>(json['videoTitle']);
  if (videoTitle != null) {
    videoHotMovies.videoTitle = videoTitle;
  }
  final dynamic videoYear = json['videoYear'];
  if (videoYear != null) {
    videoHotMovies.videoYear = videoYear;
  }
  final String? videoCategoryCode = jsonConvert.convert<String>(
      json['videoCategoryCode']);
  if (videoCategoryCode != null) {
    videoHotMovies.videoCategoryCode = videoCategoryCode;
  }
  final String? videoCategory = jsonConvert.convert<String>(
      json['videoCategory']);
  if (videoCategory != null) {
    videoHotMovies.videoCategory = videoCategory;
  }
  final int? videoType = jsonConvert.convert<int>(json['videoType']);
  if (videoType != null) {
    videoHotMovies.videoType = videoType;
  }
  final String? videoTags = jsonConvert.convert<String>(json['videoTags']);
  if (videoTags != null) {
    videoHotMovies.videoTags = videoTags;
  }
  final dynamic videoCountry = json['videoCountry'];
  if (videoCountry != null) {
    videoHotMovies.videoCountry = videoCountry;
  }
  final String? videoClarity = jsonConvert.convert<String>(
      json['videoClarity']);
  if (videoClarity != null) {
    videoHotMovies.videoClarity = videoClarity;
  }
  final String? videoBottomTag = jsonConvert.convert<String>(
      json['videoBottomTag']);
  if (videoBottomTag != null) {
    videoHotMovies.videoBottomTag = videoBottomTag;
  }
  final int? playCount = jsonConvert.convert<int>(json['playCount']);
  if (playCount != null) {
    videoHotMovies.playCount = playCount;
  }
  final int? baseLikes = jsonConvert.convert<int>(json['baseLikes']);
  if (baseLikes != null) {
    videoHotMovies.baseLikes = baseLikes;
  }
  final int? hide = jsonConvert.convert<int>(json['hide']);
  if (hide != null) {
    videoHotMovies.hide = hide;
  }
  final dynamic createTime = json['createTime'];
  if (createTime != null) {
    videoHotMovies.createTime = createTime;
  }
  final dynamic thirdVideoId = json['thirdVideoId'];
  if (thirdVideoId != null) {
    videoHotMovies.thirdVideoId = thirdVideoId;
  }
  final int? isException = jsonConvert.convert<int>(json['isException']);
  if (isException != null) {
    videoHotMovies.isException = isException;
  }
  final int? isPinned = jsonConvert.convert<int>(json['isPinned']);
  if (isPinned != null) {
    videoHotMovies.isPinned = isPinned;
  }
  final dynamic videoTime = json['videoTime'];
  if (videoTime != null) {
    videoHotMovies.videoTime = videoTime;
  }
  return videoHotMovies;
}

Map<String, dynamic> $VideoHotMoviesToJson(VideoHotMovies entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['videoImage'] = entity.videoImage;
  data['videoTitle'] = entity.videoTitle;
  data['videoYear'] = entity.videoYear;
  data['videoCategoryCode'] = entity.videoCategoryCode;
  data['videoCategory'] = entity.videoCategory;
  data['videoType'] = entity.videoType;
  data['videoTags'] = entity.videoTags;
  data['videoCountry'] = entity.videoCountry;
  data['videoClarity'] = entity.videoClarity;
  data['videoBottomTag'] = entity.videoBottomTag;
  data['playCount'] = entity.playCount;
  data['baseLikes'] = entity.baseLikes;
  data['hide'] = entity.hide;
  data['createTime'] = entity.createTime;
  data['thirdVideoId'] = entity.thirdVideoId;
  data['isException'] = entity.isException;
  data['isPinned'] = entity.isPinned;
  data['videoTime'] = entity.videoTime;
  return data;
}

extension VideoHotMoviesExtension on VideoHotMovies {
  VideoHotMovies copyWith({
    int? id,
    String? videoImage,
    String? videoTitle,
    dynamic videoYear,
    String? videoCategoryCode,
    String? videoCategory,
    int? videoType,
    String? videoTags,
    dynamic videoCountry,
    String? videoClarity,
    String? videoBottomTag,
    int? playCount,
    int? baseLikes,
    int? hide,
    dynamic createTime,
    dynamic thirdVideoId,
    int? isException,
    int? isPinned,
    dynamic videoTime,
  }) {
    return VideoHotMovies()
      ..id = id ?? this.id
      ..videoImage = videoImage ?? this.videoImage
      ..videoTitle = videoTitle ?? this.videoTitle
      ..videoYear = videoYear ?? this.videoYear
      ..videoCategoryCode = videoCategoryCode ?? this.videoCategoryCode
      ..videoCategory = videoCategory ?? this.videoCategory
      ..videoType = videoType ?? this.videoType
      ..videoTags = videoTags ?? this.videoTags
      ..videoCountry = videoCountry ?? this.videoCountry
      ..videoClarity = videoClarity ?? this.videoClarity
      ..videoBottomTag = videoBottomTag ?? this.videoBottomTag
      ..playCount = playCount ?? this.playCount
      ..baseLikes = baseLikes ?? this.baseLikes
      ..hide = hide ?? this.hide
      ..createTime = createTime ?? this.createTime
      ..thirdVideoId = thirdVideoId ?? this.thirdVideoId
      ..isException = isException ?? this.isException
      ..isPinned = isPinned ?? this.isPinned
      ..videoTime = videoTime ?? this.videoTime;
  }
}