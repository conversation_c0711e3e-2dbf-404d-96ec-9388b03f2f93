import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/notice_entity.dart';

NoticeEntity $NoticeEntityFromJson(Map<String, dynamic> json) {
  final NoticeEntity noticeEntity = NoticeEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    noticeEntity.id = id;
  }
  final String? siteMessageTitle = jsonConvert.convert<String>(
      json['siteMessageTitle']);
  if (siteMessageTitle != null) {
    noticeEntity.siteMessageTitle = siteMessageTitle;
  }
  final String? siteMessageContent = jsonConvert.convert<String>(
      json['siteMessageContent']);
  if (siteMessageContent != null) {
    noticeEntity.siteMessageContent = siteMessageContent;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    noticeEntity.createTime = createTime;
  }
  final int? siteMessageRead = jsonConvert.convert<int>(
      json['siteMessageRead']);
  if (siteMessageRead != null) {
    noticeEntity.siteMessageRead = siteMessageRead;
  }
  return noticeEntity;
}

Map<String, dynamic> $NoticeEntityToJson(NoticeEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['siteMessageTitle'] = entity.siteMessageTitle;
  data['siteMessageContent'] = entity.siteMessageContent;
  data['createTime'] = entity.createTime;
  data['siteMessageRead'] = entity.siteMessageRead;
  return data;
}

extension NoticeEntityExtension on NoticeEntity {
  NoticeEntity copyWith({
    int? id,
    String? siteMessageTitle,
    String? siteMessageContent,
    String? createTime,
    int? siteMessageRead,
  }) {
    return NoticeEntity()
      ..id = id ?? this.id
      ..siteMessageTitle = siteMessageTitle ?? this.siteMessageTitle
      ..siteMessageContent = siteMessageContent ?? this.siteMessageContent
      ..createTime = createTime ?? this.createTime
      ..siteMessageRead = siteMessageRead ?? this.siteMessageRead;
  }
}