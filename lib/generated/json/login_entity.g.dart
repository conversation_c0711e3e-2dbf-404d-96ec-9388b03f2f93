import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/login_entity.dart';

LoginEntity $LoginEntityFromJson(Map<String, dynamic> json) {
  final LoginEntity loginEntity = LoginEntity();
  final String? token = jsonConvert.convert<String>(json['token']);
  if (token != null) {
    loginEntity.token = token;
  }
  final LoginTokenUser? tokenUser = jsonConvert.convert<LoginTokenUser>(
      json['tokenUser']);
  if (tokenUser != null) {
    loginEntity.tokenUser = tokenUser;
  }
  return loginEntity;
}

Map<String, dynamic> $LoginEntityToJson(LoginEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['token'] = entity.token;
  data['tokenUser'] = entity.tokenUser.toJson();
  return data;
}

extension LoginEntityExtension on LoginEntity {
  LoginEntity copyWith({
    String? token,
    LoginTokenUser? tokenUser,
  }) {
    return LoginEntity()
      ..token = token ?? this.token
      ..tokenUser = tokenUser ?? this.tokenUser;
  }
}

LoginTokenUser $LoginTokenUserFromJson(Map<String, dynamic> json) {
  final LoginTokenUser loginTokenUser = LoginTokenUser();
  final String? token = jsonConvert.convert<String>(json['token']);
  if (token != null) {
    loginTokenUser.token = token;
  }
  final int? userId = jsonConvert.convert<int>(json['userId']);
  if (userId != null) {
    loginTokenUser.userId = userId;
  }
  final String? userNo = jsonConvert.convert<String>(json['userNo']);
  if (userNo != null) {
    loginTokenUser.userNo = userNo;
  }
  final int? agentId = jsonConvert.convert<int>(json['agentId']);
  if (agentId != null) {
    loginTokenUser.agentId = agentId;
  }
  final int? generalAgentId = jsonConvert.convert<int>(json['generalAgentId']);
  if (generalAgentId != null) {
    loginTokenUser.generalAgentId = generalAgentId;
  }
  final int? userType = jsonConvert.convert<int>(json['userType']);
  if (userType != null) {
    loginTokenUser.userType = userType;
  }
  final int? enableTransferAmount = jsonConvert.convert<int>(
      json['enableTransferAmount']);
  if (enableTransferAmount != null) {
    loginTokenUser.enableTransferAmount = enableTransferAmount;
  }
  final int? initPwd = jsonConvert.convert<int>(json['initPwd']);
  if (initPwd != null) {
    loginTokenUser.initPwd = initPwd;
  }
  final String? operator = jsonConvert.convert<String>(json['operator']);
  if (operator != null) {
    loginTokenUser.operator = operator;
  }
  return loginTokenUser;
}

Map<String, dynamic> $LoginTokenUserToJson(LoginTokenUser entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['token'] = entity.token;
  data['userId'] = entity.userId;
  data['userNo'] = entity.userNo;
  data['agentId'] = entity.agentId;
  data['generalAgentId'] = entity.generalAgentId;
  data['userType'] = entity.userType;
  data['enableTransferAmount'] = entity.enableTransferAmount;
  data['initPwd'] = entity.initPwd;
  data['operator'] = entity.operator;
  return data;
}

extension LoginTokenUserExtension on LoginTokenUser {
  LoginTokenUser copyWith({
    String? token,
    int? userId,
    String? userNo,
    int? agentId,
    int? generalAgentId,
    int? userType,
    int? enableTransferAmount,
    int? initPwd,
    String? operator,
  }) {
    return LoginTokenUser()
      ..token = token ?? this.token
      ..userId = userId ?? this.userId
      ..userNo = userNo ?? this.userNo
      ..agentId = agentId ?? this.agentId
      ..generalAgentId = generalAgentId ?? this.generalAgentId
      ..userType = userType ?? this.userType
      ..enableTransferAmount = enableTransferAmount ?? this.enableTransferAmount
      ..initPwd = initPwd ?? this.initPwd
      ..operator = operator ?? this.operator;
  }
}