import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/payment_card_entity.dart';

PaymentCardEntityList $PaymentCardEntityListFromJson(
    Map<String, dynamic> json) {
  final PaymentCardEntityList paymentCardEntityList = PaymentCardEntityList();
  final List<PaymentCardEntity>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<PaymentCardEntity>(e) as PaymentCardEntity)
      .toList();
  if (list != null) {
    paymentCardEntityList.list = list;
  }
  return paymentCardEntityList;
}

Map<String, dynamic> $PaymentCardEntityListToJson(
    PaymentCardEntityList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension PaymentCardEntityListExtension on PaymentCardEntityList {
  PaymentCardEntityList copyWith({
    List<PaymentCardEntity>? list,
  }) {
    return PaymentCardEntityList()
      ..list = list ?? this.list;
  }
}

PaymentCardEntity $PaymentCardEntityFromJson(Map<String, dynamic> json) {
  final PaymentCardEntity paymentCardEntity = PaymentCardEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    paymentCardEntity.id = id;
  }
  final int? userId = jsonConvert.convert<int>(json['userId']);
  if (userId != null) {
    paymentCardEntity.userId = userId;
  }
  final String? userNo = jsonConvert.convert<String>(json['userNo']);
  if (userNo != null) {
    paymentCardEntity.userNo = userNo;
  }
  final String? bankCode = jsonConvert.convert<String>(json['bankCode']);
  if (bankCode != null) {
    paymentCardEntity.bankCode = bankCode;
  }
  final String? bankName = jsonConvert.convert<String>(json['bankName']);
  if (bankName != null) {
    paymentCardEntity.bankName = bankName;
  }
  final String? province = jsonConvert.convert<String>(json['province']);
  if (province != null) {
    paymentCardEntity.province = province;
  }
  final String? city = jsonConvert.convert<String>(json['city']);
  if (city != null) {
    paymentCardEntity.city = city;
  }
  final String? branchBankAddress = jsonConvert.convert<String>(
      json['branchBankAddress']);
  if (branchBankAddress != null) {
    paymentCardEntity.branchBankAddress = branchBankAddress;
  }
  final String? realName = jsonConvert.convert<String>(json['realName']);
  if (realName != null) {
    paymentCardEntity.realName = realName;
  }
  final String? cardNo = jsonConvert.convert<String>(json['cardNo']);
  if (cardNo != null) {
    paymentCardEntity.cardNo = cardNo;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    paymentCardEntity.createTime = createTime;
  }
  final int? withdrawnCount = jsonConvert.convert<int>(json['withdrawnCount']);
  if (withdrawnCount != null) {
    paymentCardEntity.withdrawnCount = withdrawnCount;
  }
  final int? useStatus = jsonConvert.convert<int>(json['useStatus']);
  if (useStatus != null) {
    paymentCardEntity.useStatus = useStatus;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    paymentCardEntity.type = type;
  }
  final dynamic mainNetwork = json['mainNetwork'];
  if (mainNetwork != null) {
    paymentCardEntity.mainNetwork = mainNetwork;
  }
  return paymentCardEntity;
}

Map<String, dynamic> $PaymentCardEntityToJson(PaymentCardEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['userId'] = entity.userId;
  data['userNo'] = entity.userNo;
  data['bankCode'] = entity.bankCode;
  data['bankName'] = entity.bankName;
  data['province'] = entity.province;
  data['city'] = entity.city;
  data['branchBankAddress'] = entity.branchBankAddress;
  data['realName'] = entity.realName;
  data['cardNo'] = entity.cardNo;
  data['createTime'] = entity.createTime;
  data['withdrawnCount'] = entity.withdrawnCount;
  data['useStatus'] = entity.useStatus;
  data['type'] = entity.type;
  data['mainNetwork'] = entity.mainNetwork;
  return data;
}

extension PaymentCardEntityExtension on PaymentCardEntity {
  PaymentCardEntity copyWith({
    int? id,
    int? userId,
    String? userNo,
    String? bankCode,
    String? bankName,
    String? province,
    String? city,
    String? branchBankAddress,
    String? realName,
    String? cardNo,
    String? createTime,
    int? withdrawnCount,
    int? useStatus,
    int? type,
    dynamic mainNetwork,
  }) {
    return PaymentCardEntity()
      ..id = id ?? this.id
      ..userId = userId ?? this.userId
      ..userNo = userNo ?? this.userNo
      ..bankCode = bankCode ?? this.bankCode
      ..bankName = bankName ?? this.bankName
      ..province = province ?? this.province
      ..city = city ?? this.city
      ..branchBankAddress = branchBankAddress ?? this.branchBankAddress
      ..realName = realName ?? this.realName
      ..cardNo = cardNo ?? this.cardNo
      ..createTime = createTime ?? this.createTime
      ..withdrawnCount = withdrawnCount ?? this.withdrawnCount
      ..useStatus = useStatus ?? this.useStatus
      ..type = type ?? this.type
      ..mainNetwork = mainNetwork ?? this.mainNetwork;
  }
}