import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/my_team_entity.dart';

MyTeamEntity $MyTeamEntityFromJson(Map<String, dynamic> json) {
  final MyTeamEntity myTeamEntity = MyTeamEntity();
  final int? teamSize = jsonConvert.convert<int>(json['teamSize']);
  if (teamSize != null) {
    myTeamEntity.teamSize = teamSize;
  }
  final double? teamBetAmount = jsonConvert.convert<double>(
      json['teamBetAmount']);
  if (teamBetAmount != null) {
    myTeamEntity.teamBetAmount = teamBetAmount;
  }
  final double? teamCashinAmount = jsonConvert.convert<double>(
      json['teamCashinAmount']);
  if (teamCashinAmount != null) {
    myTeamEntity.teamCashinAmount = teamCashinAmount;
  }
  return myTeamEntity;
}

Map<String, dynamic> $MyTeamEntityToJson(MyTeamEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['teamSize'] = entity.teamSize;
  data['teamBetAmount'] = entity.teamBetAmount;
  data['teamCashinAmount'] = entity.teamCashinAmount;
  return data;
}

extension MyTeamEntityExtension on MyTeamEntity {
  MyTeamEntity copyWith({
    int? teamSize,
    double? teamBetAmount,
    double? teamCashinAmount,
  }) {
    return MyTeamEntity()
      ..teamSize = teamSize ?? this.teamSize
      ..teamBetAmount = teamBetAmount ?? this.teamBetAmount
      ..teamCashinAmount = teamCashinAmount ?? this.teamCashinAmount;
  }
}