import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/short_video_entity.dart';
import 'package:flutter/foundation.dart';


ShortVideoEntityList $ShortVideoEntityListFromJson(Map<String, dynamic> json) {
  final ShortVideoEntityList shortVideoEntityList = ShortVideoEntityList();
  final List<ShortVideoEntity>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<ShortVideoEntity>(e) as ShortVideoEntity)
      .toList();
  if (list != null) {
    shortVideoEntityList.list = list;
  }
  return shortVideoEntityList;
}

Map<String, dynamic> $ShortVideoEntityListToJson(ShortVideoEntityList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension ShortVideoEntityListExtension on ShortVideoEntityList {
  ShortVideoEntityList copyWith({
    List<ShortVideoEntity>? list,
  }) {
    return ShortVideoEntityList()
      ..list = list ?? this.list;
  }
}

ShortVideoEntity $ShortVideoEntityFromJson(Map<String, dynamic> json) {
  final ShortVideoEntity shortVideoEntity = ShortVideoEntity();
  final int? videoId = jsonConvert.convert<int>(json['videoId']);
  if (videoId != null) {
    shortVideoEntity.videoId = videoId;
  }
  final String? videoTitle = jsonConvert.convert<String>(json['videoTitle']);
  if (videoTitle != null) {
    shortVideoEntity.videoTitle = videoTitle;
  }
  final String? lineTitle = jsonConvert.convert<String>(json['lineTitle']);
  if (lineTitle != null) {
    shortVideoEntity.lineTitle = lineTitle;
  }
  final String? lineUrl = jsonConvert.convert<String>(json['lineUrl']);
  if (lineUrl != null) {
    shortVideoEntity.lineUrl = lineUrl;
  }
  final String? videoImage = jsonConvert.convert<String>(json['videoImage']);
  if (videoImage != null) {
    shortVideoEntity.videoImage = videoImage;
  }
  final int? likes = jsonConvert.convert<int>(json['likes']);
  if (likes != null) {
    shortVideoEntity.likes = likes;
  }
  final int? videoTime = jsonConvert.convert<int>(json['videoTime']);
  if (videoTime != null) {
    shortVideoEntity.videoTime = videoTime;
  }
  final bool? isNormal = jsonConvert.convert<bool>(json['isNormal']);
  if (isNormal != null) {
    shortVideoEntity.isNormal = isNormal;
  }
  final String? testIndex = jsonConvert.convert<String>(json['testIndex']);
  if (testIndex != null) {
    shortVideoEntity.testIndex = testIndex;
  }
  final bool? isPlaying = jsonConvert.convert<bool>(json['isPlaying']);
  if (isPlaying != null) {
    shortVideoEntity.isPlaying = isPlaying;
  }
  return shortVideoEntity;
}

Map<String, dynamic> $ShortVideoEntityToJson(ShortVideoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['videoId'] = entity.videoId;
  data['videoTitle'] = entity.videoTitle;
  data['lineTitle'] = entity.lineTitle;
  data['lineUrl'] = entity.lineUrl;
  data['videoImage'] = entity.videoImage;
  data['likes'] = entity.likes;
  data['videoTime'] = entity.videoTime;
  data['isNormal'] = entity.isNormal;
  data['testIndex'] = entity.testIndex;
  data['isPlaying'] = entity.isPlaying;
  return data;
}

extension ShortVideoEntityExtension on ShortVideoEntity {
  ShortVideoEntity copyWith({
    int? videoId,
    String? videoTitle,
    String? lineTitle,
    String? lineUrl,
    String? videoImage,
    int? likes,
    int? videoTime,
    bool? isNormal,
    String? testIndex,
    bool? isPlaying,
    Duration? duration,
    Duration? currentDuration,
    ValueNotifier<bool>? isLikedNotifier,
  }) {
    return ShortVideoEntity()
      ..videoId = videoId ?? this.videoId
      ..videoTitle = videoTitle ?? this.videoTitle
      ..lineTitle = lineTitle ?? this.lineTitle
      ..lineUrl = lineUrl ?? this.lineUrl
      ..videoImage = videoImage ?? this.videoImage
      ..likes = likes ?? this.likes
      ..videoTime = videoTime ?? this.videoTime
      ..isNormal = isNormal ?? this.isNormal
      ..testIndex = testIndex ?? this.testIndex
      ..isPlaying = isPlaying ?? this.isPlaying
      ..duration = duration ?? this.duration
      ..currentDuration = currentDuration ?? this.currentDuration
      ..isLikedNotifier = isLikedNotifier ?? this.isLikedNotifier;
  }
}