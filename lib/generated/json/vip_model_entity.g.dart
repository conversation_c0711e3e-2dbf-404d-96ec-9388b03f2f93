import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/vip_model_entity.dart';

VipModelListEntity $VipModelListEntityFromJson(Map<String, dynamic> json) {
  final VipModelListEntity vipModelListEntity = VipModelListEntity();
  final List<VipModel>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<VipModel>(e) as VipModel).toList();
  if (list != null) {
    vipModelListEntity.list = list;
  }
  return vipModelListEntity;
}

Map<String, dynamic> $VipModelListEntityToJson(VipModelListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension VipModelListEntityExtension on VipModelListEntity {
  VipModelListEntity copyWith({
    List<VipModel>? list,
  }) {
    return VipModelListEntity()
      ..list = list ?? this.list;
  }
}

VipModel $VipModelFromJson(Map<String, dynamic> json) {
  final VipModel vipModel = VipModel();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    vipModel.id = id;
  }
  final int? vipLevel = jsonConvert.convert<int>(json['vipLevel']);
  if (vipLevel != null) {
    vipModel.vipLevel = vipLevel;
  }
  final String? vipTitle = jsonConvert.convert<String>(json['vipTitle']);
  if (vipTitle != null) {
    vipModel.vipTitle = vipTitle;
  }
  final int? growthIntegral = jsonConvert.convert<int>(json['growthIntegral']);
  if (growthIntegral != null) {
    vipModel.growthIntegral = growthIntegral;
  }
  final double? upgradeReward = jsonConvert.convert<double>(
      json['upgradeReward']);
  if (upgradeReward != null) {
    vipModel.upgradeReward = upgradeReward;
  }
  final double? skipgradeReward = jsonConvert.convert<double>(
      json['skipgradeReward']);
  if (skipgradeReward != null) {
    vipModel.skipgradeReward = skipgradeReward;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    vipModel.createTime = createTime;
  }
  return vipModel;
}

Map<String, dynamic> $VipModelToJson(VipModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['vipLevel'] = entity.vipLevel;
  data['vipTitle'] = entity.vipTitle;
  data['growthIntegral'] = entity.growthIntegral;
  data['upgradeReward'] = entity.upgradeReward;
  data['skipgradeReward'] = entity.skipgradeReward;
  data['createTime'] = entity.createTime;
  return data;
}

extension VipModelExtension on VipModel {
  VipModel copyWith({
    int? id,
    int? vipLevel,
    String? vipTitle,
    int? growthIntegral,
    double? upgradeReward,
    double? skipgradeReward,
    String? createTime,
  }) {
    return VipModel()
      ..id = id ?? this.id
      ..vipLevel = vipLevel ?? this.vipLevel
      ..vipTitle = vipTitle ?? this.vipTitle
      ..growthIntegral = growthIntegral ?? this.growthIntegral
      ..upgradeReward = upgradeReward ?? this.upgradeReward
      ..skipgradeReward = skipgradeReward ?? this.skipgradeReward
      ..createTime = createTime ?? this.createTime;
  }
}