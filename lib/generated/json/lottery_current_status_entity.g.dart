import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/lottery_current_status_entity.dart';

LotteryCurrentStatusEntity $LotteryCurrentStatusEntityFromJson(
    Map<String, dynamic> json) {
  final LotteryCurrentStatusEntity lotteryCurrentStatusEntity = LotteryCurrentStatusEntity();
  final dynamic todayWin = json['todayWin'];
  if (todayWin != null) {
    lotteryCurrentStatusEntity.todayWin = todayWin;
  }
  final String? close = jsonConvert.convert<String>(json['close']);
  if (close != null) {
    lotteryCurrentStatusEntity.close = close;
  }
  final String? open = jsonConvert.convert<String>(json['open']);
  if (open != null) {
    lotteryCurrentStatusEntity.open = open;
  }
  final String? order = jsonConvert.convert<String>(json['order']);
  if (order != null) {
    lotteryCurrentStatusEntity.order = order;
  }
  final String? serverDate = jsonConvert.convert<String>(json['serverDate']);
  if (serverDate != null) {
    lotteryCurrentStatusEntity.serverDate = serverDate;
  }
  final int? rebate = jsonConvert.convert<int>(json['rebate']);
  if (rebate != null) {
    lotteryCurrentStatusEntity.rebate = rebate;
  }
  final bool? enable = jsonConvert.convert<bool>(json['enable']);
  if (enable != null) {
    lotteryCurrentStatusEntity.enable = enable;
  }
  final int? countDown = jsonConvert.convert<int>(json['countDown']);
  if (countDown != null) {
    lotteryCurrentStatusEntity.countDown = countDown;
  }
  final bool? start = jsonConvert.convert<bool>(json['start']);
  if (start != null) {
    lotteryCurrentStatusEntity.start = start;
  }
  return lotteryCurrentStatusEntity;
}

Map<String, dynamic> $LotteryCurrentStatusEntityToJson(
    LotteryCurrentStatusEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['todayWin'] = entity.todayWin;
  data['close'] = entity.close;
  data['open'] = entity.open;
  data['order'] = entity.order;
  data['serverDate'] = entity.serverDate;
  data['rebate'] = entity.rebate;
  data['enable'] = entity.enable;
  data['countDown'] = entity.countDown;
  data['start'] = entity.start;
  return data;
}

extension LotteryCurrentStatusEntityExtension on LotteryCurrentStatusEntity {
  LotteryCurrentStatusEntity copyWith({
    dynamic todayWin,
    String? close,
    String? open,
    String? order,
    String? serverDate,
    int? rebate,
    bool? enable,
    int? countDown,
    bool? start,
  }) {
    return LotteryCurrentStatusEntity()
      ..todayWin = todayWin ?? this.todayWin
      ..close = close ?? this.close
      ..open = open ?? this.open
      ..order = order ?? this.order
      ..serverDate = serverDate ?? this.serverDate
      ..rebate = rebate ?? this.rebate
      ..enable = enable ?? this.enable
      ..countDown = countDown ?? this.countDown
      ..start = start ?? this.start;
  }
}