import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/team_entity.dart';

TeamEntity $TeamEntityFromJson(Map<String, dynamic> json) {
  final TeamEntity teamEntity = TeamEntity();
  final int? userTeamLevel = jsonConvert.convert<int>(json['userTeamLevel']);
  if (userTeamLevel != null) {
    teamEntity.userTeamLevel = userTeamLevel;
  }
  final List<
      TeamTeamConfigList>? teamConfigList = (json['teamConfigList'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<TeamTeamConfigList>(e) as TeamTeamConfigList)
      .toList();
  if (teamConfigList != null) {
    teamEntity.teamConfigList = teamConfigList;
  }
  return teamEntity;
}

Map<String, dynamic> $TeamEntityToJson(TeamEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['userTeamLevel'] = entity.userTeamLevel;
  data['teamConfigList'] =
      entity.teamConfigList?.map((v) => v.toJson()).toList();
  return data;
}

extension TeamEntityExtension on TeamEntity {
  TeamEntity copyWith({
    int? userTeamLevel,
    List<TeamTeamConfigList>? teamConfigList,
  }) {
    return TeamEntity()
      ..userTeamLevel = userTeamLevel ?? this.userTeamLevel
      ..teamConfigList = teamConfigList ?? this.teamConfigList;
  }
}

TeamTeamConfigList $TeamTeamConfigListFromJson(Map<String, dynamic> json) {
  final TeamTeamConfigList teamTeamConfigList = TeamTeamConfigList();
  final int? teamLevel = jsonConvert.convert<int>(json['teamLevel']);
  if (teamLevel != null) {
    teamTeamConfigList.teamLevel = teamLevel;
  }
  final double? betRebate = jsonConvert.convert<double>(json['betRebate']);
  if (betRebate != null) {
    teamTeamConfigList.betRebate = betRebate;
  }
  final double? cashinRebate = jsonConvert.convert<double>(
      json['cashinRebate']);
  if (cashinRebate != null) {
    teamTeamConfigList.cashinRebate = cashinRebate;
  }
  final double? maxAmount = jsonConvert.convert<double>(json['maxAmount']);
  if (maxAmount != null) {
    teamTeamConfigList.maxAmount = maxAmount;
  }
  return teamTeamConfigList;
}

Map<String, dynamic> $TeamTeamConfigListToJson(TeamTeamConfigList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['teamLevel'] = entity.teamLevel;
  data['betRebate'] = entity.betRebate;
  data['cashinRebate'] = entity.cashinRebate;
  data['maxAmount'] = entity.maxAmount;
  return data;
}

extension TeamTeamConfigListExtension on TeamTeamConfigList {
  TeamTeamConfigList copyWith({
    int? teamLevel,
    double? betRebate,
    double? cashinRebate,
    double? maxAmount,
  }) {
    return TeamTeamConfigList()
      ..teamLevel = teamLevel ?? this.teamLevel
      ..betRebate = betRebate ?? this.betRebate
      ..cashinRebate = cashinRebate ?? this.cashinRebate
      ..maxAmount = maxAmount ?? this.maxAmount;
  }
}