import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/home_banner_entity.dart';

HomeBannerListEntity $HomeBannerListEntityFromJson(Map<String, dynamic> json) {
  final HomeBannerListEntity homeBannerListEntity = HomeBannerListEntity();
  final List<HomeBannerEntity>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<HomeBannerEntity>(e) as HomeBannerEntity)
      .toList();
  if (list != null) {
    homeBannerListEntity.list = list;
  }
  return homeBannerListEntity;
}

Map<String, dynamic> $HomeBannerListEntityToJson(HomeBannerListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension HomeBannerListEntityExtension on HomeBannerListEntity {
  HomeBannerListEntity copyWith({
    List<HomeBannerEntity>? list,
  }) {
    return HomeBannerListEntity()
      ..list = list ?? this.list;
  }
}

HomeBannerEntity $HomeBannerEntityFromJson(Map<String, dynamic> json) {
  final HomeBannerEntity homeBannerEntity = HomeBannerEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    homeBannerEntity.id = id;
  }
  final String? bannerUrl = jsonConvert.convert<String>(json['bannerUrl']);
  if (bannerUrl != null) {
    homeBannerEntity.bannerUrl = bannerUrl;
  }
  final int? bannerSort = jsonConvert.convert<int>(json['bannerSort']);
  if (bannerSort != null) {
    homeBannerEntity.bannerSort = bannerSort;
  }
  final int? jumpStatus = jsonConvert.convert<int>(json['jumpStatus']);
  if (jumpStatus != null) {
    homeBannerEntity.jumpStatus = jumpStatus;
  }
  final String? jumpUrl = jsonConvert.convert<String>(json['jumpUrl']);
  if (jumpUrl != null) {
    homeBannerEntity.jumpUrl = jumpUrl;
  }
  final int? gameBelong = jsonConvert.convert<int>(json['gameBelong']);
  if (gameBelong != null) {
    homeBannerEntity.gameBelong = gameBelong;
  }
  final int? venueId = jsonConvert.convert<int>(json['venueId']);
  if (venueId != null) {
    homeBannerEntity.venueId = venueId;
  }
  final String? platformCode = jsonConvert.convert<String>(
      json['platformCode']);
  if (platformCode != null) {
    homeBannerEntity.platformCode = platformCode;
  }
  final int? gameId = jsonConvert.convert<int>(json['gameId']);
  if (gameId != null) {
    homeBannerEntity.gameId = gameId;
  }
  return homeBannerEntity;
}

Map<String, dynamic> $HomeBannerEntityToJson(HomeBannerEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['bannerUrl'] = entity.bannerUrl;
  data['bannerSort'] = entity.bannerSort;
  data['jumpStatus'] = entity.jumpStatus;
  data['jumpUrl'] = entity.jumpUrl;
  data['gameBelong'] = entity.gameBelong;
  data['venueId'] = entity.venueId;
  data['platformCode'] = entity.platformCode;
  data['gameId'] = entity.gameId;
  return data;
}

extension HomeBannerEntityExtension on HomeBannerEntity {
  HomeBannerEntity copyWith({
    int? id,
    String? bannerUrl,
    int? bannerSort,
    int? jumpStatus,
    String? jumpUrl,
    int? gameBelong,
    int? venueId,
    String? platformCode,
    int? gameId,
  }) {
    return HomeBannerEntity()
      ..id = id ?? this.id
      ..bannerUrl = bannerUrl ?? this.bannerUrl
      ..bannerSort = bannerSort ?? this.bannerSort
      ..jumpStatus = jumpStatus ?? this.jumpStatus
      ..jumpUrl = jumpUrl ?? this.jumpUrl
      ..gameBelong = gameBelong ?? this.gameBelong
      ..venueId = venueId ?? this.venueId
      ..platformCode = platformCode ?? this.platformCode
      ..gameId = gameId ?? this.gameId;
  }
}