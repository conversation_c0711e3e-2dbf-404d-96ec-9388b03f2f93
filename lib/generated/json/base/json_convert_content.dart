// ignore_for_file: non_constant_identifier_names
// ignore_for_file: camel_case_types
// ignore_for_file: prefer_single_quotes

// This file is automatically generated. DO NOT EDIT, all your changes would be lost.
import 'package:flutter/material.dart' show debugPrint;
import 'package:game_store/core/models/entities/activity_category_entity.dart';
import 'package:game_store/core/models/entities/activity_list_entity.dart';
import 'package:game_store/core/models/entities/activity_task_record_entity.dart';
import 'package:game_store/core/models/entities/app_version_entity.dart';
import 'package:game_store/core/models/entities/bank_entity.dart';
import 'package:game_store/core/models/entities/bet_record_entity.dart';
import 'package:game_store/core/models/entities/bonus_pool_entity.dart';
import 'package:game_store/core/models/entities/chat_service_online_entity.dart';
import 'package:game_store/core/models/entities/chat_type_entity.dart';
import 'package:game_store/core/models/entities/commission_bet_entity.dart';
import 'package:game_store/core/models/entities/commission_details_entity.dart';
import 'package:game_store/core/models/entities/commission_overview_entity.dart';
import 'package:game_store/core/models/entities/commission_recharge_entity.dart';
import 'package:game_store/core/models/entities/commission_record_entity.dart';
import 'package:game_store/core/models/entities/commit_top_up_entity.dart';
import 'package:game_store/core/models/entities/customer_service_config_entity.dart';
import 'package:game_store/core/models/entities/daily_check_in_entity.dart';
import 'package:game_store/core/models/entities/daily_sign_in_list_entity.dart';
import 'package:game_store/core/models/entities/filter_video_list_entity.dart';
import 'package:game_store/core/models/entities/game_entity.dart';
import 'package:game_store/core/models/entities/game_login_entity.dart';
import 'package:game_store/core/models/entities/game_notice_entity.dart';
import 'package:game_store/core/models/entities/home_banner_entity.dart';
import 'package:game_store/core/models/entities/home_custom_entity.dart';
import 'package:game_store/core/models/entities/image_captcha_entity.dart';
import 'package:game_store/core/models/entities/invite_code_entity.dart';
import 'package:game_store/core/models/entities/jump_chat_pay_entity.dart';
import 'package:game_store/core/models/entities/login_entity.dart';
import 'package:game_store/core/models/entities/lottery_cart_item_entity.dart';
import 'package:game_store/core/models/entities/lottery_current_status_entity.dart';
import 'package:game_store/core/models/entities/lottery_entity.dart';
import 'package:game_store/core/models/entities/lottery_odds_entity.dart';
import 'package:game_store/core/models/entities/lottery_result_entity.dart';
import 'package:game_store/core/models/entities/match_result_entity.dart';
import 'package:game_store/core/models/entities/my_team_entity.dart';
import 'package:game_store/core/models/entities/notice_entity.dart';
import 'package:game_store/core/models/entities/notification_alert_entity.dart';
import 'package:game_store/core/models/entities/notifications_response_entity.dart';
import 'package:game_store/core/models/entities/order_channel_list_entity.dart';
import 'package:game_store/core/models/entities/order_main_entity.dart';
import 'package:game_store/core/models/entities/payment_card_entity.dart';
import 'package:game_store/core/models/entities/platform_amount_entity.dart';
import 'package:game_store/core/models/entities/popular_and_collection_game_entity.dart';
import 'package:game_store/core/models/entities/promotion_banner_entity.dart';
import 'package:game_store/core/models/entities/short_video_entity.dart';
import 'package:game_store/core/models/entities/statement_entity.dart';
import 'package:game_store/core/models/entities/statement_filter_way_entity.dart';
import 'package:game_store/core/models/entities/subordinate_info_entity.dart';
import 'package:game_store/core/models/entities/system_config_entity.dart';
import 'package:game_store/core/models/entities/tc_sdk_config_entity.dart';
import 'package:game_store/core/models/entities/team_details_entity.dart';
import 'package:game_store/core/models/entities/team_entity.dart';
import 'package:game_store/core/models/entities/team_members_entity.dart';
import 'package:game_store/core/models/entities/third_party_wallet_info_entity.dart';
import 'package:game_store/core/models/entities/top_up_list_entity.dart';
import 'package:game_store/core/models/entities/top_up_record_entity.dart';
import 'package:game_store/core/models/entities/user_info_entity.dart';
import 'package:game_store/core/models/entities/user_vip_entity.dart';
import 'package:game_store/core/models/entities/video_entity.dart';
import 'package:game_store/core/models/entities/video_filter_entity.dart';
import 'package:game_store/core/models/entities/video_hot_movies_entity.dart';
import 'package:game_store/core/models/entities/video_hot_tag_entity.dart';
import 'package:game_store/core/models/entities/video_list_entity.dart';
import 'package:game_store/core/models/entities/vip_model_entity.dart';
import 'package:game_store/core/models/entities/winner_entity.dart';
import 'package:game_store/core/models/entities/winning_entity.dart';
import 'package:game_store/core/models/entities/withdraw_record_entity.dart';
import 'package:game_store/core/models/entities/withdraw_user_bank_list_entity.dart';

JsonConvert jsonConvert = JsonConvert();

typedef JsonConvertFunction<T> = T Function(Map<String, dynamic> json);
typedef EnumConvertFunction<T> = T Function(String value);
typedef ConvertExceptionHandler = void Function(Object error, StackTrace stackTrace);
extension MapSafeExt<K, V> on Map<K, V> {
  T? getOrNull<T>(K? key) {
    if (!containsKey(key) || key == null) {
      return null;
    } else {
      return this[key] as T?;
    }
  }
}

class JsonConvert {
  static ConvertExceptionHandler? onError;
  JsonConvertClassCollection convertFuncMap = JsonConvertClassCollection();

  /// When you are in the development, to generate a new model class, hot-reload doesn't find new generation model class, you can build on MaterialApp method called jsonConvert. ReassembleConvertFuncMap (); This method only works in a development environment
  /// https://flutter.cn/docs/development/tools/hot-reload
  /// class MyApp extends StatelessWidget {
  ///    const MyApp({Key? key})
  ///        : super(key: key);
  ///
  ///    @override
  ///    Widget build(BuildContext context) {
  ///      jsonConvert.reassembleConvertFuncMap();
  ///      return MaterialApp();
  ///    }
  /// }
  void reassembleConvertFuncMap() {
    bool isReleaseMode = const bool.fromEnvironment('dart.vm.product');
    if (!isReleaseMode) {
      convertFuncMap = JsonConvertClassCollection();
    }
  }

  T? convert<T>(dynamic value, {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    if (value is T) {
      return value;
    }
    try {
      return _asT<T>(value, enumConvert: enumConvert);
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return null;
    }
  }

  List<T?>? convertList<T>(List<dynamic>? value,
      {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return value.map((dynamic e) => _asT<T>(e, enumConvert: enumConvert))
          .toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  List<T>? convertListNotNull<T>(dynamic value,
      {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return (value as List<dynamic>).map((dynamic e) =>
      _asT<T>(e, enumConvert: enumConvert)!).toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  T? _asT<T extends Object?>(dynamic value,
      {EnumConvertFunction? enumConvert}) {
    final String type = T.toString();
    final String valueS = value.toString();
    if (enumConvert != null) {
      return enumConvert(valueS) as T;
    } else if (type == "String") {
      return valueS as T;
    } else if (type == "int") {
      final int? intValue = int.tryParse(valueS);
      if (intValue == null) {
        return double.tryParse(valueS)?.toInt() as T?;
      } else {
        return intValue as T;
      }
    } else if (type == "double") {
      return double.parse(valueS) as T;
    } else if (type == "DateTime") {
      return DateTime.parse(valueS) as T;
    } else if (type == "bool") {
      if (valueS == '0' || valueS == '1') {
        return (valueS == '1') as T;
      }
      return (valueS == 'true') as T;
    } else if (type == "Map" || type.startsWith("Map<")) {
      return value as T;
    } else {
      if (convertFuncMap.containsKey(type)) {
        if (value == null) {
          return null;
        }
        var covertFunc = convertFuncMap[type]!;
        if (covertFunc is Map<String, dynamic>) {
          return covertFunc(value as Map<String, dynamic>) as T;
        } else {
          return covertFunc(Map<String, dynamic>.from(value)) as T;
        }
      } else {
        throw UnimplementedError(
            '$type unimplemented,you can try running the app again');
      }
    }
  }

  //list is returned by type
  static M? _getListChildType<M>(List<Map<String, dynamic>> data) {
    if (<ActivityCategoryListEntity>[] is M) {
      return data.map<ActivityCategoryListEntity>((Map<String, dynamic> e) =>
          ActivityCategoryListEntity.fromJson(e)).toList() as M;
    }
    if (<ActivityCategory>[] is M) {
      return data.map<ActivityCategory>((Map<String, dynamic> e) =>
          ActivityCategory.fromJson(e)).toList() as M;
    }
    if (<ActivityListEntity>[] is M) {
      return data.map<ActivityListEntity>((Map<String, dynamic> e) =>
          ActivityListEntity.fromJson(e)).toList() as M;
    }
    if (<ActivityRecords>[] is M) {
      return data.map<ActivityRecords>((Map<String, dynamic> e) =>
          ActivityRecords.fromJson(e)).toList() as M;
    }
    if (<ActivityTaskListEntity>[] is M) {
      return data.map<ActivityTaskListEntity>((Map<String, dynamic> e) =>
          ActivityTaskListEntity.fromJson(e)).toList() as M;
    }
    if (<ActivityTask>[] is M) {
      return data.map<ActivityTask>((Map<String, dynamic> e) =>
          ActivityTask.fromJson(e)).toList() as M;
    }
    if (<ActivityTaskRecordEntity>[] is M) {
      return data.map<ActivityTaskRecordEntity>((Map<String, dynamic> e) =>
          ActivityTaskRecordEntity.fromJson(e)).toList() as M;
    }
    if (<ActivityTaskRecord>[] is M) {
      return data.map<ActivityTaskRecord>((Map<String, dynamic> e) =>
          ActivityTaskRecord.fromJson(e)).toList() as M;
    }
    if (<AppVersionEntity>[] is M) {
      return data.map<AppVersionEntity>((Map<String, dynamic> e) =>
          AppVersionEntity.fromJson(e)).toList() as M;
    }
    if (<BankEntityList>[] is M) {
      return data.map<BankEntityList>((Map<String, dynamic> e) =>
          BankEntityList.fromJson(e)).toList() as M;
    }
    if (<BankEntity>[] is M) {
      return data.map<BankEntity>((Map<String, dynamic> e) =>
          BankEntity.fromJson(e)).toList() as M;
    }
    if (<BetRecordEntity>[] is M) {
      return data.map<BetRecordEntity>((Map<String, dynamic> e) =>
          BetRecordEntity.fromJson(e)).toList() as M;
    }
    if (<BetRecordPage>[] is M) {
      return data.map<BetRecordPage>((Map<String, dynamic> e) =>
          BetRecordPage.fromJson(e)).toList() as M;
    }
    if (<BetRecordPageRecords>[] is M) {
      return data.map<BetRecordPageRecords>((Map<String, dynamic> e) =>
          BetRecordPageRecords.fromJson(e)).toList() as M;
    }
    if (<BetRecordPageOrders>[] is M) {
      return data.map<BetRecordPageOrders>((Map<String, dynamic> e) =>
          BetRecordPageOrders.fromJson(e)).toList() as M;
    }
    if (<BetRecordPageOptimizeCountSql>[] is M) {
      return data.map<BetRecordPageOptimizeCountSql>((Map<String, dynamic> e) =>
          BetRecordPageOptimizeCountSql.fromJson(e)).toList() as M;
    }
    if (<BetRecordPageSearchCount>[] is M) {
      return data.map<BetRecordPageSearchCount>((Map<String, dynamic> e) =>
          BetRecordPageSearchCount.fromJson(e)).toList() as M;
    }
    if (<BonusPoolEntity>[] is M) {
      return data.map<BonusPoolEntity>((Map<String, dynamic> e) =>
          BonusPoolEntity.fromJson(e)).toList() as M;
    }
    if (<ChatServiceOnlineList>[] is M) {
      return data.map<ChatServiceOnlineList>((Map<String, dynamic> e) =>
          ChatServiceOnlineList.fromJson(e)).toList() as M;
    }
    if (<ChatServiceOnlineEntity>[] is M) {
      return data.map<ChatServiceOnlineEntity>((Map<String, dynamic> e) =>
          ChatServiceOnlineEntity.fromJson(e)).toList() as M;
    }
    if (<ChatTypeEntity>[] is M) {
      return data.map<ChatTypeEntity>((Map<String, dynamic> e) =>
          ChatTypeEntity.fromJson(e)).toList() as M;
    }
    if (<CommissionBetEntity>[] is M) {
      return data.map<CommissionBetEntity>((Map<String, dynamic> e) =>
          CommissionBetEntity.fromJson(e)).toList() as M;
    }
    if (<CommissionBetList>[] is M) {
      return data.map<CommissionBetList>((Map<String, dynamic> e) =>
          CommissionBetList.fromJson(e)).toList() as M;
    }
    if (<CommissionBetListThirdList>[] is M) {
      return data.map<CommissionBetListThirdList>((Map<String, dynamic> e) =>
          CommissionBetListThirdList.fromJson(e)).toList() as M;
    }
    if (<CommissionBetListThirdListList>[] is M) {
      return data.map<CommissionBetListThirdListList>((
          Map<String, dynamic> e) => CommissionBetListThirdListList.fromJson(e))
          .toList() as M;
    }
    if (<CommissionDetailsEntity>[] is M) {
      return data.map<CommissionDetailsEntity>((Map<String, dynamic> e) =>
          CommissionDetailsEntity.fromJson(e)).toList() as M;
    }
    if (<CommissionDetailsRecords>[] is M) {
      return data.map<CommissionDetailsRecords>((Map<String, dynamic> e) =>
          CommissionDetailsRecords.fromJson(e)).toList() as M;
    }
    if (<CommissionDetailsOrders>[] is M) {
      return data.map<CommissionDetailsOrders>((Map<String, dynamic> e) =>
          CommissionDetailsOrders.fromJson(e)).toList() as M;
    }
    if (<CommissionDetailsOptimizeCountSql>[] is M) {
      return data.map<CommissionDetailsOptimizeCountSql>((
          Map<String, dynamic> e) =>
          CommissionDetailsOptimizeCountSql.fromJson(e)).toList() as M;
    }
    if (<CommissionDetailsSearchCount>[] is M) {
      return data.map<CommissionDetailsSearchCount>((Map<String, dynamic> e) =>
          CommissionDetailsSearchCount.fromJson(e)).toList() as M;
    }
    if (<CommissionOverviewEntity>[] is M) {
      return data.map<CommissionOverviewEntity>((Map<String, dynamic> e) =>
          CommissionOverviewEntity.fromJson(e)).toList() as M;
    }
    if (<CommissionRechargeEntity>[] is M) {
      return data.map<CommissionRechargeEntity>((Map<String, dynamic> e) =>
          CommissionRechargeEntity.fromJson(e)).toList() as M;
    }
    if (<CommissionRechargeList>[] is M) {
      return data.map<CommissionRechargeList>((Map<String, dynamic> e) =>
          CommissionRechargeList.fromJson(e)).toList() as M;
    }
    if (<CommissionRecordEntity>[] is M) {
      return data.map<CommissionRecordEntity>((Map<String, dynamic> e) =>
          CommissionRecordEntity.fromJson(e)).toList() as M;
    }
    if (<CommissionRecordList>[] is M) {
      return data.map<CommissionRecordList>((Map<String, dynamic> e) =>
          CommissionRecordList.fromJson(e)).toList() as M;
    }
    if (<CommissionRecordOrders>[] is M) {
      return data.map<CommissionRecordOrders>((Map<String, dynamic> e) =>
          CommissionRecordOrders.fromJson(e)).toList() as M;
    }
    if (<CommissionRecordOptimizeCountSql>[] is M) {
      return data.map<CommissionRecordOptimizeCountSql>((
          Map<String, dynamic> e) =>
          CommissionRecordOptimizeCountSql.fromJson(e)).toList() as M;
    }
    if (<CommissionRecordSearchCount>[] is M) {
      return data.map<CommissionRecordSearchCount>((Map<String, dynamic> e) =>
          CommissionRecordSearchCount.fromJson(e)).toList() as M;
    }
    if (<CommitTopUpEntity>[] is M) {
      return data.map<CommitTopUpEntity>((Map<String, dynamic> e) =>
          CommitTopUpEntity.fromJson(e)).toList() as M;
    }
    if (<TopUpOrderExt>[] is M) {
      return data.map<TopUpOrderExt>((Map<String, dynamic> e) =>
          TopUpOrderExt.fromJson(e)).toList() as M;
    }
    if (<CustomerServiceConfigEntity>[] is M) {
      return data.map<CustomerServiceConfigEntity>((Map<String, dynamic> e) =>
          CustomerServiceConfigEntity.fromJson(e)).toList() as M;
    }
    if (<CustomerServiceConfigData>[] is M) {
      return data.map<CustomerServiceConfigData>((Map<String, dynamic> e) =>
          CustomerServiceConfigData.fromJson(e)).toList() as M;
    }
    if (<DailyCheckInEntity>[] is M) {
      return data.map<DailyCheckInEntity>((Map<String, dynamic> e) =>
          DailyCheckInEntity.fromJson(e)).toList() as M;
    }
    if (<DailyCheckInItem>[] is M) {
      return data.map<DailyCheckInItem>((Map<String, dynamic> e) =>
          DailyCheckInItem.fromJson(e)).toList() as M;
    }
    if (<DailySignInList>[] is M) {
      return data.map<DailySignInList>((Map<String, dynamic> e) =>
          DailySignInList.fromJson(e)).toList() as M;
    }
    if (<DailySignInEntity>[] is M) {
      return data.map<DailySignInEntity>((Map<String, dynamic> e) =>
          DailySignInEntity.fromJson(e)).toList() as M;
    }
    if (<FilterVideoListEntity>[] is M) {
      return data.map<FilterVideoListEntity>((Map<String, dynamic> e) =>
          FilterVideoListEntity.fromJson(e)).toList() as M;
    }
    if (<GameListEntity>[] is M) {
      return data.map<GameListEntity>((Map<String, dynamic> e) =>
          GameListEntity.fromJson(e)).toList() as M;
    }
    if (<PlatformInfo>[] is M) {
      return data.map<PlatformInfo>((Map<String, dynamic> e) =>
          PlatformInfo.fromJson(e)).toList() as M;
    }
    if (<GameType>[] is M) {
      return data.map<GameType>((Map<String, dynamic> e) =>
          GameType.fromJson(e)).toList() as M;
    }
    if (<GamePlatform>[] is M) {
      return data.map<GamePlatform>((Map<String, dynamic> e) =>
          GamePlatform.fromJson(e)).toList() as M;
    }
    if (<Game>[] is M) {
      return data.map<Game>((Map<String, dynamic> e) => Game.fromJson(e))
          .toList() as M;
    }
    if (<GameLoginEntity>[] is M) {
      return data.map<GameLoginEntity>((Map<String, dynamic> e) =>
          GameLoginEntity.fromJson(e)).toList() as M;
    }
    if (<GameNoticeEntity>[] is M) {
      return data.map<GameNoticeEntity>((Map<String, dynamic> e) =>
          GameNoticeEntity.fromJson(e)).toList() as M;
    }
    if (<HomeBannerListEntity>[] is M) {
      return data.map<HomeBannerListEntity>((Map<String, dynamic> e) =>
          HomeBannerListEntity.fromJson(e)).toList() as M;
    }
    if (<HomeBannerEntity>[] is M) {
      return data.map<HomeBannerEntity>((Map<String, dynamic> e) =>
          HomeBannerEntity.fromJson(e)).toList() as M;
    }
    if (<HomeCustomEntity>[] is M) {
      return data.map<HomeCustomEntity>((Map<String, dynamic> e) =>
          HomeCustomEntity.fromJson(e)).toList() as M;
    }
    if (<HomeCustomSection>[] is M) {
      return data.map<HomeCustomSection>((Map<String, dynamic> e) =>
          HomeCustomSection.fromJson(e)).toList() as M;
    }
    if (<HomeCustomSubItem>[] is M) {
      return data.map<HomeCustomSubItem>((Map<String, dynamic> e) =>
          HomeCustomSubItem.fromJson(e)).toList() as M;
    }
    if (<ImageCaptchaEntity>[] is M) {
      return data.map<ImageCaptchaEntity>((Map<String, dynamic> e) =>
          ImageCaptchaEntity.fromJson(e)).toList() as M;
    }
    if (<InviteCodeEntity>[] is M) {
      return data.map<InviteCodeEntity>((Map<String, dynamic> e) =>
          InviteCodeEntity.fromJson(e)).toList() as M;
    }
    if (<JumpChatPayEntity>[] is M) {
      return data.map<JumpChatPayEntity>((Map<String, dynamic> e) =>
          JumpChatPayEntity.fromJson(e)).toList() as M;
    }
    if (<LoginEntity>[] is M) {
      return data.map<LoginEntity>((Map<String, dynamic> e) =>
          LoginEntity.fromJson(e)).toList() as M;
    }
    if (<LoginTokenUser>[] is M) {
      return data.map<LoginTokenUser>((Map<String, dynamic> e) =>
          LoginTokenUser.fromJson(e)).toList() as M;
    }
    if (<LotteryCartItemEntity>[] is M) {
      return data.map<LotteryCartItemEntity>((Map<String, dynamic> e) =>
          LotteryCartItemEntity.fromJson(e)).toList() as M;
    }
    if (<LotteryCurrentStatusEntity>[] is M) {
      return data.map<LotteryCurrentStatusEntity>((Map<String, dynamic> e) =>
          LotteryCurrentStatusEntity.fromJson(e)).toList() as M;
    }
    if (<LotteryListEntity>[] is M) {
      return data.map<LotteryListEntity>((Map<String, dynamic> e) =>
          LotteryListEntity.fromJson(e)).toList() as M;
    }
    if (<LotteryGroup>[] is M) {
      return data.map<LotteryGroup>((Map<String, dynamic> e) =>
          LotteryGroup.fromJson(e)).toList() as M;
    }
    if (<Lottery>[] is M) {
      return data.map<Lottery>((Map<String, dynamic> e) => Lottery.fromJson(e))
          .toList() as M;
    }
    if (<LotteryOddsList>[] is M) {
      return data.map<LotteryOddsList>((Map<String, dynamic> e) =>
          LotteryOddsList.fromJson(e)).toList() as M;
    }
    if (<LotteryOdds>[] is M) {
      return data.map<LotteryOdds>((Map<String, dynamic> e) =>
          LotteryOdds.fromJson(e)).toList() as M;
    }
    if (<LotteryOddsGroup>[] is M) {
      return data.map<LotteryOddsGroup>((Map<String, dynamic> e) =>
          LotteryOddsGroup.fromJson(e)).toList() as M;
    }
    if (<LotteryResultEntity>[] is M) {
      return data.map<LotteryResultEntity>((Map<String, dynamic> e) =>
          LotteryResultEntity.fromJson(e)).toList() as M;
    }
    if (<LotteryResultRecords>[] is M) {
      return data.map<LotteryResultRecords>((Map<String, dynamic> e) =>
          LotteryResultRecords.fromJson(e)).toList() as M;
    }
    if (<MatchResultEntity>[] is M) {
      return data.map<MatchResultEntity>((Map<String, dynamic> e) =>
          MatchResultEntity.fromJson(e)).toList() as M;
    }
    if (<MyTeamEntity>[] is M) {
      return data.map<MyTeamEntity>((Map<String, dynamic> e) =>
          MyTeamEntity.fromJson(e)).toList() as M;
    }
    if (<NoticeEntity>[] is M) {
      return data.map<NoticeEntity>((Map<String, dynamic> e) =>
          NoticeEntity.fromJson(e)).toList() as M;
    }
    if (<NotificationAlertListEntity>[] is M) {
      return data.map<NotificationAlertListEntity>((Map<String, dynamic> e) =>
          NotificationAlertListEntity.fromJson(e)).toList() as M;
    }
    if (<NotificationAlertEntity>[] is M) {
      return data.map<NotificationAlertEntity>((Map<String, dynamic> e) =>
          NotificationAlertEntity.fromJson(e)).toList() as M;
    }
    if (<NotificationsResponseEntity>[] is M) {
      return data.map<NotificationsResponseEntity>((Map<String, dynamic> e) =>
          NotificationsResponseEntity.fromJson(e)).toList() as M;
    }
    if (<NotificationsRecords>[] is M) {
      return data.map<NotificationsRecords>((Map<String, dynamic> e) =>
          NotificationsRecords.fromJson(e)).toList() as M;
    }
    if (<OrderChannelListEntity>[] is M) {
      return data.map<OrderChannelListEntity>((Map<String, dynamic> e) =>
          OrderChannelListEntity.fromJson(e)).toList() as M;
    }
    if (<OrderChannelListPage>[] is M) {
      return data.map<OrderChannelListPage>((Map<String, dynamic> e) =>
          OrderChannelListPage.fromJson(e)).toList() as M;
    }
    if (<OrderChannelListPageRecords>[] is M) {
      return data.map<OrderChannelListPageRecords>((Map<String, dynamic> e) =>
          OrderChannelListPageRecords.fromJson(e)).toList() as M;
    }
    if (<OrderMainEntityList>[] is M) {
      return data.map<OrderMainEntityList>((Map<String, dynamic> e) =>
          OrderMainEntityList.fromJson(e)).toList() as M;
    }
    if (<OrderMainEntity>[] is M) {
      return data.map<OrderMainEntity>((Map<String, dynamic> e) =>
          OrderMainEntity.fromJson(e)).toList() as M;
    }
    if (<OrderPlatformEntityList>[] is M) {
      return data.map<OrderPlatformEntityList>((Map<String, dynamic> e) =>
          OrderPlatformEntityList.fromJson(e)).toList() as M;
    }
    if (<OrderPlatformEntity>[] is M) {
      return data.map<OrderPlatformEntity>((Map<String, dynamic> e) =>
          OrderPlatformEntity.fromJson(e)).toList() as M;
    }
    if (<PaymentCardEntityList>[] is M) {
      return data.map<PaymentCardEntityList>((Map<String, dynamic> e) =>
          PaymentCardEntityList.fromJson(e)).toList() as M;
    }
    if (<PaymentCardEntity>[] is M) {
      return data.map<PaymentCardEntity>((Map<String, dynamic> e) =>
          PaymentCardEntity.fromJson(e)).toList() as M;
    }
    if (<PlatformAmountEntityList>[] is M) {
      return data.map<PlatformAmountEntityList>((Map<String, dynamic> e) =>
          PlatformAmountEntityList.fromJson(e)).toList() as M;
    }
    if (<PlatformAmountEntity>[] is M) {
      return data.map<PlatformAmountEntity>((Map<String, dynamic> e) =>
          PlatformAmountEntity.fromJson(e)).toList() as M;
    }
    if (<PopularAndCollectionGameEntity>[] is M) {
      return data.map<PopularAndCollectionGameEntity>((
          Map<String, dynamic> e) => PopularAndCollectionGameEntity.fromJson(e))
          .toList() as M;
    }
    if (<PopularVenue>[] is M) {
      return data.map<PopularVenue>((Map<String, dynamic> e) =>
          PopularVenue.fromJson(e)).toList() as M;
    }
    if (<PopularGame>[] is M) {
      return data.map<PopularGame>((Map<String, dynamic> e) =>
          PopularGame.fromJson(e)).toList() as M;
    }
    if (<PromotionBannerEntity>[] is M) {
      return data.map<PromotionBannerEntity>((Map<String, dynamic> e) =>
          PromotionBannerEntity.fromJson(e)).toList() as M;
    }
    if (<PromotionBannerList>[] is M) {
      return data.map<PromotionBannerList>((Map<String, dynamic> e) =>
          PromotionBannerList.fromJson(e)).toList() as M;
    }
    if (<ShortVideoEntityList>[] is M) {
      return data.map<ShortVideoEntityList>((Map<String, dynamic> e) =>
          ShortVideoEntityList.fromJson(e)).toList() as M;
    }
    if (<ShortVideoEntity>[] is M) {
      return data.map<ShortVideoEntity>((Map<String, dynamic> e) =>
          ShortVideoEntity.fromJson(e)).toList() as M;
    }
    if (<StatementEntity>[] is M) {
      return data.map<StatementEntity>((Map<String, dynamic> e) =>
          StatementEntity.fromJson(e)).toList() as M;
    }
    if (<StatementRecords>[] is M) {
      return data.map<StatementRecords>((Map<String, dynamic> e) =>
          StatementRecords.fromJson(e)).toList() as M;
    }
    if (<StatementFilterWayList>[] is M) {
      return data.map<StatementFilterWayList>((Map<String, dynamic> e) =>
          StatementFilterWayList.fromJson(e)).toList() as M;
    }
    if (<StatementFilterWay>[] is M) {
      return data.map<StatementFilterWay>((Map<String, dynamic> e) =>
          StatementFilterWay.fromJson(e)).toList() as M;
    }
    if (<StatementFilterTypeList>[] is M) {
      return data.map<StatementFilterTypeList>((Map<String, dynamic> e) =>
          StatementFilterTypeList.fromJson(e)).toList() as M;
    }
    if (<StatementFilterType>[] is M) {
      return data.map<StatementFilterType>((Map<String, dynamic> e) =>
          StatementFilterType.fromJson(e)).toList() as M;
    }
    if (<SubordinateInfoEntity>[] is M) {
      return data.map<SubordinateInfoEntity>((Map<String, dynamic> e) =>
          SubordinateInfoEntity.fromJson(e)).toList() as M;
    }
    if (<SystemConfigEntity>[] is M) {
      return data.map<SystemConfigEntity>((Map<String, dynamic> e) =>
          SystemConfigEntity.fromJson(e)).toList() as M;
    }
    if (<LoadLoginAndRegWay>[] is M) {
      return data.map<LoadLoginAndRegWay>((Map<String, dynamic> e) =>
          LoadLoginAndRegWay.fromJson(e)).toList() as M;
    }
    if (<LanguageType>[] is M) {
      return data.map<LanguageType>((Map<String, dynamic> e) =>
          LanguageType.fromJson(e)).toList() as M;
    }
    if (<LanguageConfig>[] is M) {
      return data.map<LanguageConfig>((Map<String, dynamic> e) =>
          LanguageConfig.fromJson(e)).toList() as M;
    }
    if (<TCSDKConfigEntity>[] is M) {
      return data.map<TCSDKConfigEntity>((Map<String, dynamic> e) =>
          TCSDKConfigEntity.fromJson(e)).toList() as M;
    }
    if (<TeamDetailsEntity>[] is M) {
      return data.map<TeamDetailsEntity>((Map<String, dynamic> e) =>
          TeamDetailsEntity.fromJson(e)).toList() as M;
    }
    if (<TeamDetailsRecords>[] is M) {
      return data.map<TeamDetailsRecords>((Map<String, dynamic> e) =>
          TeamDetailsRecords.fromJson(e)).toList() as M;
    }
    if (<TeamDetailsOrders>[] is M) {
      return data.map<TeamDetailsOrders>((Map<String, dynamic> e) =>
          TeamDetailsOrders.fromJson(e)).toList() as M;
    }
    if (<TeamDetailsOptimizeCountSql>[] is M) {
      return data.map<TeamDetailsOptimizeCountSql>((Map<String, dynamic> e) =>
          TeamDetailsOptimizeCountSql.fromJson(e)).toList() as M;
    }
    if (<TeamDetailsSearchCount>[] is M) {
      return data.map<TeamDetailsSearchCount>((Map<String, dynamic> e) =>
          TeamDetailsSearchCount.fromJson(e)).toList() as M;
    }
    if (<TeamEntity>[] is M) {
      return data.map<TeamEntity>((Map<String, dynamic> e) =>
          TeamEntity.fromJson(e)).toList() as M;
    }
    if (<TeamTeamConfigList>[] is M) {
      return data.map<TeamTeamConfigList>((Map<String, dynamic> e) =>
          TeamTeamConfigList.fromJson(e)).toList() as M;
    }
    if (<TeamMembersEntity>[] is M) {
      return data.map<TeamMembersEntity>((Map<String, dynamic> e) =>
          TeamMembersEntity.fromJson(e)).toList() as M;
    }
    if (<TeamMembersRecords>[] is M) {
      return data.map<TeamMembersRecords>((Map<String, dynamic> e) =>
          TeamMembersRecords.fromJson(e)).toList() as M;
    }
    if (<TeamMembersOrders>[] is M) {
      return data.map<TeamMembersOrders>((Map<String, dynamic> e) =>
          TeamMembersOrders.fromJson(e)).toList() as M;
    }
    if (<TeamMembersOptimizeCountSql>[] is M) {
      return data.map<TeamMembersOptimizeCountSql>((Map<String, dynamic> e) =>
          TeamMembersOptimizeCountSql.fromJson(e)).toList() as M;
    }
    if (<TeamMembersSearchCount>[] is M) {
      return data.map<TeamMembersSearchCount>((Map<String, dynamic> e) =>
          TeamMembersSearchCount.fromJson(e)).toList() as M;
    }
    if (<ThirdPartyWalletInfoEntity>[] is M) {
      return data.map<ThirdPartyWalletInfoEntity>((Map<String, dynamic> e) =>
          ThirdPartyWalletInfoEntity.fromJson(e)).toList() as M;
    }
    if (<TopUpListEntityList>[] is M) {
      return data.map<TopUpListEntityList>((Map<String, dynamic> e) =>
          TopUpListEntityList.fromJson(e)).toList() as M;
    }
    if (<TopUpListEntity>[] is M) {
      return data.map<TopUpListEntity>((Map<String, dynamic> e) =>
          TopUpListEntity.fromJson(e)).toList() as M;
    }
    if (<TopUpListPayTypeList>[] is M) {
      return data.map<TopUpListPayTypeList>((Map<String, dynamic> e) =>
          TopUpListPayTypeList.fromJson(e)).toList() as M;
    }
    if (<TopUpRecordEntity>[] is M) {
      return data.map<TopUpRecordEntity>((Map<String, dynamic> e) =>
          TopUpRecordEntity.fromJson(e)).toList() as M;
    }
    if (<TopUpRecord>[] is M) {
      return data.map<TopUpRecord>((Map<String, dynamic> e) =>
          TopUpRecord.fromJson(e)).toList() as M;
    }
    if (<TransactFilterWayList>[] is M) {
      return data.map<TransactFilterWayList>((Map<String, dynamic> e) =>
          TransactFilterWayList.fromJson(e)).toList() as M;
    }
    if (<TransactFilterWay>[] is M) {
      return data.map<TransactFilterWay>((Map<String, dynamic> e) =>
          TransactFilterWay.fromJson(e)).toList() as M;
    }
    if (<TransactFilterTypeList>[] is M) {
      return data.map<TransactFilterTypeList>((Map<String, dynamic> e) =>
          TransactFilterTypeList.fromJson(e)).toList() as M;
    }
    if (<TransactFilterType>[] is M) {
      return data.map<TransactFilterType>((Map<String, dynamic> e) =>
          TransactFilterType.fromJson(e)).toList() as M;
    }
    if (<UserInfoEntity>[] is M) {
      return data.map<UserInfoEntity>((Map<String, dynamic> e) =>
          UserInfoEntity.fromJson(e)).toList() as M;
    }
    if (<UserBalanceEntity>[] is M) {
      return data.map<UserBalanceEntity>((Map<String, dynamic> e) =>
          UserBalanceEntity.fromJson(e)).toList() as M;
    }
    if (<UserVipEntity>[] is M) {
      return data.map<UserVipEntity>((Map<String, dynamic> e) =>
          UserVipEntity.fromJson(e)).toList() as M;
    }
    if (<VideoEntityList>[] is M) {
      return data.map<VideoEntityList>((Map<String, dynamic> e) =>
          VideoEntityList.fromJson(e)).toList() as M;
    }
    if (<VideoEntity>[] is M) {
      return data.map<VideoEntity>((Map<String, dynamic> e) =>
          VideoEntity.fromJson(e)).toList() as M;
    }
    if (<VideoVipRemainDayEntity>[] is M) {
      return data.map<VideoVipRemainDayEntity>((Map<String, dynamic> e) =>
          VideoVipRemainDayEntity.fromJson(e)).toList() as M;
    }
    if (<VideoFilterEntityList>[] is M) {
      return data.map<VideoFilterEntityList>((Map<String, dynamic> e) =>
          VideoFilterEntityList.fromJson(e)).toList() as M;
    }
    if (<VideoFilterEntity>[] is M) {
      return data.map<VideoFilterEntity>((Map<String, dynamic> e) =>
          VideoFilterEntity.fromJson(e)).toList() as M;
    }
    if (<VideoHotMoviesEntity>[] is M) {
      return data.map<VideoHotMoviesEntity>((Map<String, dynamic> e) =>
          VideoHotMoviesEntity.fromJson(e)).toList() as M;
    }
    if (<VideoHotMovies>[] is M) {
      return data.map<VideoHotMovies>((Map<String, dynamic> e) =>
          VideoHotMovies.fromJson(e)).toList() as M;
    }
    if (<VideoHotTagEntity>[] is M) {
      return data.map<VideoHotTagEntity>((Map<String, dynamic> e) =>
          VideoHotTagEntity.fromJson(e)).toList() as M;
    }
    if (<VideoHotTagMoviesCategory>[] is M) {
      return data.map<VideoHotTagMoviesCategory>((Map<String, dynamic> e) =>
          VideoHotTagMoviesCategory.fromJson(e)).toList() as M;
    }
    if (<VideoListEntity>[] is M) {
      return data.map<VideoListEntity>((Map<String, dynamic> e) =>
          VideoListEntity.fromJson(e)).toList() as M;
    }
    if (<VideoListRecords>[] is M) {
      return data.map<VideoListRecords>((Map<String, dynamic> e) =>
          VideoListRecords.fromJson(e)).toList() as M;
    }
    if (<VideoDetailEntity>[] is M) {
      return data.map<VideoDetailEntity>((Map<String, dynamic> e) =>
          VideoDetailEntity.fromJson(e)).toList() as M;
    }
    if (<VipModelListEntity>[] is M) {
      return data.map<VipModelListEntity>((Map<String, dynamic> e) =>
          VipModelListEntity.fromJson(e)).toList() as M;
    }
    if (<VipModel>[] is M) {
      return data.map<VipModel>((Map<String, dynamic> e) =>
          VipModel.fromJson(e)).toList() as M;
    }
    if (<WinnerEntityList>[] is M) {
      return data.map<WinnerEntityList>((Map<String, dynamic> e) =>
          WinnerEntityList.fromJson(e)).toList() as M;
    }
    if (<WinnerEntity>[] is M) {
      return data.map<WinnerEntity>((Map<String, dynamic> e) =>
          WinnerEntity.fromJson(e)).toList() as M;
    }
    if (<WinningEntityList>[] is M) {
      return data.map<WinningEntityList>((Map<String, dynamic> e) =>
          WinningEntityList.fromJson(e)).toList() as M;
    }
    if (<WinningEntity>[] is M) {
      return data.map<WinningEntity>((Map<String, dynamic> e) =>
          WinningEntity.fromJson(e)).toList() as M;
    }
    if (<WithdrawRecordEntity>[] is M) {
      return data.map<WithdrawRecordEntity>((Map<String, dynamic> e) =>
          WithdrawRecordEntity.fromJson(e)).toList() as M;
    }
    if (<WithdrawRecord>[] is M) {
      return data.map<WithdrawRecord>((Map<String, dynamic> e) =>
          WithdrawRecord.fromJson(e)).toList() as M;
    }
    if (<WithdrawStatusEntity>[] is M) {
      return data.map<WithdrawStatusEntity>((Map<String, dynamic> e) =>
          WithdrawStatusEntity.fromJson(e)).toList() as M;
    }
    if (<WithdrawUserBankBriefList>[] is M) {
      return data.map<WithdrawUserBankBriefList>((Map<String, dynamic> e) =>
          WithdrawUserBankBriefList.fromJson(e)).toList() as M;
    }
    if (<WithdrawUserBankBrief>[] is M) {
      return data.map<WithdrawUserBankBrief>((Map<String, dynamic> e) =>
          WithdrawUserBankBrief.fromJson(e)).toList() as M;
    }
    if (<WithdrawUserBankInfoEntity>[] is M) {
      return data.map<WithdrawUserBankInfoEntity>((Map<String, dynamic> e) =>
          WithdrawUserBankInfoEntity.fromJson(e)).toList() as M;
    }
    if (<WithdrawManualChannelList>[] is M) {
      return data.map<WithdrawManualChannelList>((Map<String, dynamic> e) =>
          WithdrawManualChannelList.fromJson(e)).toList() as M;
    }
    if (<WithdrawManualChannelEntity>[] is M) {
      return data.map<WithdrawManualChannelEntity>((Map<String, dynamic> e) =>
          WithdrawManualChannelEntity.fromJson(e)).toList() as M;
    }

    debugPrint("$M not found");

    return null;
  }

  static M? fromJsonAsT<M>(dynamic json) {
    if (json is M) {
      return json;
    }
    if (json is List) {
      return _getListChildType<M>(
          json.map((dynamic e) => e as Map<String, dynamic>).toList());
    } else {
      return jsonConvert.convert<M>(json);
    }
  }
}

class JsonConvertClassCollection {
  Map<String, JsonConvertFunction> convertFuncMap = {
    (ActivityCategoryListEntity).toString(): ActivityCategoryListEntity
        .fromJson,
    (ActivityCategory).toString(): ActivityCategory.fromJson,
    (ActivityListEntity).toString(): ActivityListEntity.fromJson,
    (ActivityRecords).toString(): ActivityRecords.fromJson,
    (ActivityTaskListEntity).toString(): ActivityTaskListEntity.fromJson,
    (ActivityTask).toString(): ActivityTask.fromJson,
    (ActivityTaskRecordEntity).toString(): ActivityTaskRecordEntity.fromJson,
    (ActivityTaskRecord).toString(): ActivityTaskRecord.fromJson,
    (AppVersionEntity).toString(): AppVersionEntity.fromJson,
    (BankEntityList).toString(): BankEntityList.fromJson,
    (BankEntity).toString(): BankEntity.fromJson,
    (BetRecordEntity).toString(): BetRecordEntity.fromJson,
    (BetRecordPage).toString(): BetRecordPage.fromJson,
    (BetRecordPageRecords).toString(): BetRecordPageRecords.fromJson,
    (BetRecordPageOrders).toString(): BetRecordPageOrders.fromJson,
    (BetRecordPageOptimizeCountSql).toString(): BetRecordPageOptimizeCountSql
        .fromJson,
    (BetRecordPageSearchCount).toString(): BetRecordPageSearchCount.fromJson,
    (BonusPoolEntity).toString(): BonusPoolEntity.fromJson,
    (ChatServiceOnlineList).toString(): ChatServiceOnlineList.fromJson,
    (ChatServiceOnlineEntity).toString(): ChatServiceOnlineEntity.fromJson,
    (ChatTypeEntity).toString(): ChatTypeEntity.fromJson,
    (CommissionBetEntity).toString(): CommissionBetEntity.fromJson,
    (CommissionBetList).toString(): CommissionBetList.fromJson,
    (CommissionBetListThirdList).toString(): CommissionBetListThirdList
        .fromJson,
    (CommissionBetListThirdListList).toString(): CommissionBetListThirdListList
        .fromJson,
    (CommissionDetailsEntity).toString(): CommissionDetailsEntity.fromJson,
    (CommissionDetailsRecords).toString(): CommissionDetailsRecords.fromJson,
    (CommissionDetailsOrders).toString(): CommissionDetailsOrders.fromJson,
    (CommissionDetailsOptimizeCountSql)
        .toString(): CommissionDetailsOptimizeCountSql.fromJson,
    (CommissionDetailsSearchCount).toString(): CommissionDetailsSearchCount
        .fromJson,
    (CommissionOverviewEntity).toString(): CommissionOverviewEntity.fromJson,
    (CommissionRechargeEntity).toString(): CommissionRechargeEntity.fromJson,
    (CommissionRechargeList).toString(): CommissionRechargeList.fromJson,
    (CommissionRecordEntity).toString(): CommissionRecordEntity.fromJson,
    (CommissionRecordList).toString(): CommissionRecordList.fromJson,
    (CommissionRecordOrders).toString(): CommissionRecordOrders.fromJson,
    (CommissionRecordOptimizeCountSql)
        .toString(): CommissionRecordOptimizeCountSql.fromJson,
    (CommissionRecordSearchCount).toString(): CommissionRecordSearchCount
        .fromJson,
    (CommitTopUpEntity).toString(): CommitTopUpEntity.fromJson,
    (TopUpOrderExt).toString(): TopUpOrderExt.fromJson,
    (CustomerServiceConfigEntity).toString(): CustomerServiceConfigEntity
        .fromJson,
    (CustomerServiceConfigData).toString(): CustomerServiceConfigData.fromJson,
    (DailyCheckInEntity).toString(): DailyCheckInEntity.fromJson,
    (DailyCheckInItem).toString(): DailyCheckInItem.fromJson,
    (DailySignInList).toString(): DailySignInList.fromJson,
    (DailySignInEntity).toString(): DailySignInEntity.fromJson,
    (FilterVideoListEntity).toString(): FilterVideoListEntity.fromJson,
    (GameListEntity).toString(): GameListEntity.fromJson,
    (PlatformInfo).toString(): PlatformInfo.fromJson,
    (GameType).toString(): GameType.fromJson,
    (GamePlatform).toString(): GamePlatform.fromJson,
    (Game).toString(): Game.fromJson,
    (GameLoginEntity).toString(): GameLoginEntity.fromJson,
    (GameNoticeEntity).toString(): GameNoticeEntity.fromJson,
    (HomeBannerListEntity).toString(): HomeBannerListEntity.fromJson,
    (HomeBannerEntity).toString(): HomeBannerEntity.fromJson,
    (HomeCustomEntity).toString(): HomeCustomEntity.fromJson,
    (HomeCustomSection).toString(): HomeCustomSection.fromJson,
    (HomeCustomSubItem).toString(): HomeCustomSubItem.fromJson,
    (ImageCaptchaEntity).toString(): ImageCaptchaEntity.fromJson,
    (InviteCodeEntity).toString(): InviteCodeEntity.fromJson,
    (JumpChatPayEntity).toString(): JumpChatPayEntity.fromJson,
    (LoginEntity).toString(): LoginEntity.fromJson,
    (LoginTokenUser).toString(): LoginTokenUser.fromJson,
    (LotteryCartItemEntity).toString(): LotteryCartItemEntity.fromJson,
    (LotteryCurrentStatusEntity).toString(): LotteryCurrentStatusEntity
        .fromJson,
    (LotteryListEntity).toString(): LotteryListEntity.fromJson,
    (LotteryGroup).toString(): LotteryGroup.fromJson,
    (Lottery).toString(): Lottery.fromJson,
    (LotteryOddsList).toString(): LotteryOddsList.fromJson,
    (LotteryOdds).toString(): LotteryOdds.fromJson,
    (LotteryOddsGroup).toString(): LotteryOddsGroup.fromJson,
    (LotteryResultEntity).toString(): LotteryResultEntity.fromJson,
    (LotteryResultRecords).toString(): LotteryResultRecords.fromJson,
    (MatchResultEntity).toString(): MatchResultEntity.fromJson,
    (MyTeamEntity).toString(): MyTeamEntity.fromJson,
    (NoticeEntity).toString(): NoticeEntity.fromJson,
    (NotificationAlertListEntity).toString(): NotificationAlertListEntity
        .fromJson,
    (NotificationAlertEntity).toString(): NotificationAlertEntity.fromJson,
    (NotificationsResponseEntity).toString(): NotificationsResponseEntity
        .fromJson,
    (NotificationsRecords).toString(): NotificationsRecords.fromJson,
    (OrderChannelListEntity).toString(): OrderChannelListEntity.fromJson,
    (OrderChannelListPage).toString(): OrderChannelListPage.fromJson,
    (OrderChannelListPageRecords).toString(): OrderChannelListPageRecords
        .fromJson,
    (OrderMainEntityList).toString(): OrderMainEntityList.fromJson,
    (OrderMainEntity).toString(): OrderMainEntity.fromJson,
    (OrderPlatformEntityList).toString(): OrderPlatformEntityList.fromJson,
    (OrderPlatformEntity).toString(): OrderPlatformEntity.fromJson,
    (PaymentCardEntityList).toString(): PaymentCardEntityList.fromJson,
    (PaymentCardEntity).toString(): PaymentCardEntity.fromJson,
    (PlatformAmountEntityList).toString(): PlatformAmountEntityList.fromJson,
    (PlatformAmountEntity).toString(): PlatformAmountEntity.fromJson,
    (PopularAndCollectionGameEntity).toString(): PopularAndCollectionGameEntity
        .fromJson,
    (PopularVenue).toString(): PopularVenue.fromJson,
    (PopularGame).toString(): PopularGame.fromJson,
    (PromotionBannerEntity).toString(): PromotionBannerEntity.fromJson,
    (PromotionBannerList).toString(): PromotionBannerList.fromJson,
    (ShortVideoEntityList).toString(): ShortVideoEntityList.fromJson,
    (ShortVideoEntity).toString(): ShortVideoEntity.fromJson,
    (StatementEntity).toString(): StatementEntity.fromJson,
    (StatementRecords).toString(): StatementRecords.fromJson,
    (StatementFilterWayList).toString(): StatementFilterWayList.fromJson,
    (StatementFilterWay).toString(): StatementFilterWay.fromJson,
    (StatementFilterTypeList).toString(): StatementFilterTypeList.fromJson,
    (StatementFilterType).toString(): StatementFilterType.fromJson,
    (SubordinateInfoEntity).toString(): SubordinateInfoEntity.fromJson,
    (SystemConfigEntity).toString(): SystemConfigEntity.fromJson,
    (LoadLoginAndRegWay).toString(): LoadLoginAndRegWay.fromJson,
    (LanguageType).toString(): LanguageType.fromJson,
    (LanguageConfig).toString(): LanguageConfig.fromJson,
    (TCSDKConfigEntity).toString(): TCSDKConfigEntity.fromJson,
    (TeamDetailsEntity).toString(): TeamDetailsEntity.fromJson,
    (TeamDetailsRecords).toString(): TeamDetailsRecords.fromJson,
    (TeamDetailsOrders).toString(): TeamDetailsOrders.fromJson,
    (TeamDetailsOptimizeCountSql).toString(): TeamDetailsOptimizeCountSql
        .fromJson,
    (TeamDetailsSearchCount).toString(): TeamDetailsSearchCount.fromJson,
    (TeamEntity).toString(): TeamEntity.fromJson,
    (TeamTeamConfigList).toString(): TeamTeamConfigList.fromJson,
    (TeamMembersEntity).toString(): TeamMembersEntity.fromJson,
    (TeamMembersRecords).toString(): TeamMembersRecords.fromJson,
    (TeamMembersOrders).toString(): TeamMembersOrders.fromJson,
    (TeamMembersOptimizeCountSql).toString(): TeamMembersOptimizeCountSql
        .fromJson,
    (TeamMembersSearchCount).toString(): TeamMembersSearchCount.fromJson,
    (ThirdPartyWalletInfoEntity).toString(): ThirdPartyWalletInfoEntity
        .fromJson,
    (TopUpListEntityList).toString(): TopUpListEntityList.fromJson,
    (TopUpListEntity).toString(): TopUpListEntity.fromJson,
    (TopUpListPayTypeList).toString(): TopUpListPayTypeList.fromJson,
    (TopUpRecordEntity).toString(): TopUpRecordEntity.fromJson,
    (TopUpRecord).toString(): TopUpRecord.fromJson,
    (TransactFilterWayList).toString(): TransactFilterWayList.fromJson,
    (TransactFilterWay).toString(): TransactFilterWay.fromJson,
    (TransactFilterTypeList).toString(): TransactFilterTypeList.fromJson,
    (TransactFilterType).toString(): TransactFilterType.fromJson,
    (UserInfoEntity).toString(): UserInfoEntity.fromJson,
    (UserBalanceEntity).toString(): UserBalanceEntity.fromJson,
    (UserVipEntity).toString(): UserVipEntity.fromJson,
    (VideoEntityList).toString(): VideoEntityList.fromJson,
    (VideoEntity).toString(): VideoEntity.fromJson,
    (VideoVipRemainDayEntity).toString(): VideoVipRemainDayEntity.fromJson,
    (VideoFilterEntityList).toString(): VideoFilterEntityList.fromJson,
    (VideoFilterEntity).toString(): VideoFilterEntity.fromJson,
    (VideoHotMoviesEntity).toString(): VideoHotMoviesEntity.fromJson,
    (VideoHotMovies).toString(): VideoHotMovies.fromJson,
    (VideoHotTagEntity).toString(): VideoHotTagEntity.fromJson,
    (VideoHotTagMoviesCategory).toString(): VideoHotTagMoviesCategory.fromJson,
    (VideoListEntity).toString(): VideoListEntity.fromJson,
    (VideoListRecords).toString(): VideoListRecords.fromJson,
    (VideoDetailEntity).toString(): VideoDetailEntity.fromJson,
    (VipModelListEntity).toString(): VipModelListEntity.fromJson,
    (VipModel).toString(): VipModel.fromJson,
    (WinnerEntityList).toString(): WinnerEntityList.fromJson,
    (WinnerEntity).toString(): WinnerEntity.fromJson,
    (WinningEntityList).toString(): WinningEntityList.fromJson,
    (WinningEntity).toString(): WinningEntity.fromJson,
    (WithdrawRecordEntity).toString(): WithdrawRecordEntity.fromJson,
    (WithdrawRecord).toString(): WithdrawRecord.fromJson,
    (WithdrawStatusEntity).toString(): WithdrawStatusEntity.fromJson,
    (WithdrawUserBankBriefList).toString(): WithdrawUserBankBriefList.fromJson,
    (WithdrawUserBankBrief).toString(): WithdrawUserBankBrief.fromJson,
    (WithdrawUserBankInfoEntity).toString(): WithdrawUserBankInfoEntity
        .fromJson,
    (WithdrawManualChannelList).toString(): WithdrawManualChannelList.fromJson,
    (WithdrawManualChannelEntity).toString(): WithdrawManualChannelEntity
        .fromJson,
  };

  bool containsKey(String type) {
    return convertFuncMap.containsKey(type);
  }

  JsonConvertFunction? operator [](String key) {
    return convertFuncMap[key];
  }
}