import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/video_list_entity.dart';
import 'package:equatable/equatable.dart';

import 'package:logger/logger.dart';


VideoListEntity $VideoListEntityFromJson(Map<String, dynamic> json) {
  final VideoListEntity videoListEntity = VideoListEntity();
  final List<VideoListRecords>? records = (json['records'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<VideoListRecords>(e) as VideoListRecords)
      .toList();
  if (records != null) {
    videoListEntity.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    videoListEntity.total = total;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    videoListEntity.size = size;
  }
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    videoListEntity.current = current;
  }
  final int? pages = jsonConvert.convert<int>(json['pages']);
  if (pages != null) {
    videoListEntity.pages = pages;
  }
  return videoListEntity;
}

Map<String, dynamic> $VideoListEntityToJson(VideoListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  data['size'] = entity.size;
  data['current'] = entity.current;
  data['pages'] = entity.pages;
  return data;
}

extension VideoListEntityExtension on VideoListEntity {
  VideoListEntity copyWith({
    List<VideoListRecords>? records,
    int? total,
    int? size,
    int? current,
    int? pages,
  }) {
    return VideoListEntity()
      ..records = records ?? this.records
      ..total = total ?? this.total
      ..size = size ?? this.size
      ..current = current ?? this.current
      ..pages = pages ?? this.pages;
  }
}

VideoListRecords $VideoListRecordsFromJson(Map<String, dynamic> json) {
  final VideoListRecords videoListRecords = VideoListRecords();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    videoListRecords.id = id;
  }
  final String? videoImage = jsonConvert.convert<String>(json['videoImage']);
  if (videoImage != null) {
    videoListRecords.videoImage = videoImage;
  }
  final String? videoTitle = jsonConvert.convert<String>(json['videoTitle']);
  if (videoTitle != null) {
    videoListRecords.videoTitle = videoTitle;
  }
  final String? videoTime = jsonConvert.convert<String>(json['videoTime']);
  if (videoTime != null) {
    videoListRecords.videoTime = videoTime;
  }
  final String? videoYear = jsonConvert.convert<String>(json['videoYear']);
  if (videoYear != null) {
    videoListRecords.videoYear = videoYear;
  }
  final String? videoCategory = jsonConvert.convert<String>(
      json['videoCategory']);
  if (videoCategory != null) {
    videoListRecords.videoCategory = videoCategory;
  }
  final int? videoType = jsonConvert.convert<int>(json['videoType']);
  if (videoType != null) {
    videoListRecords.videoType = videoType;
  }
  final String? videoTags = jsonConvert.convert<String>(json['videoTags']);
  if (videoTags != null) {
    videoListRecords.videoTags = videoTags;
  }
  final String? videoCountry = jsonConvert.convert<String>(
      json['videoCountry']);
  if (videoCountry != null) {
    videoListRecords.videoCountry = videoCountry;
  }
  final String? videoClarity = jsonConvert.convert<String>(
      json['videoClarity']);
  if (videoClarity != null) {
    videoListRecords.videoClarity = videoClarity;
  }
  final String? videoBottomTag = jsonConvert.convert<String>(
      json['videoBottomTag']);
  if (videoBottomTag != null) {
    videoListRecords.videoBottomTag = videoBottomTag;
  }
  final dynamic playCount = json['playCount'];
  if (playCount != null) {
    videoListRecords.playCount = playCount;
  }
  final int? hide = jsonConvert.convert<int>(json['hide']);
  if (hide != null) {
    videoListRecords.hide = hide;
  }
  final dynamic createTime = json['createTime'];
  if (createTime != null) {
    videoListRecords.createTime = createTime;
  }
  return videoListRecords;
}

Map<String, dynamic> $VideoListRecordsToJson(VideoListRecords entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['videoImage'] = entity.videoImage;
  data['videoTitle'] = entity.videoTitle;
  data['videoTime'] = entity.videoTime;
  data['videoYear'] = entity.videoYear;
  data['videoCategory'] = entity.videoCategory;
  data['videoType'] = entity.videoType;
  data['videoTags'] = entity.videoTags;
  data['videoCountry'] = entity.videoCountry;
  data['videoClarity'] = entity.videoClarity;
  data['videoBottomTag'] = entity.videoBottomTag;
  data['playCount'] = entity.playCount;
  data['hide'] = entity.hide;
  data['createTime'] = entity.createTime;
  return data;
}

extension VideoListRecordsExtension on VideoListRecords {
  VideoListRecords copyWith({
    int? id,
    String? videoImage,
    String? videoTitle,
    String? videoTime,
    String? videoYear,
    String? videoCategory,
    int? videoType,
    String? videoTags,
    String? videoCountry,
    String? videoClarity,
    String? videoBottomTag,
    dynamic playCount,
    int? hide,
    dynamic createTime,
  }) {
    return VideoListRecords()
      ..id = id ?? this.id
      ..videoImage = videoImage ?? this.videoImage
      ..videoTitle = videoTitle ?? this.videoTitle
      ..videoTime = videoTime ?? this.videoTime
      ..videoYear = videoYear ?? this.videoYear
      ..videoCategory = videoCategory ?? this.videoCategory
      ..videoType = videoType ?? this.videoType
      ..videoTags = videoTags ?? this.videoTags
      ..videoCountry = videoCountry ?? this.videoCountry
      ..videoClarity = videoClarity ?? this.videoClarity
      ..videoBottomTag = videoBottomTag ?? this.videoBottomTag
      ..playCount = playCount ?? this.playCount
      ..hide = hide ?? this.hide
      ..createTime = createTime ?? this.createTime;
  }
}

VideoDetailEntity $VideoDetailEntityFromJson(Map<String, dynamic> json) {
  final VideoDetailEntity videoDetailEntity = VideoDetailEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    videoDetailEntity.id = id;
  }
  final int? videoId = jsonConvert.convert<int>(json['videoId']);
  if (videoId != null) {
    videoDetailEntity.videoId = videoId;
  }
  final String? lineTitle = jsonConvert.convert<String>(json['lineTitle']);
  if (lineTitle != null) {
    videoDetailEntity.lineTitle = lineTitle;
  }
  final String? lineUrl = jsonConvert.convert<String>(json['lineUrl']);
  if (lineUrl != null) {
    videoDetailEntity.lineUrl = lineUrl;
  }
  final String? videoTags = jsonConvert.convert<String>(json['videoTags']);
  if (videoTags != null) {
    videoDetailEntity.videoTags = videoTags;
  }
  final String? videoTitle = jsonConvert.convert<String>(json['videoTitle']);
  if (videoTitle != null) {
    videoDetailEntity.videoTitle = videoTitle;
  }
  final String? videoCategory = jsonConvert.convert<String>(
      json['videoCategory']);
  if (videoCategory != null) {
    videoDetailEntity.videoCategory = videoCategory;
  }
  final String? videoYear = jsonConvert.convert<String>(json['videoYear']);
  if (videoYear != null) {
    videoDetailEntity.videoYear = videoYear;
  }
  final int? playCount = jsonConvert.convert<int>(json['playCount']);
  if (playCount != null) {
    videoDetailEntity.playCount = playCount;
  }
  final int? likes = jsonConvert.convert<int>(json['likes']);
  if (likes != null) {
    videoDetailEntity.likes = likes;
  }
  final bool? isLiked = jsonConvert.convert<bool>(json['isLiked']);
  if (isLiked != null) {
    videoDetailEntity.isLiked = isLiked;
  }
  final List<VideoListRecords>? recommendList = (json['recommendList'] as List<
      dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<VideoListRecords>(e) as VideoListRecords)
      .toList();
  if (recommendList != null) {
    videoDetailEntity.recommendList = recommendList;
  }
  return videoDetailEntity;
}

Map<String, dynamic> $VideoDetailEntityToJson(VideoDetailEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['videoId'] = entity.videoId;
  data['lineTitle'] = entity.lineTitle;
  data['lineUrl'] = entity.lineUrl;
  data['videoTags'] = entity.videoTags;
  data['videoTitle'] = entity.videoTitle;
  data['videoCategory'] = entity.videoCategory;
  data['videoYear'] = entity.videoYear;
  data['playCount'] = entity.playCount;
  data['likes'] = entity.likes;
  data['isLiked'] = entity.isLiked;
  data['recommendList'] = entity.recommendList.map((v) => v.toJson()).toList();
  return data;
}

extension VideoDetailEntityExtension on VideoDetailEntity {
  VideoDetailEntity copyWith({
    int? id,
    int? videoId,
    String? lineTitle,
    String? lineUrl,
    String? videoTags,
    String? videoTitle,
    String? videoCategory,
    String? videoYear,
    int? playCount,
    int? likes,
    bool? isLiked,
    List<VideoListRecords>? recommendList,
  }) {
    return VideoDetailEntity()
      ..id = id ?? this.id
      ..videoId = videoId ?? this.videoId
      ..lineTitle = lineTitle ?? this.lineTitle
      ..lineUrl = lineUrl ?? this.lineUrl
      ..videoTags = videoTags ?? this.videoTags
      ..videoTitle = videoTitle ?? this.videoTitle
      ..videoCategory = videoCategory ?? this.videoCategory
      ..videoYear = videoYear ?? this.videoYear
      ..playCount = playCount ?? this.playCount
      ..likes = likes ?? this.likes
      ..isLiked = isLiked ?? this.isLiked
      ..recommendList = recommendList ?? this.recommendList;
  }
}