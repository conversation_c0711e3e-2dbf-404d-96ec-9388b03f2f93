import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/activity_category_entity.dart';

ActivityCategoryListEntity $ActivityCategoryListEntityFromJson(
    Map<String, dynamic> json) {
  final ActivityCategoryListEntity activityCategoryListEntity = ActivityCategoryListEntity();
  final List<ActivityCategory>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<ActivityCategory>(e) as ActivityCategory)
      .toList();
  if (list != null) {
    activityCategoryListEntity.list = list;
  }
  return activityCategoryListEntity;
}

Map<String, dynamic> $ActivityCategoryListEntityToJson(
    ActivityCategoryListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension ActivityCategoryListEntityExtension on ActivityCategoryListEntity {
  ActivityCategoryListEntity copyWith({
    List<ActivityCategory>? list,
  }) {
    return ActivityCategoryListEntity()
      ..list = list ?? this.list;
  }
}

ActivityCategory $ActivityCategoryFromJson(Map<String, dynamic> json) {
  final ActivityCategory activityCategory = ActivityCategory();
  final int? category = jsonConvert.convert<int>(json['category']);
  if (category != null) {
    activityCategory.category = category;
  }
  final String? categoryName = jsonConvert.convert<String>(
      json['categoryName']);
  if (categoryName != null) {
    activityCategory.categoryName = categoryName;
  }
  return activityCategory;
}

Map<String, dynamic> $ActivityCategoryToJson(ActivityCategory entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['category'] = entity.category;
  data['categoryName'] = entity.categoryName;
  return data;
}

extension ActivityCategoryExtension on ActivityCategory {
  ActivityCategory copyWith({
    int? category,
    String? categoryName,
  }) {
    return ActivityCategory()
      ..category = category ?? this.category
      ..categoryName = categoryName ?? this.categoryName;
  }
}