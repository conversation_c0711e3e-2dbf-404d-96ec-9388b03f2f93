import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/commission_record_entity.dart';

CommissionRecordEntity $CommissionRecordEntityFromJson(
    Map<String, dynamic> json) {
  final CommissionRecordEntity commissionRecordEntity = CommissionRecordEntity();
  final List<CommissionRecordList>? records = (json['records'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<CommissionRecordList>(e) as CommissionRecordList)
      .toList();
  if (records != null) {
    commissionRecordEntity.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    commissionRecordEntity.total = total;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    commissionRecordEntity.size = size;
  }
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    commissionRecordEntity.current = current;
  }
  final List<CommissionRecordOrders>? orders = (json['orders'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<CommissionRecordOrders>(e) as CommissionRecordOrders)
      .toList();
  if (orders != null) {
    commissionRecordEntity.orders = orders;
  }
  final CommissionRecordOptimizeCountSql? optimizeCountSql = jsonConvert
      .convert<CommissionRecordOptimizeCountSql>(json['optimizeCountSql']);
  if (optimizeCountSql != null) {
    commissionRecordEntity.optimizeCountSql = optimizeCountSql;
  }
  final CommissionRecordSearchCount? searchCount = jsonConvert.convert<
      CommissionRecordSearchCount>(json['searchCount']);
  if (searchCount != null) {
    commissionRecordEntity.searchCount = searchCount;
  }
  final bool? optimizeJoinOfCountSql = jsonConvert.convert<bool>(
      json['optimizeJoinOfCountSql']);
  if (optimizeJoinOfCountSql != null) {
    commissionRecordEntity.optimizeJoinOfCountSql = optimizeJoinOfCountSql;
  }
  final int? maxLimit = jsonConvert.convert<int>(json['maxLimit']);
  if (maxLimit != null) {
    commissionRecordEntity.maxLimit = maxLimit;
  }
  final String? countId = jsonConvert.convert<String>(json['countId']);
  if (countId != null) {
    commissionRecordEntity.countId = countId;
  }
  final int? pages = jsonConvert.convert<int>(json['pages']);
  if (pages != null) {
    commissionRecordEntity.pages = pages;
  }
  return commissionRecordEntity;
}

Map<String, dynamic> $CommissionRecordEntityToJson(
    CommissionRecordEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  data['size'] = entity.size;
  data['current'] = entity.current;
  data['orders'] = entity.orders.map((v) => v.toJson()).toList();
  data['optimizeCountSql'] = entity.optimizeCountSql.toJson();
  data['searchCount'] = entity.searchCount.toJson();
  data['optimizeJoinOfCountSql'] = entity.optimizeJoinOfCountSql;
  data['maxLimit'] = entity.maxLimit;
  data['countId'] = entity.countId;
  data['pages'] = entity.pages;
  return data;
}

extension CommissionRecordEntityExtension on CommissionRecordEntity {
  CommissionRecordEntity copyWith({
    List<CommissionRecordList>? records,
    int? total,
    int? size,
    int? current,
    List<CommissionRecordOrders>? orders,
    CommissionRecordOptimizeCountSql? optimizeCountSql,
    CommissionRecordSearchCount? searchCount,
    bool? optimizeJoinOfCountSql,
    int? maxLimit,
    String? countId,
    int? pages,
  }) {
    return CommissionRecordEntity()
      ..records = records ?? this.records
      ..total = total ?? this.total
      ..size = size ?? this.size
      ..current = current ?? this.current
      ..orders = orders ?? this.orders
      ..optimizeCountSql = optimizeCountSql ?? this.optimizeCountSql
      ..searchCount = searchCount ?? this.searchCount
      ..optimizeJoinOfCountSql = optimizeJoinOfCountSql ??
          this.optimizeJoinOfCountSql
      ..maxLimit = maxLimit ?? this.maxLimit
      ..countId = countId ?? this.countId
      ..pages = pages ?? this.pages;
  }
}

CommissionRecordList $CommissionRecordListFromJson(Map<String, dynamic> json) {
  final CommissionRecordList commissionRecordList = CommissionRecordList();
  final int? userId = jsonConvert.convert<int>(json['userId']);
  if (userId != null) {
    commissionRecordList.userId = userId;
  }
  final String? userNo = jsonConvert.convert<String>(json['userNo']);
  if (userNo != null) {
    commissionRecordList.userNo = userNo;
  }
  final int? changeAmount = jsonConvert.convert<int>(json['changeAmount']);
  if (changeAmount != null) {
    commissionRecordList.changeAmount = changeAmount;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    commissionRecordList.createTime = createTime;
  }
  return commissionRecordList;
}

Map<String, dynamic> $CommissionRecordListToJson(CommissionRecordList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['userId'] = entity.userId;
  data['userNo'] = entity.userNo;
  data['changeAmount'] = entity.changeAmount;
  data['createTime'] = entity.createTime;
  return data;
}

extension CommissionRecordListExtension on CommissionRecordList {
  CommissionRecordList copyWith({
    int? userId,
    String? userNo,
    int? changeAmount,
    String? createTime,
  }) {
    return CommissionRecordList()
      ..userId = userId ?? this.userId
      ..userNo = userNo ?? this.userNo
      ..changeAmount = changeAmount ?? this.changeAmount
      ..createTime = createTime ?? this.createTime;
  }
}

CommissionRecordOrders $CommissionRecordOrdersFromJson(
    Map<String, dynamic> json) {
  final CommissionRecordOrders commissionRecordOrders = CommissionRecordOrders();
  final String? column = jsonConvert.convert<String>(json['column']);
  if (column != null) {
    commissionRecordOrders.column = column;
  }
  final bool? asc = jsonConvert.convert<bool>(json['asc']);
  if (asc != null) {
    commissionRecordOrders.asc = asc;
  }
  return commissionRecordOrders;
}

Map<String, dynamic> $CommissionRecordOrdersToJson(
    CommissionRecordOrders entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['column'] = entity.column;
  data['asc'] = entity.asc;
  return data;
}

extension CommissionRecordOrdersExtension on CommissionRecordOrders {
  CommissionRecordOrders copyWith({
    String? column,
    bool? asc,
  }) {
    return CommissionRecordOrders()
      ..column = column ?? this.column
      ..asc = asc ?? this.asc;
  }
}

CommissionRecordOptimizeCountSql $CommissionRecordOptimizeCountSqlFromJson(
    Map<String, dynamic> json) {
  final CommissionRecordOptimizeCountSql commissionRecordOptimizeCountSql = CommissionRecordOptimizeCountSql();
  return commissionRecordOptimizeCountSql;
}

Map<String, dynamic> $CommissionRecordOptimizeCountSqlToJson(
    CommissionRecordOptimizeCountSql entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  return data;
}

extension CommissionRecordOptimizeCountSqlExtension on CommissionRecordOptimizeCountSql {
}

CommissionRecordSearchCount $CommissionRecordSearchCountFromJson(
    Map<String, dynamic> json) {
  final CommissionRecordSearchCount commissionRecordSearchCount = CommissionRecordSearchCount();
  return commissionRecordSearchCount;
}

Map<String, dynamic> $CommissionRecordSearchCountToJson(
    CommissionRecordSearchCount entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  return data;
}

extension CommissionRecordSearchCountExtension on CommissionRecordSearchCount {
}