import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/team_details_entity.dart';

TeamDetailsEntity $TeamDetailsEntityFromJson(Map<String, dynamic> json) {
  final TeamDetailsEntity teamDetailsEntity = TeamDetailsEntity();
  final List<TeamDetailsRecords>? records = (json['records'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<TeamDetailsRecords>(e) as TeamDetailsRecords)
      .toList();
  if (records != null) {
    teamDetailsEntity.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    teamDetailsEntity.total = total;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    teamDetailsEntity.size = size;
  }
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    teamDetailsEntity.current = current;
  }
  final List<TeamDetailsOrders>? orders = (json['orders'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<TeamDetailsOrders>(e) as TeamDetailsOrders)
      .toList();
  if (orders != null) {
    teamDetailsEntity.orders = orders;
  }
  final TeamDetailsOptimizeCountSql? optimizeCountSql = jsonConvert.convert<
      TeamDetailsOptimizeCountSql>(json['optimizeCountSql']);
  if (optimizeCountSql != null) {
    teamDetailsEntity.optimizeCountSql = optimizeCountSql;
  }
  final TeamDetailsSearchCount? searchCount = jsonConvert.convert<
      TeamDetailsSearchCount>(json['searchCount']);
  if (searchCount != null) {
    teamDetailsEntity.searchCount = searchCount;
  }
  final bool? optimizeJoinOfCountSql = jsonConvert.convert<bool>(
      json['optimizeJoinOfCountSql']);
  if (optimizeJoinOfCountSql != null) {
    teamDetailsEntity.optimizeJoinOfCountSql = optimizeJoinOfCountSql;
  }
  final int? maxLimit = jsonConvert.convert<int>(json['maxLimit']);
  if (maxLimit != null) {
    teamDetailsEntity.maxLimit = maxLimit;
  }
  final String? countId = jsonConvert.convert<String>(json['countId']);
  if (countId != null) {
    teamDetailsEntity.countId = countId;
  }
  final int? pages = jsonConvert.convert<int>(json['pages']);
  if (pages != null) {
    teamDetailsEntity.pages = pages;
  }
  return teamDetailsEntity;
}

Map<String, dynamic> $TeamDetailsEntityToJson(TeamDetailsEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['records'] = entity.records?.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  data['size'] = entity.size;
  data['current'] = entity.current;
  data['orders'] = entity.orders?.map((v) => v.toJson()).toList();
  data['optimizeCountSql'] = entity.optimizeCountSql?.toJson();
  data['searchCount'] = entity.searchCount?.toJson();
  data['optimizeJoinOfCountSql'] = entity.optimizeJoinOfCountSql;
  data['maxLimit'] = entity.maxLimit;
  data['countId'] = entity.countId;
  data['pages'] = entity.pages;
  return data;
}

extension TeamDetailsEntityExtension on TeamDetailsEntity {
  TeamDetailsEntity copyWith({
    List<TeamDetailsRecords>? records,
    int? total,
    int? size,
    int? current,
    List<TeamDetailsOrders>? orders,
    TeamDetailsOptimizeCountSql? optimizeCountSql,
    TeamDetailsSearchCount? searchCount,
    bool? optimizeJoinOfCountSql,
    int? maxLimit,
    String? countId,
    int? pages,
  }) {
    return TeamDetailsEntity()
      ..records = records ?? this.records
      ..total = total ?? this.total
      ..size = size ?? this.size
      ..current = current ?? this.current
      ..orders = orders ?? this.orders
      ..optimizeCountSql = optimizeCountSql ?? this.optimizeCountSql
      ..searchCount = searchCount ?? this.searchCount
      ..optimizeJoinOfCountSql = optimizeJoinOfCountSql ??
          this.optimizeJoinOfCountSql
      ..maxLimit = maxLimit ?? this.maxLimit
      ..countId = countId ?? this.countId
      ..pages = pages ?? this.pages;
  }
}

TeamDetailsRecords $TeamDetailsRecordsFromJson(Map<String, dynamic> json) {
  final TeamDetailsRecords teamDetailsRecords = TeamDetailsRecords();
  final String? subUserNo = jsonConvert.convert<String>(json['subUserNo']);
  if (subUserNo != null) {
    teamDetailsRecords.subUserNo = subUserNo;
  }
  final double? amount = jsonConvert.convert<double>(json['amount']);
  if (amount != null) {
    teamDetailsRecords.amount = amount;
  }
  final double? commissionAmount = jsonConvert.convert<double>(
      json['commissionAmount']);
  if (commissionAmount != null) {
    teamDetailsRecords.commissionAmount = commissionAmount;
  }
  final double? withdrawAmount = jsonConvert.convert<double>(
      json['withdrawAmount']);
  if (withdrawAmount != null) {
    teamDetailsRecords.withdrawAmount = withdrawAmount;
  }
  final double? profitAmount = jsonConvert.convert<double>(
      json['profitAmount']);
  if (profitAmount != null) {
    teamDetailsRecords.profitAmount = profitAmount;
  }
  return teamDetailsRecords;
}

Map<String, dynamic> $TeamDetailsRecordsToJson(TeamDetailsRecords entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['subUserNo'] = entity.subUserNo;
  data['amount'] = entity.amount;
  data['commissionAmount'] = entity.commissionAmount;
  data['withdrawAmount'] = entity.withdrawAmount;
  data['profitAmount'] = entity.profitAmount;
  return data;
}

extension TeamDetailsRecordsExtension on TeamDetailsRecords {
  TeamDetailsRecords copyWith({
    String? subUserNo,
    double? amount,
    double? commissionAmount,
    double? withdrawAmount,
    double? profitAmount,
  }) {
    return TeamDetailsRecords()
      ..subUserNo = subUserNo ?? this.subUserNo
      ..amount = amount ?? this.amount
      ..commissionAmount = commissionAmount ?? this.commissionAmount
      ..withdrawAmount = withdrawAmount ?? this.withdrawAmount
      ..profitAmount = profitAmount ?? this.profitAmount;
  }
}

TeamDetailsOrders $TeamDetailsOrdersFromJson(Map<String, dynamic> json) {
  final TeamDetailsOrders teamDetailsOrders = TeamDetailsOrders();
  final String? column = jsonConvert.convert<String>(json['column']);
  if (column != null) {
    teamDetailsOrders.column = column;
  }
  final bool? asc = jsonConvert.convert<bool>(json['asc']);
  if (asc != null) {
    teamDetailsOrders.asc = asc;
  }
  return teamDetailsOrders;
}

Map<String, dynamic> $TeamDetailsOrdersToJson(TeamDetailsOrders entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['column'] = entity.column;
  data['asc'] = entity.asc;
  return data;
}

extension TeamDetailsOrdersExtension on TeamDetailsOrders {
  TeamDetailsOrders copyWith({
    String? column,
    bool? asc,
  }) {
    return TeamDetailsOrders()
      ..column = column ?? this.column
      ..asc = asc ?? this.asc;
  }
}

TeamDetailsOptimizeCountSql $TeamDetailsOptimizeCountSqlFromJson(
    Map<String, dynamic> json) {
  final TeamDetailsOptimizeCountSql teamDetailsOptimizeCountSql = TeamDetailsOptimizeCountSql();
  return teamDetailsOptimizeCountSql;
}

Map<String, dynamic> $TeamDetailsOptimizeCountSqlToJson(
    TeamDetailsOptimizeCountSql entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  return data;
}

extension TeamDetailsOptimizeCountSqlExtension on TeamDetailsOptimizeCountSql {
}

TeamDetailsSearchCount $TeamDetailsSearchCountFromJson(
    Map<String, dynamic> json) {
  final TeamDetailsSearchCount teamDetailsSearchCount = TeamDetailsSearchCount();
  return teamDetailsSearchCount;
}

Map<String, dynamic> $TeamDetailsSearchCountToJson(
    TeamDetailsSearchCount entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  return data;
}

extension TeamDetailsSearchCountExtension on TeamDetailsSearchCount {
}