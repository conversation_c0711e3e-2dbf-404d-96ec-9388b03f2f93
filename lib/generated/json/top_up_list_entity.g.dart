import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/top_up_list_entity.dart';

TopUpListEntityList $TopUpListEntityListFromJson(Map<String, dynamic> json) {
  final TopUpListEntityList topUpListEntityList = TopUpListEntityList();
  final List<TopUpListEntity>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<TopUpListEntity>(e) as TopUpListEntity)
      .toList();
  if (list != null) {
    topUpListEntityList.list = list;
  }
  return topUpListEntityList;
}

Map<String, dynamic> $TopUpListEntityListToJson(TopUpListEntityList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension TopUpListEntityListExtension on TopUpListEntityList {
  TopUpListEntityList copyWith({
    List<TopUpListEntity>? list,
  }) {
    return TopUpListEntityList()
      ..list = list ?? this.list;
  }
}

TopUpListEntity $TopUpListEntityFromJson(Map<String, dynamic> json) {
  final TopUpListEntity topUpListEntity = TopUpListEntity();
  final String? payWayName = jsonConvert.convert<String>(json['payWayName']);
  if (payWayName != null) {
    topUpListEntity.payWayName = payWayName;
  }
  final String? payWayCode = jsonConvert.convert<String>(json['payWayCode']);
  if (payWayCode != null) {
    topUpListEntity.payWayCode = payWayCode;
  }
  final String? payWayTag = jsonConvert.convert<String>(json['payWayTag']);
  if (payWayTag != null) {
    topUpListEntity.payWayTag = payWayTag;
  }
  final String? icon = jsonConvert.convert<String>(json['icon']);
  if (icon != null) {
    topUpListEntity.icon = icon;
  }
  final dynamic exchangeRate = json['exchangeRate'];
  if (exchangeRate != null) {
    topUpListEntity.exchangeRate = exchangeRate;
  }
  final bool? recommended = jsonConvert.convert<bool>(json['recommended']);
  if (recommended != null) {
    topUpListEntity.recommended = recommended;
  }
  final List<TopUpListPayTypeList>? payTypeList = (json['payTypeList'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<TopUpListPayTypeList>(e) as TopUpListPayTypeList)
      .toList();
  if (payTypeList != null) {
    topUpListEntity.payTypeList = payTypeList;
  }
  return topUpListEntity;
}

Map<String, dynamic> $TopUpListEntityToJson(TopUpListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['payWayName'] = entity.payWayName;
  data['payWayCode'] = entity.payWayCode;
  data['payWayTag'] = entity.payWayTag;
  data['icon'] = entity.icon;
  data['exchangeRate'] = entity.exchangeRate;
  data['recommended'] = entity.recommended;
  data['payTypeList'] = entity.payTypeList.map((v) => v.toJson()).toList();
  return data;
}

extension TopUpListEntityExtension on TopUpListEntity {
  TopUpListEntity copyWith({
    String? payWayName,
    String? payWayCode,
    String? payWayTag,
    String? icon,
    dynamic exchangeRate,
    bool? recommended,
    List<TopUpListPayTypeList>? payTypeList,
  }) {
    return TopUpListEntity()
      ..payWayName = payWayName ?? this.payWayName
      ..payWayCode = payWayCode ?? this.payWayCode
      ..payWayTag = payWayTag ?? this.payWayTag
      ..icon = icon ?? this.icon
      ..exchangeRate = exchangeRate ?? this.exchangeRate
      ..recommended = recommended ?? this.recommended
      ..payTypeList = payTypeList ?? this.payTypeList;
  }
}

TopUpListPayTypeList $TopUpListPayTypeListFromJson(Map<String, dynamic> json) {
  final TopUpListPayTypeList topUpListPayTypeList = TopUpListPayTypeList();
  final String? payTypeName = jsonConvert.convert<String>(json['payTypeName']);
  if (payTypeName != null) {
    topUpListPayTypeList.payTypeName = payTypeName;
  }
  final int? payTypeId = jsonConvert.convert<int>(json['payTypeId']);
  if (payTypeId != null) {
    topUpListPayTypeList.payTypeId = payTypeId;
  }
  final double? present = jsonConvert.convert<double>(json['present']);
  if (present != null) {
    topUpListPayTypeList.present = present;
  }
  final String? controllerTips = jsonConvert.convert<String>(
      json['controllerTips']);
  if (controllerTips != null) {
    topUpListPayTypeList.controllerTips = controllerTips;
  }
  final double? amountMaxLimit = jsonConvert.convert<double>(
      json['amountMaxLimit']);
  if (amountMaxLimit != null) {
    topUpListPayTypeList.amountMaxLimit = amountMaxLimit;
  }
  final double? amountMinLimit = jsonConvert.convert<double>(
      json['amountMinLimit']);
  if (amountMinLimit != null) {
    topUpListPayTypeList.amountMinLimit = amountMinLimit;
  }
  final int? sort = jsonConvert.convert<int>(json['sort']);
  if (sort != null) {
    topUpListPayTypeList.sort = sort;
  }
  final String? amountList = jsonConvert.convert<String>(json['amountList']);
  if (amountList != null) {
    topUpListPayTypeList.amountList = amountList;
  }
  final bool? fixedAmount = jsonConvert.convert<bool>(json['fixedAmount']);
  if (fixedAmount != null) {
    topUpListPayTypeList.fixedAmount = fixedAmount;
  }
  final bool? redirectWalletLobby = jsonConvert.convert<bool>(
      json['redirectWalletLobby']);
  if (redirectWalletLobby != null) {
    topUpListPayTypeList.redirectWalletLobby = redirectWalletLobby;
  }
  final int? offlineChatPayType = jsonConvert.convert<int>(
      json['offlineChatPayType']);
  if (offlineChatPayType != null) {
    topUpListPayTypeList.offlineChatPayType = offlineChatPayType;
  }
  final String? amountLimit = jsonConvert.convert<String>(json['amountLimit']);
  if (amountLimit != null) {
    topUpListPayTypeList.amountLimit = amountLimit;
  }
  final String? lobbyLink = jsonConvert.convert<String>(json['lobbyLink']);
  if (lobbyLink != null) {
    topUpListPayTypeList.lobbyLink = lobbyLink;
  }
  final double? balance = jsonConvert.convert<double>(json['balance']);
  if (balance != null) {
    topUpListPayTypeList.balance = balance;
  }
  final String? payChannelTag = jsonConvert.convert<String>(
      json['payChannelTag']);
  if (payChannelTag != null) {
    topUpListPayTypeList.payChannelTag = payChannelTag;
  }
  return topUpListPayTypeList;
}

Map<String, dynamic> $TopUpListPayTypeListToJson(TopUpListPayTypeList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['payTypeName'] = entity.payTypeName;
  data['payTypeId'] = entity.payTypeId;
  data['present'] = entity.present;
  data['controllerTips'] = entity.controllerTips;
  data['amountMaxLimit'] = entity.amountMaxLimit;
  data['amountMinLimit'] = entity.amountMinLimit;
  data['sort'] = entity.sort;
  data['amountList'] = entity.amountList;
  data['fixedAmount'] = entity.fixedAmount;
  data['redirectWalletLobby'] = entity.redirectWalletLobby;
  data['offlineChatPayType'] = entity.offlineChatPayType;
  data['amountLimit'] = entity.amountLimit;
  data['lobbyLink'] = entity.lobbyLink;
  data['balance'] = entity.balance;
  data['payChannelTag'] = entity.payChannelTag;
  return data;
}

extension TopUpListPayTypeListExtension on TopUpListPayTypeList {
  TopUpListPayTypeList copyWith({
    String? payTypeName,
    int? payTypeId,
    double? present,
    String? controllerTips,
    double? amountMaxLimit,
    double? amountMinLimit,
    int? sort,
    String? amountList,
    bool? fixedAmount,
    bool? redirectWalletLobby,
    int? offlineChatPayType,
    String? amountLimit,
    String? lobbyLink,
    double? balance,
    String? payChannelTag,
  }) {
    return TopUpListPayTypeList()
      ..payTypeName = payTypeName ?? this.payTypeName
      ..payTypeId = payTypeId ?? this.payTypeId
      ..present = present ?? this.present
      ..controllerTips = controllerTips ?? this.controllerTips
      ..amountMaxLimit = amountMaxLimit ?? this.amountMaxLimit
      ..amountMinLimit = amountMinLimit ?? this.amountMinLimit
      ..sort = sort ?? this.sort
      ..amountList = amountList ?? this.amountList
      ..fixedAmount = fixedAmount ?? this.fixedAmount
      ..redirectWalletLobby = redirectWalletLobby ?? this.redirectWalletLobby
      ..offlineChatPayType = offlineChatPayType ?? this.offlineChatPayType
      ..amountLimit = amountLimit ?? this.amountLimit
      ..lobbyLink = lobbyLink ?? this.lobbyLink
      ..balance = balance ?? this.balance
      ..payChannelTag = payChannelTag ?? this.payChannelTag;
  }
}