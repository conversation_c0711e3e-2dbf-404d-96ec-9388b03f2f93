import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/user_info_entity.dart';
import 'package:equatable/equatable.dart';


UserInfoEntity $UserInfoEntityFromJson(Map<String, dynamic> json) {
  final UserInfoEntity userInfoEntity = UserInfoEntity();
  final int? agentId = jsonConvert.convert<int>(json['agentId']);
  if (agentId != null) {
    userInfoEntity.agentId = agentId;
  }
  final String? agentUserNo = jsonConvert.convert<String>(json['agentUserNo']);
  if (agentUserNo != null) {
    userInfoEntity.agentUserNo = agentUserNo;
  }
  final String? userNo = jsonConvert.convert<String>(json['userNo']);
  if (userNo != null) {
    userInfoEntity.userNo = userNo;
  }
  final int? userType = jsonConvert.convert<int>(json['userType']);
  if (userType != null) {
    userInfoEntity.userType = userType;
  }
  final int? faceId = jsonConvert.convert<int>(json['faceId']);
  if (faceId != null) {
    userInfoEntity.faceId = faceId;
  }
  final String? nickName = jsonConvert.convert<String>(json['nickName']);
  if (nickName != null) {
    userInfoEntity.nickName = nickName;
  }
  final String? email = jsonConvert.convert<String>(json['email']);
  if (email != null) {
    userInfoEntity.email = email;
  }
  final String? phoneNo = jsonConvert.convert<String>(json['phoneNo']);
  if (phoneNo != null) {
    userInfoEntity.phoneNo = phoneNo;
  }
  final int? gender = jsonConvert.convert<int>(json['gender']);
  if (gender != null) {
    userInfoEntity.gender = gender;
  }
  final dynamic birthday = json['birthday'];
  if (birthday != null) {
    userInfoEntity.birthday = birthday;
  }
  final String? vipTitle = jsonConvert.convert<String>(json['vipTitle']);
  if (vipTitle != null) {
    userInfoEntity.vipTitle = vipTitle;
  }
  final int? vipLevel = jsonConvert.convert<int>(json['vipLevel']);
  if (vipLevel != null) {
    userInfoEntity.vipLevel = vipLevel;
  }
  final bool? hasFundPwd = jsonConvert.convert<bool>(json['hasFundPwd']);
  if (hasFundPwd != null) {
    userInfoEntity.hasFundPwd = hasFundPwd;
  }
  final bool? tiktokTabVisible = jsonConvert.convert<bool>(json['movieView']);
  if (tiktokTabVisible != null) {
    userInfoEntity.tiktokTabVisible = tiktokTabVisible;
  }
  final double? integral = jsonConvert.convert<double>(json['integral']);
  if (integral != null) {
    userInfoEntity.integral = integral;
  }
  final String? lastLoginTime = jsonConvert.convert<String>(
      json['lastLoginTime']);
  if (lastLoginTime != null) {
    userInfoEntity.lastLoginTime = lastLoginTime;
  }
  final int? enableTransferAmount = jsonConvert.convert<int>(
      json['enableTransferAmount']);
  if (enableTransferAmount != null) {
    userInfoEntity.enableTransferAmount = enableTransferAmount;
  }
  final String? realName = jsonConvert.convert<String>(json['realName']);
  if (realName != null) {
    userInfoEntity.realName = realName;
  }
  final int? phoneStatus = jsonConvert.convert<int>(json['phoneStatus']);
  if (phoneStatus != null) {
    userInfoEntity.phoneStatus = phoneStatus;
  }
  final String? language = jsonConvert.convert<String>(json['language']);
  if (language != null) {
    userInfoEntity.language = language;
  }
  return userInfoEntity;
}

Map<String, dynamic> $UserInfoEntityToJson(UserInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['agentId'] = entity.agentId;
  data['agentUserNo'] = entity.agentUserNo;
  data['userNo'] = entity.userNo;
  data['userType'] = entity.userType;
  data['faceId'] = entity.faceId;
  data['nickName'] = entity.nickName;
  data['email'] = entity.email;
  data['phoneNo'] = entity.phoneNo;
  data['gender'] = entity.gender;
  data['birthday'] = entity.birthday;
  data['vipTitle'] = entity.vipTitle;
  data['vipLevel'] = entity.vipLevel;
  data['hasFundPwd'] = entity.hasFundPwd;
  data['movieView'] = entity.tiktokTabVisible;
  data['integral'] = entity.integral;
  data['lastLoginTime'] = entity.lastLoginTime;
  data['enableTransferAmount'] = entity.enableTransferAmount;
  data['realName'] = entity.realName;
  data['phoneStatus'] = entity.phoneStatus;
  data['language'] = entity.language;
  return data;
}

extension UserInfoEntityExtension on UserInfoEntity {
  UserInfoEntity copyWith({
    int? agentId,
    String? agentUserNo,
    String? userNo,
    int? userType,
    int? faceId,
    String? nickName,
    String? email,
    String? phoneNo,
    int? gender,
    dynamic birthday,
    String? vipTitle,
    int? vipLevel,
    bool? hasFundPwd,
    bool? tiktokTabVisible,
    double? integral,
    String? lastLoginTime,
    int? enableTransferAmount,
    String? realName,
    int? phoneStatus,
    String? language,
  }) {
    return UserInfoEntity()
      ..agentId = agentId ?? this.agentId
      ..agentUserNo = agentUserNo ?? this.agentUserNo
      ..userNo = userNo ?? this.userNo
      ..userType = userType ?? this.userType
      ..faceId = faceId ?? this.faceId
      ..nickName = nickName ?? this.nickName
      ..email = email ?? this.email
      ..phoneNo = phoneNo ?? this.phoneNo
      ..gender = gender ?? this.gender
      ..birthday = birthday ?? this.birthday
      ..vipTitle = vipTitle ?? this.vipTitle
      ..vipLevel = vipLevel ?? this.vipLevel
      ..hasFundPwd = hasFundPwd ?? this.hasFundPwd
      ..tiktokTabVisible = tiktokTabVisible ?? this.tiktokTabVisible
      ..integral = integral ?? this.integral
      ..lastLoginTime = lastLoginTime ?? this.lastLoginTime
      ..enableTransferAmount = enableTransferAmount ?? this.enableTransferAmount
      ..realName = realName ?? this.realName
      ..phoneStatus = phoneStatus ?? this.phoneStatus
      ..language = language ?? this.language;
  }
}

UserBalanceEntity $UserBalanceEntityFromJson(Map<String, dynamic> json) {
  final UserBalanceEntity userBalanceEntity = UserBalanceEntity();
  final double? accountMoney = jsonConvert.convert<double>(
      json['accountMoney']);
  if (accountMoney != null) {
    userBalanceEntity.accountMoney = accountMoney;
  }
  final double? lockMoney = jsonConvert.convert<double>(json['lockMoney']);
  if (lockMoney != null) {
    userBalanceEntity.lockMoney = lockMoney;
  }
  return userBalanceEntity;
}

Map<String, dynamic> $UserBalanceEntityToJson(UserBalanceEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['accountMoney'] = entity.accountMoney;
  data['lockMoney'] = entity.lockMoney;
  return data;
}

extension UserBalanceEntityExtension on UserBalanceEntity {
  UserBalanceEntity copyWith({
    double? accountMoney,
    double? lockMoney,
  }) {
    return UserBalanceEntity()
      ..accountMoney = accountMoney ?? this.accountMoney
      ..lockMoney = lockMoney ?? this.lockMoney;
  }
}