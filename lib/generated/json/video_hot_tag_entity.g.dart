import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/video_hot_tag_entity.dart';

VideoHotTagEntity $VideoHotTagEntityFromJson(Map<String, dynamic> json) {
  final VideoHotTagEntity videoHotTagEntity = VideoHotTagEntity();
  final List<String>? areaCategory = (json['areaCategory'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<String>(e) as String)
      .toList();
  if (areaCategory != null) {
    videoHotTagEntity.areaCategory = areaCategory;
  }
  final List<String>? videoYear = (json['videoYear'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (videoYear != null) {
    videoHotTagEntity.videoYear = videoYear;
  }
  final List<
      VideoHotTagMoviesCategory>? moviesCategory = (json['moviesCategory'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<VideoHotTagMoviesCategory>(
          e) as VideoHotTagMoviesCategory).toList();
  if (moviesCategory != null) {
    videoHotTagEntity.moviesCategory = moviesCategory;
  }
  return videoHotTagEntity;
}

Map<String, dynamic> $VideoHotTagEntityToJson(VideoHotTagEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['areaCategory'] = entity.areaCategory;
  data['videoYear'] = entity.videoYear;
  data['moviesCategory'] =
      entity.moviesCategory?.map((v) => v.toJson()).toList();
  return data;
}

extension VideoHotTagEntityExtension on VideoHotTagEntity {
  VideoHotTagEntity copyWith({
    List<String>? areaCategory,
    List<String>? videoYear,
    List<VideoHotTagMoviesCategory>? moviesCategory,
  }) {
    return VideoHotTagEntity()
      ..areaCategory = areaCategory ?? this.areaCategory
      ..videoYear = videoYear ?? this.videoYear
      ..moviesCategory = moviesCategory ?? this.moviesCategory;
  }
}

VideoHotTagMoviesCategory $VideoHotTagMoviesCategoryFromJson(
    Map<String, dynamic> json) {
  final VideoHotTagMoviesCategory videoHotTagMoviesCategory = VideoHotTagMoviesCategory();
  final String? dictKey = jsonConvert.convert<String>(json['dict_key']);
  if (dictKey != null) {
    videoHotTagMoviesCategory.dictKey = dictKey;
  }
  final String? dictValue = jsonConvert.convert<String>(json['dict_value']);
  if (dictValue != null) {
    videoHotTagMoviesCategory.dictValue = dictValue;
  }
  return videoHotTagMoviesCategory;
}

Map<String, dynamic> $VideoHotTagMoviesCategoryToJson(
    VideoHotTagMoviesCategory entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['dict_key'] = entity.dictKey;
  data['dict_value'] = entity.dictValue;
  return data;
}

extension VideoHotTagMoviesCategoryExtension on VideoHotTagMoviesCategory {
  VideoHotTagMoviesCategory copyWith({
    String? dictKey,
    String? dictValue,
  }) {
    return VideoHotTagMoviesCategory()
      ..dictKey = dictKey ?? this.dictKey
      ..dictValue = dictValue ?? this.dictValue;
  }
}