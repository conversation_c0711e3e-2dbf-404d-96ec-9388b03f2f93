import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/game_entity.dart';
import 'package:equatable/equatable.dart';

import 'package:game_store/core/constants/constants.dart';

import 'package:game_store/core/models/entities/lottery_entity.dart';

import 'package:game_store/core/models/entities/popular_and_collection_game_entity.dart';


GameListEntity $GameListEntityFromJson(Map<String, dynamic> json) {
  final GameListEntity gameListEntity = GameListEntity();
  final List<
      PlatformInfo>? thirdPlatformList = (json['thirdPlatformList'] as List<
      dynamic>?)?.map(
          (e) => jsonConvert.convert<PlatformInfo>(e) as PlatformInfo).toList();
  if (thirdPlatformList != null) {
    gameListEntity.thirdPlatformList = thirdPlatformList;
  }
  final List<GameType>? gameList = (json['gameList'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<GameType>(e) as GameType).toList();
  if (gameList != null) {
    gameListEntity.gameList = gameList;
  }
  return gameListEntity;
}

Map<String, dynamic> $GameListEntityToJson(GameListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['thirdPlatformList'] =
      entity.thirdPlatformList.map((v) => v.toJson()).toList();
  data['gameList'] = entity.gameList.map((v) => v.toJson()).toList();
  return data;
}

extension GameListEntityExtension on GameListEntity {
  GameListEntity copyWith({
    List<PlatformInfo>? thirdPlatformList,
    List<GameType>? gameList,
  }) {
    return GameListEntity()
      ..thirdPlatformList = thirdPlatformList ?? this.thirdPlatformList
      ..gameList = gameList ?? this.gameList;
  }
}

PlatformInfo $PlatformInfoFromJson(Map<String, dynamic> json) {
  final PlatformInfo platformInfo = PlatformInfo();
  final String? platformName = jsonConvert.convert<String>(
      json['platformName']);
  if (platformName != null) {
    platformInfo.platformName = platformName;
  }
  final String? platformCode = jsonConvert.convert<String>(
      json['platformCode']);
  if (platformCode != null) {
    platformInfo.platformCode = platformCode;
  }
  final String? iconUrl = jsonConvert.convert<String>(json['iconUrl']);
  if (iconUrl != null) {
    platformInfo.iconUrl = iconUrl;
  }
  return platformInfo;
}

Map<String, dynamic> $PlatformInfoToJson(PlatformInfo entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['platformName'] = entity.platformName;
  data['platformCode'] = entity.platformCode;
  data['iconUrl'] = entity.iconUrl;
  return data;
}

extension PlatformInfoExtension on PlatformInfo {
  PlatformInfo copyWith({
    String? platformName,
    String? platformCode,
    String? iconUrl,
  }) {
    return PlatformInfo()
      ..platformName = platformName ?? this.platformName
      ..platformCode = platformCode ?? this.platformCode
      ..iconUrl = iconUrl ?? this.iconUrl;
  }
}

GameType $GameTypeFromJson(Map<String, dynamic> json) {
  final GameType gameType = GameType();
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    gameType.name = name;
  }
  final String? code = jsonConvert.convert<String>(json['code']);
  if (code != null) {
    gameType.code = code;
  }
  final List<GamePlatform>? data = (json['data'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<GamePlatform>(e) as GamePlatform).toList();
  if (data != null) {
    gameType.data = data;
  }
  final List? extraList = (json['extraList'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (extraList != null) {
    gameType.extraList = extraList;
  }
  final bool? isHot = jsonConvert.convert<bool>(json['isHot']);
  if (isHot != null) {
    gameType.isHot = isHot;
  }
  final double? sectionHeight = jsonConvert.convert<double>(
      json['sectionHeight']);
  if (sectionHeight != null) {
    gameType.sectionHeight = sectionHeight;
  }
  return gameType;
}

Map<String, dynamic> $GameTypeToJson(GameType entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['name'] = entity.name;
  data['code'] = entity.code;
  data['data'] = entity.data.map((v) => v.toJson()).toList();
  data['extraList'] = entity.extraList;
  data['isHot'] = entity.isHot;
  data['sectionHeight'] = entity.sectionHeight;
  return data;
}

extension GameTypeExtension on GameType {
  GameType copyWith({
    String? name,
    String? code,
    List<GamePlatform>? data,
    List? extraList,
    bool? isHot,
    double? sectionHeight,
  }) {
    return GameType()
      ..name = name ?? this.name
      ..code = code ?? this.code
      ..data = data ?? this.data
      ..extraList = extraList ?? this.extraList
      ..isHot = isHot ?? this.isHot
      ..sectionHeight = sectionHeight ?? this.sectionHeight;
  }
}

GamePlatform $GamePlatformFromJson(Map<String, dynamic> json) {
  final GamePlatform gamePlatform = GamePlatform();
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    gamePlatform.name = name;
  }
  final String? platformCode = jsonConvert.convert<String>(
      json['platformCode']);
  if (platformCode != null) {
    gamePlatform.platformCode = platformCode;
  }
  final bool? isLogin = jsonConvert.convert<bool>(json['isLogin']);
  if (isLogin != null) {
    gamePlatform.isLogin = isLogin;
  }
  final String? gameClassCode = jsonConvert.convert<String>(
      json['gameClassCode']);
  if (gameClassCode != null) {
    gamePlatform.gameClassCode = gameClassCode;
  }
  final int? gameType = jsonConvert.convert<int>(json['gameType']);
  if (gameType != null) {
    gamePlatform.gameType = gameType;
  }
  final String? iconUrl = jsonConvert.convert<String>(json['iconUrl']);
  if (iconUrl != null) {
    gamePlatform.iconUrl = iconUrl;
  }
  final List<Game>? data = (json['data'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<Game>(e) as Game).toList();
  if (data != null) {
    gamePlatform.data = data;
  }
  final dynamic extraData = json['extraData'];
  if (extraData != null) {
    gamePlatform.extraData = extraData;
  }
  return gamePlatform;
}

Map<String, dynamic> $GamePlatformToJson(GamePlatform entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['name'] = entity.name;
  data['platformCode'] = entity.platformCode;
  data['isLogin'] = entity.isLogin;
  data['gameClassCode'] = entity.gameClassCode;
  data['gameType'] = entity.gameType;
  data['iconUrl'] = entity.iconUrl;
  data['data'] = entity.data.map((v) => v.toJson()).toList();
  data['extraData'] = entity.extraData;
  return data;
}

extension GamePlatformExtension on GamePlatform {
  GamePlatform copyWith({
    String? name,
    String? platformCode,
    bool? isLogin,
    String? gameClassCode,
    int? gameType,
    String? iconUrl,
    List<Game>? data,
    dynamic extraData,
  }) {
    return GamePlatform()
      ..name = name ?? this.name
      ..platformCode = platformCode ?? this.platformCode
      ..isLogin = isLogin ?? this.isLogin
      ..gameClassCode = gameClassCode ?? this.gameClassCode
      ..gameType = gameType ?? this.gameType
      ..iconUrl = iconUrl ?? this.iconUrl
      ..data = data ?? this.data
      ..extraData = extraData ?? this.extraData;
  }
}

Game $GameFromJson(Map<String, dynamic> json) {
  final Game game = Game();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    game.id = id;
  }
  final String? gameClassCode = jsonConvert.convert<String>(
      json['gameClassCode']);
  if (gameClassCode != null) {
    game.gameClassCode = gameClassCode;
  }
  final String? gameClassName = jsonConvert.convert<String>(
      json['gameClassName']);
  if (gameClassName != null) {
    game.gameClassName = gameClassName;
  }
  final String? categoryCode = jsonConvert.convert<String>(
      json['categoryCode']);
  if (categoryCode != null) {
    game.categoryCode = categoryCode;
  }
  final String? categoryName = jsonConvert.convert<String>(
      json['categoryName']);
  if (categoryName != null) {
    game.categoryName = categoryName;
  }
  final String? gameCode = jsonConvert.convert<String>(json['gameCode']);
  if (gameCode != null) {
    game.gameCode = gameCode;
  }
  final String? gameName = jsonConvert.convert<String>(json['gameName']);
  if (gameName != null) {
    game.gameName = gameName;
  }
  final int? thirdPlatformId = jsonConvert.convert<int>(
      json['thirdPlatformId']);
  if (thirdPlatformId != null) {
    game.thirdPlatformId = thirdPlatformId;
  }
  final String? thirdPlatformName = jsonConvert.convert<String>(
      json['thirdPlatformName']);
  if (thirdPlatformName != null) {
    game.thirdPlatformName = thirdPlatformName;
  }
  final bool? isDisplay = jsonConvert.convert<bool>(json['isDisplay']);
  if (isDisplay != null) {
    game.isDisplay = isDisplay;
  }
  final bool? isAegis = jsonConvert.convert<bool>(json['isAegis']);
  if (isAegis != null) {
    game.isAegis = isAegis;
  }
  final int? weight = jsonConvert.convert<int>(json['weight']);
  if (weight != null) {
    game.weight = weight;
  }
  final String? iconUrl = jsonConvert.convert<String>(json['iconUrl']);
  if (iconUrl != null) {
    game.iconUrl = iconUrl;
  }
  final bool? savour = jsonConvert.convert<bool>(json['savour']);
  if (savour != null) {
    game.savour = savour;
  }
  final int? gameType = jsonConvert.convert<int>(json['gameType']);
  if (gameType != null) {
    game.gameType = gameType;
  }
  return game;
}

Map<String, dynamic> $GameToJson(Game entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['gameClassCode'] = entity.gameClassCode;
  data['gameClassName'] = entity.gameClassName;
  data['categoryCode'] = entity.categoryCode;
  data['categoryName'] = entity.categoryName;
  data['gameCode'] = entity.gameCode;
  data['gameName'] = entity.gameName;
  data['thirdPlatformId'] = entity.thirdPlatformId;
  data['thirdPlatformName'] = entity.thirdPlatformName;
  data['isDisplay'] = entity.isDisplay;
  data['isAegis'] = entity.isAegis;
  data['weight'] = entity.weight;
  data['iconUrl'] = entity.iconUrl;
  data['savour'] = entity.savour;
  data['gameType'] = entity.gameType;
  return data;
}

extension GameExtension on Game {
  Game copyWith({
    int? id,
    String? gameClassCode,
    String? gameClassName,
    String? categoryCode,
    String? categoryName,
    String? gameCode,
    String? gameName,
    int? thirdPlatformId,
    String? thirdPlatformName,
    bool? isDisplay,
    bool? isAegis,
    int? weight,
    String? iconUrl,
    bool? savour,
    int? gameType,
  }) {
    return Game()
      ..id = id ?? this.id
      ..gameClassCode = gameClassCode ?? this.gameClassCode
      ..gameClassName = gameClassName ?? this.gameClassName
      ..categoryCode = categoryCode ?? this.categoryCode
      ..categoryName = categoryName ?? this.categoryName
      ..gameCode = gameCode ?? this.gameCode
      ..gameName = gameName ?? this.gameName
      ..thirdPlatformId = thirdPlatformId ?? this.thirdPlatformId
      ..thirdPlatformName = thirdPlatformName ?? this.thirdPlatformName
      ..isDisplay = isDisplay ?? this.isDisplay
      ..isAegis = isAegis ?? this.isAegis
      ..weight = weight ?? this.weight
      ..iconUrl = iconUrl ?? this.iconUrl
      ..savour = savour ?? this.savour
      ..gameType = gameType ?? this.gameType;
  }
}