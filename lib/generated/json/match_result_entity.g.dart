import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/match_result_entity.dart';

MatchResultEntity $MatchResultEntityFromJson(Map<String, dynamic> json) {
  final MatchResultEntity matchResultEntity = MatchResultEntity();
  final String? issue = jsonConvert.convert<String>(json['issue']);
  if (issue != null) {
    matchResultEntity.issue = issue;
  }
  final String? issueTime = jsonConvert.convert<String>(json['issueTime']);
  if (issueTime != null) {
    matchResultEntity.issueTime = issueTime;
  }
  final String? result = jsonConvert.convert<String>(json['result']);
  if (result != null) {
    matchResultEntity.result = result;
  }
  final List<
      dynamic>? recentOneHandredCount = (json['recentOneHandredCount'] as List<
      dynamic>?)?.map(
          (e) => e).toList();
  if (recentOneHandredCount != null) {
    matchResultEntity.recentOneHandredCount = recentOneHandredCount;
  }
  return matchResultEntity;
}

Map<String, dynamic> $MatchResultEntityToJson(MatchResultEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['issue'] = entity.issue;
  data['issueTime'] = entity.issueTime;
  data['result'] = entity.result;
  data['recentOneHandredCount'] = entity.recentOneHandredCount;
  return data;
}

extension MatchResultEntityExtension on MatchResultEntity {
  MatchResultEntity copyWith({
    String? issue,
    String? issueTime,
    String? result,
    List<dynamic>? recentOneHandredCount,
  }) {
    return MatchResultEntity()
      ..issue = issue ?? this.issue
      ..issueTime = issueTime ?? this.issueTime
      ..result = result ?? this.result
      ..recentOneHandredCount = recentOneHandredCount ??
          this.recentOneHandredCount;
  }
}