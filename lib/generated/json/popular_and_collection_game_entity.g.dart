import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/popular_and_collection_game_entity.dart';

PopularAndCollectionGameEntity $PopularAndCollectionGameEntityFromJson(
    Map<String, dynamic> json) {
  final PopularAndCollectionGameEntity popularAndCollectionGameEntity = PopularAndCollectionGameEntity();
  final List<PopularGame>? userSavourGame = (json['userSavourGame'] as List<
      dynamic>?)?.map(
          (e) => jsonConvert.convert<PopularGame>(e) as PopularGame).toList();
  if (userSavourGame != null) {
    popularAndCollectionGameEntity.userSavourGame = userSavourGame;
  }
  final List<PopularGame>? popularGame = (json['popularGame'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<PopularGame>(e) as PopularGame)
      .toList();
  if (popularGame != null) {
    popularAndCollectionGameEntity.popularGame = popularGame;
  }
  final List<PopularVenue>? popularVenue = (json['popularVenue'] as List<
      dynamic>?)?.map(
          (e) => jsonConvert.convert<PopularVenue>(e) as PopularVenue).toList();
  if (popularVenue != null) {
    popularAndCollectionGameEntity.popularVenue = popularVenue;
  }
  return popularAndCollectionGameEntity;
}

Map<String, dynamic> $PopularAndCollectionGameEntityToJson(
    PopularAndCollectionGameEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['userSavourGame'] =
      entity.userSavourGame.map((v) => v.toJson()).toList();
  data['popularGame'] = entity.popularGame.map((v) => v.toJson()).toList();
  data['popularVenue'] = entity.popularVenue.map((v) => v.toJson()).toList();
  return data;
}

extension PopularAndCollectionGameEntityExtension on PopularAndCollectionGameEntity {
  PopularAndCollectionGameEntity copyWith({
    List<PopularGame>? userSavourGame,
    List<PopularGame>? popularGame,
    List<PopularVenue>? popularVenue,
  }) {
    return PopularAndCollectionGameEntity()
      ..userSavourGame = userSavourGame ?? this.userSavourGame
      ..popularGame = popularGame ?? this.popularGame
      ..popularVenue = popularVenue ?? this.popularVenue;
  }
}

PopularVenue $PopularVenueFromJson(Map<String, dynamic> json) {
  final PopularVenue popularVenue = PopularVenue();
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    popularVenue.name = name;
  }
  final int? platformId = jsonConvert.convert<int>(json['platformId']);
  if (platformId != null) {
    popularVenue.platformId = platformId;
  }
  final String? platformCode = jsonConvert.convert<String>(
      json['platformCode']);
  if (platformCode != null) {
    popularVenue.platformCode = platformCode;
  }
  final String? iconUrl = jsonConvert.convert<String>(json['iconUrl']);
  if (iconUrl != null) {
    popularVenue.iconUrl = iconUrl;
  }
  final String? recommendTime = jsonConvert.convert<String>(
      json['recommendTime']);
  if (recommendTime != null) {
    popularVenue.recommendTime = recommendTime;
  }
  final int? gameType = jsonConvert.convert<int>(json['gameType']);
  if (gameType != null) {
    popularVenue.gameType = gameType;
  }
  final bool? isLogin = jsonConvert.convert<bool>(json['isLogin']);
  if (isLogin != null) {
    popularVenue.isLogin = isLogin;
  }
  final dynamic clickNum = json['clickNum'];
  if (clickNum != null) {
    popularVenue.clickNum = clickNum;
  }
  final dynamic popularGames = json['popularGames'];
  if (popularGames != null) {
    popularVenue.popularGames = popularGames;
  }
  return popularVenue;
}

Map<String, dynamic> $PopularVenueToJson(PopularVenue entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['name'] = entity.name;
  data['platformId'] = entity.platformId;
  data['platformCode'] = entity.platformCode;
  data['iconUrl'] = entity.iconUrl;
  data['recommendTime'] = entity.recommendTime;
  data['gameType'] = entity.gameType;
  data['isLogin'] = entity.isLogin;
  data['clickNum'] = entity.clickNum;
  data['popularGames'] = entity.popularGames;
  return data;
}

extension PopularVenueExtension on PopularVenue {
  PopularVenue copyWith({
    String? name,
    int? platformId,
    String? platformCode,
    String? iconUrl,
    String? recommendTime,
    int? gameType,
    bool? isLogin,
    dynamic clickNum,
    dynamic popularGames,
  }) {
    return PopularVenue()
      ..name = name ?? this.name
      ..platformId = platformId ?? this.platformId
      ..platformCode = platformCode ?? this.platformCode
      ..iconUrl = iconUrl ?? this.iconUrl
      ..recommendTime = recommendTime ?? this.recommendTime
      ..gameType = gameType ?? this.gameType
      ..isLogin = isLogin ?? this.isLogin
      ..clickNum = clickNum ?? this.clickNum
      ..popularGames = popularGames ?? this.popularGames;
  }
}

PopularGame $PopularGameFromJson(Map<String, dynamic> json) {
  final PopularGame popularGame = PopularGame();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    popularGame.id = id;
  }
  final String? gameClassCode = jsonConvert.convert<String>(
      json['gameClassCode']);
  if (gameClassCode != null) {
    popularGame.gameClassCode = gameClassCode;
  }
  final String? gameClassName = jsonConvert.convert<String>(
      json['gameClassName']);
  if (gameClassName != null) {
    popularGame.gameClassName = gameClassName;
  }
  final String? categoryCode = jsonConvert.convert<String>(
      json['categoryCode']);
  if (categoryCode != null) {
    popularGame.categoryCode = categoryCode;
  }
  final String? gameCode = jsonConvert.convert<String>(json['gameCode']);
  if (gameCode != null) {
    popularGame.gameCode = gameCode;
  }
  final String? gameName = jsonConvert.convert<String>(json['gameName']);
  if (gameName != null) {
    popularGame.gameName = gameName;
  }
  final int? platformId = jsonConvert.convert<int>(json['platformId']);
  if (platformId != null) {
    popularGame.platformId = platformId;
  }
  final String? platformName = jsonConvert.convert<String>(
      json['platformName']);
  if (platformName != null) {
    popularGame.platformName = platformName;
  }
  final String? platformCode = jsonConvert.convert<String>(
      json['platformCode']);
  if (platformCode != null) {
    popularGame.platformCode = platformCode;
  }
  final String? iconUrl = jsonConvert.convert<String>(json['iconUrl']);
  if (iconUrl != null) {
    popularGame.iconUrl = iconUrl;
  }
  final int? gameType = jsonConvert.convert<int>(json['gameType']);
  if (gameType != null) {
    popularGame.gameType = gameType;
  }
  final String? recommendTime = jsonConvert.convert<String>(
      json['recommendTime']);
  if (recommendTime != null) {
    popularGame.recommendTime = recommendTime;
  }
  final bool? isLogin = jsonConvert.convert<bool>(json['isLogin']);
  if (isLogin != null) {
    popularGame.isLogin = isLogin;
  }
  final dynamic clickNum = json['clickNum'];
  if (clickNum != null) {
    popularGame.clickNum = clickNum;
  }
  final bool? savour = jsonConvert.convert<bool>(json['savour']);
  if (savour != null) {
    popularGame.savour = savour;
  }
  return popularGame;
}

Map<String, dynamic> $PopularGameToJson(PopularGame entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['gameClassCode'] = entity.gameClassCode;
  data['gameClassName'] = entity.gameClassName;
  data['categoryCode'] = entity.categoryCode;
  data['gameCode'] = entity.gameCode;
  data['gameName'] = entity.gameName;
  data['platformId'] = entity.platformId;
  data['platformName'] = entity.platformName;
  data['platformCode'] = entity.platformCode;
  data['iconUrl'] = entity.iconUrl;
  data['gameType'] = entity.gameType;
  data['recommendTime'] = entity.recommendTime;
  data['isLogin'] = entity.isLogin;
  data['clickNum'] = entity.clickNum;
  data['savour'] = entity.savour;
  return data;
}

extension PopularGameExtension on PopularGame {
  PopularGame copyWith({
    int? id,
    String? gameClassCode,
    String? gameClassName,
    String? categoryCode,
    String? gameCode,
    String? gameName,
    int? platformId,
    String? platformName,
    String? platformCode,
    String? iconUrl,
    int? gameType,
    String? recommendTime,
    bool? isLogin,
    dynamic clickNum,
    bool? savour,
  }) {
    return PopularGame()
      ..id = id ?? this.id
      ..gameClassCode = gameClassCode ?? this.gameClassCode
      ..gameClassName = gameClassName ?? this.gameClassName
      ..categoryCode = categoryCode ?? this.categoryCode
      ..gameCode = gameCode ?? this.gameCode
      ..gameName = gameName ?? this.gameName
      ..platformId = platformId ?? this.platformId
      ..platformName = platformName ?? this.platformName
      ..platformCode = platformCode ?? this.platformCode
      ..iconUrl = iconUrl ?? this.iconUrl
      ..gameType = gameType ?? this.gameType
      ..recommendTime = recommendTime ?? this.recommendTime
      ..isLogin = isLogin ?? this.isLogin
      ..clickNum = clickNum ?? this.clickNum
      ..savour = savour ?? this.savour;
  }
}