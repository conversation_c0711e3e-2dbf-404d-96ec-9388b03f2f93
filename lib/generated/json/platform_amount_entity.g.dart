import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/platform_amount_entity.dart';

PlatformAmountEntityList $PlatformAmountEntityListFromJson(
    Map<String, dynamic> json) {
  final PlatformAmountEntityList platformAmountEntityList = PlatformAmountEntityList();
  final List<PlatformAmountEntity>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<PlatformAmountEntity>(e) as PlatformAmountEntity)
      .toList();
  if (list != null) {
    platformAmountEntityList.list = list;
  }
  return platformAmountEntityList;
}

Map<String, dynamic> $PlatformAmountEntityListToJson(
    PlatformAmountEntityList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension PlatformAmountEntityListExtension on PlatformAmountEntityList {
  PlatformAmountEntityList copyWith({
    List<PlatformAmountEntity>? list,
  }) {
    return PlatformAmountEntityList()
      ..list = list ?? this.list;
  }
}

PlatformAmountEntity $PlatformAmountEntityFromJson(Map<String, dynamic> json) {
  final PlatformAmountEntity platformAmountEntity = PlatformAmountEntity();
  final int? platformId = jsonConvert.convert<int>(json['platformId']);
  if (platformId != null) {
    platformAmountEntity.platformId = platformId;
  }
  final String? platformName = jsonConvert.convert<String>(
      json['platformName']);
  if (platformName != null) {
    platformAmountEntity.platformName = platformName;
  }
  final double? amount = jsonConvert.convert<double>(json['amount']);
  if (amount != null) {
    platformAmountEntity.amount = amount;
  }
  final bool? isLoading = jsonConvert.convert<bool>(json['isLoading']);
  if (isLoading != null) {
    platformAmountEntity.isLoading = isLoading;
  }
  return platformAmountEntity;
}

Map<String, dynamic> $PlatformAmountEntityToJson(PlatformAmountEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['platformId'] = entity.platformId;
  data['platformName'] = entity.platformName;
  data['amount'] = entity.amount;
  data['isLoading'] = entity.isLoading;
  return data;
}

extension PlatformAmountEntityExtension on PlatformAmountEntity {
  PlatformAmountEntity copyWith({
    int? platformId,
    String? platformName,
    double? amount,
    bool? isLoading,
  }) {
    return PlatformAmountEntity()
      ..platformId = platformId ?? this.platformId
      ..platformName = platformName ?? this.platformName
      ..amount = amount ?? this.amount
      ..isLoading = isLoading ?? this.isLoading;
  }
}