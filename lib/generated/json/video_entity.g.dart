import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/video_entity.dart';

VideoEntityList $VideoEntityListFromJson(Map<String, dynamic> json) {
  final VideoEntityList videoEntityList = VideoEntityList();
  final List<VideoEntity>? records = (json['records'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<VideoEntity>(e) as VideoEntity).toList();
  if (records != null) {
    videoEntityList.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    videoEntityList.total = total;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    videoEntityList.size = size;
  }
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    videoEntityList.current = current;
  }
  final int? pages = jsonConvert.convert<int>(json['pages']);
  if (pages != null) {
    videoEntityList.pages = pages;
  }
  return videoEntityList;
}

Map<String, dynamic> $VideoEntityListToJson(VideoEntityList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  data['size'] = entity.size;
  data['current'] = entity.current;
  data['pages'] = entity.pages;
  return data;
}

extension VideoEntityListExtension on VideoEntityList {
  VideoEntityList copyWith({
    List<VideoEntity>? records,
    int? total,
    int? size,
    int? current,
    int? pages,
  }) {
    return VideoEntityList()
      ..records = records ?? this.records
      ..total = total ?? this.total
      ..size = size ?? this.size
      ..current = current ?? this.current
      ..pages = pages ?? this.pages;
  }
}

VideoEntity $VideoEntityFromJson(Map<String, dynamic> json) {
  final VideoEntity videoEntity = VideoEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    videoEntity.id = id;
  }
  final String? videoImage = jsonConvert.convert<String>(json['videoImage']);
  if (videoImage != null) {
    videoEntity.videoImage = videoImage;
  }
  final String? videoTitle = jsonConvert.convert<String>(json['videoTitle']);
  if (videoTitle != null) {
    videoEntity.videoTitle = videoTitle;
  }
  final String? videoYear = jsonConvert.convert<String>(json['videoYear']);
  if (videoYear != null) {
    videoEntity.videoYear = videoYear;
  }
  final String? videoTags = jsonConvert.convert<String>(json['videoTags']);
  if (videoTags != null) {
    videoEntity.videoTags = videoTags;
  }
  final String? videoCountry = jsonConvert.convert<String>(
      json['videoCountry']);
  if (videoCountry != null) {
    videoEntity.videoCountry = videoCountry;
  }
  final String? videoClarity = jsonConvert.convert<String>(
      json['videoClarity']);
  if (videoClarity != null) {
    videoEntity.videoClarity = videoClarity;
  }
  final String? videoBottomTag = jsonConvert.convert<String>(
      json['videoBottomTag']);
  if (videoBottomTag != null) {
    videoEntity.videoBottomTag = videoBottomTag;
  }
  final int? hide = jsonConvert.convert<int>(json['hide']);
  if (hide != null) {
    videoEntity.hide = hide;
  }
  final List? links = (json['links'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (links != null) {
    videoEntity.links = links;
  }
  return videoEntity;
}

Map<String, dynamic> $VideoEntityToJson(VideoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['videoImage'] = entity.videoImage;
  data['videoTitle'] = entity.videoTitle;
  data['videoYear'] = entity.videoYear;
  data['videoTags'] = entity.videoTags;
  data['videoCountry'] = entity.videoCountry;
  data['videoClarity'] = entity.videoClarity;
  data['videoBottomTag'] = entity.videoBottomTag;
  data['hide'] = entity.hide;
  data['links'] = entity.links;
  return data;
}

extension VideoEntityExtension on VideoEntity {
  VideoEntity copyWith({
    int? id,
    String? videoImage,
    String? videoTitle,
    String? videoYear,
    String? videoTags,
    String? videoCountry,
    String? videoClarity,
    String? videoBottomTag,
    int? hide,
    List? links,
  }) {
    return VideoEntity()
      ..id = id ?? this.id
      ..videoImage = videoImage ?? this.videoImage
      ..videoTitle = videoTitle ?? this.videoTitle
      ..videoYear = videoYear ?? this.videoYear
      ..videoTags = videoTags ?? this.videoTags
      ..videoCountry = videoCountry ?? this.videoCountry
      ..videoClarity = videoClarity ?? this.videoClarity
      ..videoBottomTag = videoBottomTag ?? this.videoBottomTag
      ..hide = hide ?? this.hide
      ..links = links ?? this.links;
  }
}

VideoVipRemainDayEntity $VideoVipRemainDayEntityFromJson(
    Map<String, dynamic> json) {
  final VideoVipRemainDayEntity videoVipRemainDayEntity = VideoVipRemainDayEntity();
  final int? days = jsonConvert.convert<int>(json['days']);
  if (days != null) {
    videoVipRemainDayEntity.days = days;
  }
  final String? expiredDate = jsonConvert.convert<String>(json['expiredDate']);
  if (expiredDate != null) {
    videoVipRemainDayEntity.expiredDate = expiredDate;
  }
  return videoVipRemainDayEntity;
}

Map<String, dynamic> $VideoVipRemainDayEntityToJson(
    VideoVipRemainDayEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['days'] = entity.days;
  data['expiredDate'] = entity.expiredDate;
  return data;
}

extension VideoVipRemainDayEntityExtension on VideoVipRemainDayEntity {
  VideoVipRemainDayEntity copyWith({
    int? days,
    String? expiredDate,
  }) {
    return VideoVipRemainDayEntity()
      ..days = days ?? this.days
      ..expiredDate = expiredDate ?? this.expiredDate;
  }
}