import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/withdraw_user_bank_list_entity.dart';

WithdrawUserBankBriefList $WithdrawUserBankBriefListFromJson(
    Map<String, dynamic> json) {
  final WithdrawUserBankBriefList withdrawUserBankBriefList = WithdrawUserBankBriefList();
  final List<WithdrawUserBankBrief>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<WithdrawUserBankBrief>(e) as WithdrawUserBankBrief)
      .toList();
  if (list != null) {
    withdrawUserBankBriefList.list = list;
  }
  return withdrawUserBankBriefList;
}

Map<String, dynamic> $WithdrawUserBankBriefListToJson(
    WithdrawUserBankBriefList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension WithdrawUserBankBriefListExtension on WithdrawUserBankBriefList {
  WithdrawUserBankBriefList copyWith({
    List<WithdrawUserBankBrief>? list,
  }) {
    return WithdrawUserBankBriefList()
      ..list = list ?? this.list;
  }
}

WithdrawUserBankBrief $WithdrawUserBankBriefFromJson(
    Map<String, dynamic> json) {
  final WithdrawUserBankBrief withdrawUserBankBrief = WithdrawUserBankBrief();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    withdrawUserBankBrief.id = id;
  }
  final int? userId = jsonConvert.convert<int>(json['userId']);
  if (userId != null) {
    withdrawUserBankBrief.userId = userId;
  }
  final String? userNo = jsonConvert.convert<String>(json['userNo']);
  if (userNo != null) {
    withdrawUserBankBrief.userNo = userNo;
  }
  final String? bankCode = jsonConvert.convert<String>(json['bankCode']);
  if (bankCode != null) {
    withdrawUserBankBrief.bankCode = bankCode;
  }
  final String? bankName = jsonConvert.convert<String>(json['bankName']);
  if (bankName != null) {
    withdrawUserBankBrief.bankName = bankName;
  }
  final dynamic province = json['province'];
  if (province != null) {
    withdrawUserBankBrief.province = province;
  }
  final dynamic city = json['city'];
  if (city != null) {
    withdrawUserBankBrief.city = city;
  }
  final String? branchBankAddress = jsonConvert.convert<String>(
      json['branchBankAddress']);
  if (branchBankAddress != null) {
    withdrawUserBankBrief.branchBankAddress = branchBankAddress;
  }
  final String? realName = jsonConvert.convert<String>(json['realName']);
  if (realName != null) {
    withdrawUserBankBrief.realName = realName;
  }
  final String? cardNo = jsonConvert.convert<String>(json['cardNo']);
  if (cardNo != null) {
    withdrawUserBankBrief.cardNo = cardNo;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    withdrawUserBankBrief.createTime = createTime;
  }
  final double? withdrawnCount = jsonConvert.convert<double>(
      json['withdrawnCount']);
  if (withdrawnCount != null) {
    withdrawUserBankBrief.withdrawnCount = withdrawnCount;
  }
  final int? useStatus = jsonConvert.convert<int>(json['useStatus']);
  if (useStatus != null) {
    withdrawUserBankBrief.useStatus = useStatus;
  }
  final dynamic mainNetwork = json['mainNetwork'];
  if (mainNetwork != null) {
    withdrawUserBankBrief.mainNetwork = mainNetwork;
  }
  return withdrawUserBankBrief;
}

Map<String, dynamic> $WithdrawUserBankBriefToJson(
    WithdrawUserBankBrief entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['userId'] = entity.userId;
  data['userNo'] = entity.userNo;
  data['bankCode'] = entity.bankCode;
  data['bankName'] = entity.bankName;
  data['province'] = entity.province;
  data['city'] = entity.city;
  data['branchBankAddress'] = entity.branchBankAddress;
  data['realName'] = entity.realName;
  data['cardNo'] = entity.cardNo;
  data['createTime'] = entity.createTime;
  data['withdrawnCount'] = entity.withdrawnCount;
  data['useStatus'] = entity.useStatus;
  data['mainNetwork'] = entity.mainNetwork;
  return data;
}

extension WithdrawUserBankBriefExtension on WithdrawUserBankBrief {
  WithdrawUserBankBrief copyWith({
    int? id,
    int? userId,
    String? userNo,
    String? bankCode,
    String? bankName,
    dynamic province,
    dynamic city,
    String? branchBankAddress,
    String? realName,
    String? cardNo,
    String? createTime,
    double? withdrawnCount,
    int? useStatus,
    dynamic mainNetwork,
  }) {
    return WithdrawUserBankBrief()
      ..id = id ?? this.id
      ..userId = userId ?? this.userId
      ..userNo = userNo ?? this.userNo
      ..bankCode = bankCode ?? this.bankCode
      ..bankName = bankName ?? this.bankName
      ..province = province ?? this.province
      ..city = city ?? this.city
      ..branchBankAddress = branchBankAddress ?? this.branchBankAddress
      ..realName = realName ?? this.realName
      ..cardNo = cardNo ?? this.cardNo
      ..createTime = createTime ?? this.createTime
      ..withdrawnCount = withdrawnCount ?? this.withdrawnCount
      ..useStatus = useStatus ?? this.useStatus
      ..mainNetwork = mainNetwork ?? this.mainNetwork;
  }
}

WithdrawUserBankInfoEntity $WithdrawUserBankInfoEntityFromJson(
    Map<String, dynamic> json) {
  final WithdrawUserBankInfoEntity withdrawUserBankInfoEntity = WithdrawUserBankInfoEntity();
  final double? fundAmount = jsonConvert.convert<double>(json['fundAmount']);
  if (fundAmount != null) {
    withdrawUserBankInfoEntity.fundAmount = fundAmount;
  }
  final double? withdrawalAmount = jsonConvert.convert<double>(
      json['withdrawalAmount']);
  if (withdrawalAmount != null) {
    withdrawUserBankInfoEntity.withdrawalAmount = withdrawalAmount;
  }
  final String? bankName = jsonConvert.convert<String>(json['bankName']);
  if (bankName != null) {
    withdrawUserBankInfoEntity.bankName = bankName;
  }
  final String? cardNo = jsonConvert.convert<String>(json['cardNo']);
  if (cardNo != null) {
    withdrawUserBankInfoEntity.cardNo = cardNo;
  }
  final String? bankCode = jsonConvert.convert<String>(json['bankCode']);
  if (bankCode != null) {
    withdrawUserBankInfoEntity.bankCode = bankCode;
  }
  final int? currentServiceChargeRate = jsonConvert.convert<int>(
      json['currentServiceChargeRate']);
  if (currentServiceChargeRate != null) {
    withdrawUserBankInfoEntity.currentServiceChargeRate =
        currentServiceChargeRate;
  }
  final int? firstServiceChargeRate = jsonConvert.convert<int>(
      json['firstServiceChargeRate']);
  if (firstServiceChargeRate != null) {
    withdrawUserBankInfoEntity.firstServiceChargeRate = firstServiceChargeRate;
  }
  final int? serviceChargeRate = jsonConvert.convert<int>(
      json['serviceChargeRate']);
  if (serviceChargeRate != null) {
    withdrawUserBankInfoEntity.serviceChargeRate = serviceChargeRate;
  }
  final int? serviceChargeMaxLimit = jsonConvert.convert<int>(
      json['serviceChargeMaxLimit']);
  if (serviceChargeMaxLimit != null) {
    withdrawUserBankInfoEntity.serviceChargeMaxLimit = serviceChargeMaxLimit;
  }
  final int? greaterThanEqualAmount = jsonConvert.convert<int>(
      json['greaterThanEqualAmount']);
  if (greaterThanEqualAmount != null) {
    withdrawUserBankInfoEntity.greaterThanEqualAmount = greaterThanEqualAmount;
  }
  final int? thanServiceChargeRate = jsonConvert.convert<int>(
      json['thanServiceChargeRate']);
  if (thanServiceChargeRate != null) {
    withdrawUserBankInfoEntity.thanServiceChargeRate = thanServiceChargeRate;
  }
  final double? amountMaxLimit = jsonConvert.convert<double>(
      json['amountMaxLimit']);
  if (amountMaxLimit != null) {
    withdrawUserBankInfoEntity.amountMaxLimit = amountMaxLimit;
  }
  final double? amountMinLimit = jsonConvert.convert<double>(
      json['amountMinLimit']);
  if (amountMinLimit != null) {
    withdrawUserBankInfoEntity.amountMinLimit = amountMinLimit;
  }
  final int? repeatedHours = jsonConvert.convert<int>(json['repeatedHours']);
  if (repeatedHours != null) {
    withdrawUserBankInfoEntity.repeatedHours = repeatedHours;
  }
  final int? freeTimes = jsonConvert.convert<int>(json['freeTimes']);
  if (freeTimes != null) {
    withdrawUserBankInfoEntity.freeTimes = freeTimes;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    withdrawUserBankInfoEntity.remark = remark;
  }
  final double? virtualCurrencyRate = jsonConvert.convert<double>(
      json['virtualCurrencyRate']);
  if (virtualCurrencyRate != null) {
    withdrawUserBankInfoEntity.virtualCurrencyRate = virtualCurrencyRate;
  }
  final int? userBankInfoId = jsonConvert.convert<int>(json['userBankInfoId']);
  if (userBankInfoId != null) {
    withdrawUserBankInfoEntity.userBankInfoId = userBankInfoId;
  }
  final int? useStatus = jsonConvert.convert<int>(json['useStatus']);
  if (useStatus != null) {
    withdrawUserBankInfoEntity.useStatus = useStatus;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    withdrawUserBankInfoEntity.type = type;
  }
  return withdrawUserBankInfoEntity;
}

Map<String, dynamic> $WithdrawUserBankInfoEntityToJson(
    WithdrawUserBankInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['fundAmount'] = entity.fundAmount;
  data['withdrawalAmount'] = entity.withdrawalAmount;
  data['bankName'] = entity.bankName;
  data['cardNo'] = entity.cardNo;
  data['bankCode'] = entity.bankCode;
  data['currentServiceChargeRate'] = entity.currentServiceChargeRate;
  data['firstServiceChargeRate'] = entity.firstServiceChargeRate;
  data['serviceChargeRate'] = entity.serviceChargeRate;
  data['serviceChargeMaxLimit'] = entity.serviceChargeMaxLimit;
  data['greaterThanEqualAmount'] = entity.greaterThanEqualAmount;
  data['thanServiceChargeRate'] = entity.thanServiceChargeRate;
  data['amountMaxLimit'] = entity.amountMaxLimit;
  data['amountMinLimit'] = entity.amountMinLimit;
  data['repeatedHours'] = entity.repeatedHours;
  data['freeTimes'] = entity.freeTimes;
  data['remark'] = entity.remark;
  data['virtualCurrencyRate'] = entity.virtualCurrencyRate;
  data['userBankInfoId'] = entity.userBankInfoId;
  data['useStatus'] = entity.useStatus;
  data['type'] = entity.type;
  return data;
}

extension WithdrawUserBankInfoEntityExtension on WithdrawUserBankInfoEntity {
  WithdrawUserBankInfoEntity copyWith({
    double? fundAmount,
    double? withdrawalAmount,
    String? bankName,
    String? cardNo,
    String? bankCode,
    int? currentServiceChargeRate,
    int? firstServiceChargeRate,
    int? serviceChargeRate,
    int? serviceChargeMaxLimit,
    int? greaterThanEqualAmount,
    int? thanServiceChargeRate,
    double? amountMaxLimit,
    double? amountMinLimit,
    int? repeatedHours,
    int? freeTimes,
    String? remark,
    double? virtualCurrencyRate,
    int? userBankInfoId,
    int? useStatus,
    int? type,
  }) {
    return WithdrawUserBankInfoEntity()
      ..fundAmount = fundAmount ?? this.fundAmount
      ..withdrawalAmount = withdrawalAmount ?? this.withdrawalAmount
      ..bankName = bankName ?? this.bankName
      ..cardNo = cardNo ?? this.cardNo
      ..bankCode = bankCode ?? this.bankCode
      ..currentServiceChargeRate = currentServiceChargeRate ??
          this.currentServiceChargeRate
      ..firstServiceChargeRate = firstServiceChargeRate ??
          this.firstServiceChargeRate
      ..serviceChargeRate = serviceChargeRate ?? this.serviceChargeRate
      ..serviceChargeMaxLimit = serviceChargeMaxLimit ??
          this.serviceChargeMaxLimit
      ..greaterThanEqualAmount = greaterThanEqualAmount ??
          this.greaterThanEqualAmount
      ..thanServiceChargeRate = thanServiceChargeRate ??
          this.thanServiceChargeRate
      ..amountMaxLimit = amountMaxLimit ?? this.amountMaxLimit
      ..amountMinLimit = amountMinLimit ?? this.amountMinLimit
      ..repeatedHours = repeatedHours ?? this.repeatedHours
      ..freeTimes = freeTimes ?? this.freeTimes
      ..remark = remark ?? this.remark
      ..virtualCurrencyRate = virtualCurrencyRate ?? this.virtualCurrencyRate
      ..userBankInfoId = userBankInfoId ?? this.userBankInfoId
      ..useStatus = useStatus ?? this.useStatus
      ..type = type ?? this.type;
  }
}

WithdrawManualChannelList $WithdrawManualChannelListFromJson(
    Map<String, dynamic> json) {
  final WithdrawManualChannelList withdrawManualChannelList = WithdrawManualChannelList();
  final List<WithdrawManualChannelEntity>? list = (json['list'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<WithdrawManualChannelEntity>(
          e) as WithdrawManualChannelEntity).toList();
  if (list != null) {
    withdrawManualChannelList.list = list;
  }
  return withdrawManualChannelList;
}

Map<String, dynamic> $WithdrawManualChannelListToJson(
    WithdrawManualChannelList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension WithdrawManualChannelListExtension on WithdrawManualChannelList {
  WithdrawManualChannelList copyWith({
    List<WithdrawManualChannelEntity>? list,
  }) {
    return WithdrawManualChannelList()
      ..list = list ?? this.list;
  }
}

WithdrawManualChannelEntity $WithdrawManualChannelEntityFromJson(
    Map<String, dynamic> json) {
  final WithdrawManualChannelEntity withdrawManualChannelEntity = WithdrawManualChannelEntity();
  final String? icon = jsonConvert.convert<String>(json['icon']);
  if (icon != null) {
    withdrawManualChannelEntity.icon = icon;
  }
  final String? channelName = jsonConvert.convert<String>(json['channelName']);
  if (channelName != null) {
    withdrawManualChannelEntity.channelName = channelName;
  }
  final int? channelId = jsonConvert.convert<int>(json['channelId']);
  if (channelId != null) {
    withdrawManualChannelEntity.channelId = channelId;
  }
  final double? rate = jsonConvert.convert<double>(json['rate']);
  if (rate != null) {
    withdrawManualChannelEntity.rate = rate;
  }
  return withdrawManualChannelEntity;
}

Map<String, dynamic> $WithdrawManualChannelEntityToJson(
    WithdrawManualChannelEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['icon'] = entity.icon;
  data['channelName'] = entity.channelName;
  data['channelId'] = entity.channelId;
  data['rate'] = entity.rate;
  return data;
}

extension WithdrawManualChannelEntityExtension on WithdrawManualChannelEntity {
  WithdrawManualChannelEntity copyWith({
    String? icon,
    String? channelName,
    int? channelId,
    double? rate,
  }) {
    return WithdrawManualChannelEntity()
      ..icon = icon ?? this.icon
      ..channelName = channelName ?? this.channelName
      ..channelId = channelId ?? this.channelId
      ..rate = rate ?? this.rate;
  }
}