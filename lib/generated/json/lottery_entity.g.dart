import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/lottery_entity.dart';

LotteryListEntity $LotteryListEntityFromJson(Map<String, dynamic> json) {
  final LotteryListEntity lotteryListEntity = LotteryListEntity();
  final List<LotteryGroup>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<LotteryGroup>(e) as LotteryGroup).toList();
  if (list != null) {
    lotteryListEntity.list = list;
  }
  return lotteryListEntity;
}

Map<String, dynamic> $LotteryListEntityToJson(LotteryListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension LotteryListEntityExtension on LotteryListEntity {
  LotteryListEntity copyWith({
    List<LotteryGroup>? list,
  }) {
    return LotteryListEntity()
      ..list = list ?? this.list;
  }
}

LotteryGroup $LotteryGroupFromJson(Map<String, dynamic> json) {
  final LotteryGroup lotteryGroup = LotteryGroup();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    lotteryGroup.id = id;
  }
  final String? groupName = jsonConvert.convert<String>(json['groupName']);
  if (groupName != null) {
    lotteryGroup.groupName = groupName;
  }
  final int? groupType = jsonConvert.convert<int>(json['groupType']);
  if (groupType != null) {
    lotteryGroup.groupType = groupType;
  }
  final int? groupStatus = jsonConvert.convert<int>(json['groupStatus']);
  if (groupStatus != null) {
    lotteryGroup.groupStatus = groupStatus;
  }
  final int? groupSort = jsonConvert.convert<int>(json['groupSort']);
  if (groupSort != null) {
    lotteryGroup.groupSort = groupSort;
  }
  final List<Lottery>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<Lottery>(e) as Lottery).toList();
  if (list != null) {
    lotteryGroup.list = list;
  }
  final String? iconUrl = jsonConvert.convert<String>(json['iconUrl']);
  if (iconUrl != null) {
    lotteryGroup.iconUrl = iconUrl;
  }
  final bool? isLogin = jsonConvert.convert<bool>(json['isLogin']);
  if (isLogin != null) {
    lotteryGroup.isLogin = isLogin;
  }
  return lotteryGroup;
}

Map<String, dynamic> $LotteryGroupToJson(LotteryGroup entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['groupName'] = entity.groupName;
  data['groupType'] = entity.groupType;
  data['groupStatus'] = entity.groupStatus;
  data['groupSort'] = entity.groupSort;
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  data['iconUrl'] = entity.iconUrl;
  data['isLogin'] = entity.isLogin;
  return data;
}

extension LotteryGroupExtension on LotteryGroup {
  LotteryGroup copyWith({
    int? id,
    String? groupName,
    int? groupType,
    int? groupStatus,
    int? groupSort,
    List<Lottery>? list,
    String? iconUrl,
    bool? isLogin,
  }) {
    return LotteryGroup()
      ..id = id ?? this.id
      ..groupName = groupName ?? this.groupName
      ..groupType = groupType ?? this.groupType
      ..groupStatus = groupStatus ?? this.groupStatus
      ..groupSort = groupSort ?? this.groupSort
      ..list = list ?? this.list
      ..iconUrl = iconUrl ?? this.iconUrl
      ..isLogin = isLogin ?? this.isLogin;
  }
}

Lottery $LotteryFromJson(Map<String, dynamic> json) {
  final Lottery lottery = Lottery();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    lottery.id = id;
  }
  final String? lotteryCode = jsonConvert.convert<String>(json['lotteryCode']);
  if (lotteryCode != null) {
    lottery.lotteryCode = lotteryCode;
  }
  final String? lotteryName = jsonConvert.convert<String>(json['lotteryName']);
  if (lotteryName != null) {
    lottery.lotteryName = lotteryName;
  }
  final String? imgUrl = jsonConvert.convert<String>(json['imgUrl']);
  if (imgUrl != null) {
    lottery.imgUrl = imgUrl;
  }
  final int? sortId = jsonConvert.convert<int>(json['sortId']);
  if (sortId != null) {
    lottery.sortId = sortId;
  }
  final dynamic lotteryStatus = json['lotteryStatus'];
  if (lotteryStatus != null) {
    lottery.lotteryStatus = lotteryStatus;
  }
  final dynamic officialStatus = json['officialStatus'];
  if (officialStatus != null) {
    lottery.officialStatus = officialStatus;
  }
  final int? gameCategoryId = jsonConvert.convert<int>(json['gameCategoryId']);
  if (gameCategoryId != null) {
    lottery.gameCategoryId = gameCategoryId;
  }
  final String? gameClassCode = jsonConvert.convert<String>(
      json['gameClassCode']);
  if (gameClassCode != null) {
    lottery.gameClassCode = gameClassCode;
  }
  final String? gameCategoryCode = jsonConvert.convert<String>(
      json['gameCategoryCode']);
  if (gameCategoryCode != null) {
    lottery.gameCategoryCode = gameCategoryCode;
  }
  final dynamic isDelete = json['isDelete'];
  if (isDelete != null) {
    lottery.isDelete = isDelete;
  }
  final bool? isFav = jsonConvert.convert<bool>(json['isFav']);
  if (isFav != null) {
    lottery.isFav = isFav;
  }
  return lottery;
}

Map<String, dynamic> $LotteryToJson(Lottery entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['lotteryCode'] = entity.lotteryCode;
  data['lotteryName'] = entity.lotteryName;
  data['imgUrl'] = entity.imgUrl;
  data['sortId'] = entity.sortId;
  data['lotteryStatus'] = entity.lotteryStatus;
  data['officialStatus'] = entity.officialStatus;
  data['gameCategoryId'] = entity.gameCategoryId;
  data['gameClassCode'] = entity.gameClassCode;
  data['gameCategoryCode'] = entity.gameCategoryCode;
  data['isDelete'] = entity.isDelete;
  data['isFav'] = entity.isFav;
  return data;
}

extension LotteryExtension on Lottery {
  Lottery copyWith({
    int? id,
    String? lotteryCode,
    String? lotteryName,
    String? imgUrl,
    int? sortId,
    dynamic lotteryStatus,
    dynamic officialStatus,
    int? gameCategoryId,
    String? gameClassCode,
    String? gameCategoryCode,
    dynamic isDelete,
    bool? isFav,
  }) {
    return Lottery()
      ..id = id ?? this.id
      ..lotteryCode = lotteryCode ?? this.lotteryCode
      ..lotteryName = lotteryName ?? this.lotteryName
      ..imgUrl = imgUrl ?? this.imgUrl
      ..sortId = sortId ?? this.sortId
      ..lotteryStatus = lotteryStatus ?? this.lotteryStatus
      ..officialStatus = officialStatus ?? this.officialStatus
      ..gameCategoryId = gameCategoryId ?? this.gameCategoryId
      ..gameClassCode = gameClassCode ?? this.gameClassCode
      ..gameCategoryCode = gameCategoryCode ?? this.gameCategoryCode
      ..isDelete = isDelete ?? this.isDelete
      ..isFav = isFav ?? this.isFav;
  }
}