import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/commit_top_up_entity.dart';

CommitTopUpEntity $CommitTopUpEntityFromJson(Map<String, dynamic> json) {
  final CommitTopUpEntity commitTopUpEntity = CommitTopUpEntity();
  final String? orderNo = jsonConvert.convert<String>(json['orderNo']);
  if (orderNo != null) {
    commitTopUpEntity.orderNo = orderNo;
  }
  final dynamic payWayName = json['payWayName'];
  if (payWayName != null) {
    commitTopUpEntity.payWayName = payWayName;
  }
  final dynamic tradeTime = json['tradeTime'];
  if (tradeTime != null) {
    commitTopUpEntity.tradeTime = tradeTime;
  }
  final double? amount = jsonConvert.convert<double>(json['amount']);
  if (amount != null) {
    commitTopUpEntity.amount = amount;
  }
  final String? currency = jsonConvert.convert<String>(json['currency']);
  if (currency != null) {
    commitTopUpEntity.currency = currency;
  }
  final String? payUrl = jsonConvert.convert<String>(json['payUrl']);
  if (payUrl != null) {
    commitTopUpEntity.payUrl = payUrl;
  }
  final TopUpOrderExt? ext = jsonConvert.convert<TopUpOrderExt>(json['ext']);
  if (ext != null) {
    commitTopUpEntity.ext = ext;
  }
  return commitTopUpEntity;
}

Map<String, dynamic> $CommitTopUpEntityToJson(CommitTopUpEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['orderNo'] = entity.orderNo;
  data['payWayName'] = entity.payWayName;
  data['tradeTime'] = entity.tradeTime;
  data['amount'] = entity.amount;
  data['currency'] = entity.currency;
  data['payUrl'] = entity.payUrl;
  data['ext'] = entity.ext?.toJson();
  return data;
}

extension CommitTopUpEntityExtension on CommitTopUpEntity {
  CommitTopUpEntity copyWith({
    String? orderNo,
    dynamic payWayName,
    dynamic tradeTime,
    double? amount,
    String? currency,
    String? payUrl,
    TopUpOrderExt? ext,
  }) {
    return CommitTopUpEntity()
      ..orderNo = orderNo ?? this.orderNo
      ..payWayName = payWayName ?? this.payWayName
      ..tradeTime = tradeTime ?? this.tradeTime
      ..amount = amount ?? this.amount
      ..currency = currency ?? this.currency
      ..payUrl = payUrl ?? this.payUrl
      ..ext = ext ?? this.ext;
  }
}

TopUpOrderExt $TopUpOrderExtFromJson(Map<String, dynamic> json) {
  final TopUpOrderExt topUpOrderExt = TopUpOrderExt();
  final String? payee = jsonConvert.convert<String>(json['payee']);
  if (payee != null) {
    topUpOrderExt.payee = payee;
  }
  final String? orderNo = jsonConvert.convert<String>(json['orderNo']);
  if (orderNo != null) {
    topUpOrderExt.orderNo = orderNo;
  }
  final int? orderAmount = jsonConvert.convert<int>(json['orderAmount']);
  if (orderAmount != null) {
    topUpOrderExt.orderAmount = orderAmount;
  }
  final int? orderLiveTime = jsonConvert.convert<int>(json['orderLiveTime']);
  if (orderLiveTime != null) {
    topUpOrderExt.orderLiveTime = orderLiveTime;
  }
  final String? payType = jsonConvert.convert<String>(json['payType']);
  if (payType != null) {
    topUpOrderExt.payType = payType;
  }
  final String? bankCard = jsonConvert.convert<String>(json['bankCard']);
  if (bankCard != null) {
    topUpOrderExt.bankCard = bankCard;
  }
  final String? receivingBank = jsonConvert.convert<String>(
      json['receivingBank']);
  if (receivingBank != null) {
    topUpOrderExt.receivingBank = receivingBank;
  }
  final String? orderStatus = jsonConvert.convert<String>(json['orderStatus']);
  if (orderStatus != null) {
    topUpOrderExt.orderStatus = orderStatus;
  }
  final double? depositNo = jsonConvert.convert<double>(json['depositNo']);
  if (depositNo != null) {
    topUpOrderExt.depositNo = depositNo;
  }
  final String? addressQrCode = jsonConvert.convert<String>(
      json['addressQrCode']);
  if (addressQrCode != null) {
    topUpOrderExt.addressQrCode = addressQrCode;
  }
  final String? exchangeRate = jsonConvert.convert<String>(
      json['exchangeRate']);
  if (exchangeRate != null) {
    topUpOrderExt.exchangeRate = exchangeRate;
  }
  final String? pact = jsonConvert.convert<String>(json['pact']);
  if (pact != null) {
    topUpOrderExt.pact = pact;
  }
  final String? depositAddress = jsonConvert.convert<String>(
      json['depositAddress']);
  if (depositAddress != null) {
    topUpOrderExt.depositAddress = depositAddress;
  }
  final String? currency = jsonConvert.convert<String>(json['currency']);
  if (currency != null) {
    topUpOrderExt.currency = currency;
  }
  final String? bannerImg = jsonConvert.convert<String>(json['bannerImg']);
  if (bannerImg != null) {
    topUpOrderExt.bannerImg = bannerImg;
  }
  final String? chainImg = jsonConvert.convert<String>(json['chainImg']);
  if (chainImg != null) {
    topUpOrderExt.chainImg = chainImg;
  }
  final String? usdtImg = jsonConvert.convert<String>(json['usdtImg']);
  if (usdtImg != null) {
    topUpOrderExt.usdtImg = usdtImg;
  }
  final String? cnyImg = jsonConvert.convert<String>(json['cnyImg']);
  if (cnyImg != null) {
    topUpOrderExt.cnyImg = cnyImg;
  }
  final String? exchangeRateImg = jsonConvert.convert<String>(
      json['exchangeRateImg']);
  if (exchangeRateImg != null) {
    topUpOrderExt.exchangeRateImg = exchangeRateImg;
  }
  final String? tipHead = jsonConvert.convert<String>(json['tipHead']);
  if (tipHead != null) {
    topUpOrderExt.tipHead = tipHead;
  }
  final String? tipContent = jsonConvert.convert<String>(json['tipContent']);
  if (tipContent != null) {
    topUpOrderExt.tipContent = tipContent;
  }
  return topUpOrderExt;
}

Map<String, dynamic> $TopUpOrderExtToJson(TopUpOrderExt entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['payee'] = entity.payee;
  data['orderNo'] = entity.orderNo;
  data['orderAmount'] = entity.orderAmount;
  data['orderLiveTime'] = entity.orderLiveTime;
  data['payType'] = entity.payType;
  data['bankCard'] = entity.bankCard;
  data['receivingBank'] = entity.receivingBank;
  data['orderStatus'] = entity.orderStatus;
  data['depositNo'] = entity.depositNo;
  data['addressQrCode'] = entity.addressQrCode;
  data['exchangeRate'] = entity.exchangeRate;
  data['pact'] = entity.pact;
  data['depositAddress'] = entity.depositAddress;
  data['currency'] = entity.currency;
  data['bannerImg'] = entity.bannerImg;
  data['chainImg'] = entity.chainImg;
  data['usdtImg'] = entity.usdtImg;
  data['cnyImg'] = entity.cnyImg;
  data['exchangeRateImg'] = entity.exchangeRateImg;
  data['tipHead'] = entity.tipHead;
  data['tipContent'] = entity.tipContent;
  return data;
}

extension TopUpOrderExtExtension on TopUpOrderExt {
  TopUpOrderExt copyWith({
    String? payee,
    String? orderNo,
    int? orderAmount,
    int? orderLiveTime,
    String? payType,
    String? bankCard,
    String? receivingBank,
    String? orderStatus,
    double? depositNo,
    String? addressQrCode,
    String? exchangeRate,
    String? pact,
    String? depositAddress,
    String? currency,
    String? bannerImg,
    String? chainImg,
    String? usdtImg,
    String? cnyImg,
    String? exchangeRateImg,
    String? tipHead,
    String? tipContent,
  }) {
    return TopUpOrderExt()
      ..payee = payee ?? this.payee
      ..orderNo = orderNo ?? this.orderNo
      ..orderAmount = orderAmount ?? this.orderAmount
      ..orderLiveTime = orderLiveTime ?? this.orderLiveTime
      ..payType = payType ?? this.payType
      ..bankCard = bankCard ?? this.bankCard
      ..receivingBank = receivingBank ?? this.receivingBank
      ..orderStatus = orderStatus ?? this.orderStatus
      ..depositNo = depositNo ?? this.depositNo
      ..addressQrCode = addressQrCode ?? this.addressQrCode
      ..exchangeRate = exchangeRate ?? this.exchangeRate
      ..pact = pact ?? this.pact
      ..depositAddress = depositAddress ?? this.depositAddress
      ..currency = currency ?? this.currency
      ..bannerImg = bannerImg ?? this.bannerImg
      ..chainImg = chainImg ?? this.chainImg
      ..usdtImg = usdtImg ?? this.usdtImg
      ..cnyImg = cnyImg ?? this.cnyImg
      ..exchangeRateImg = exchangeRateImg ?? this.exchangeRateImg
      ..tipHead = tipHead ?? this.tipHead
      ..tipContent = tipContent ?? this.tipContent;
  }
}