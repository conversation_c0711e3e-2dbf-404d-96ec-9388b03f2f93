import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/notification_alert_entity.dart';

NotificationAlertListEntity $NotificationAlertListEntityFromJson(
    Map<String, dynamic> json) {
  final NotificationAlertListEntity notificationAlertListEntity = NotificationAlertListEntity();
  final List<NotificationAlertEntity>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<NotificationAlertEntity>(
          e) as NotificationAlertEntity)
      .toList();
  if (list != null) {
    notificationAlertListEntity.list = list;
  }
  return notificationAlertListEntity;
}

Map<String, dynamic> $NotificationAlertListEntityToJson(
    NotificationAlertListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension NotificationAlertListEntityExtension on NotificationAlertListEntity {
  NotificationAlertListEntity copyWith({
    List<NotificationAlertEntity>? list,
  }) {
    return NotificationAlertListEntity()
      ..list = list ?? this.list;
  }
}

NotificationAlertEntity $NotificationAlertEntityFromJson(
    Map<String, dynamic> json) {
  final NotificationAlertEntity notificationAlertEntity = NotificationAlertEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    notificationAlertEntity.id = id;
  }
  final int? noticeType = jsonConvert.convert<int>(json['noticeType']);
  if (noticeType != null) {
    notificationAlertEntity.noticeType = noticeType;
  }
  final String? noticeTitle = jsonConvert.convert<String>(json['noticeTitle']);
  if (noticeTitle != null) {
    notificationAlertEntity.noticeTitle = noticeTitle;
  }
  final String? noticeContent = jsonConvert.convert<String>(
      json['noticeContent']);
  if (noticeContent != null) {
    notificationAlertEntity.noticeContent = noticeContent;
  }
  final String? beginTime = jsonConvert.convert<String>(json['beginTime']);
  if (beginTime != null) {
    notificationAlertEntity.beginTime = beginTime;
  }
  final String? endTime = jsonConvert.convert<String>(json['endTime']);
  if (endTime != null) {
    notificationAlertEntity.endTime = endTime;
  }
  final int? receiveType = jsonConvert.convert<int>(json['receiveType']);
  if (receiveType != null) {
    notificationAlertEntity.receiveType = receiveType;
  }
  final int? noticeSeq = jsonConvert.convert<int>(json['noticeSeq']);
  if (noticeSeq != null) {
    notificationAlertEntity.noticeSeq = noticeSeq;
  }
  final int? noticeStatus = jsonConvert.convert<int>(json['noticeStatus']);
  if (noticeStatus != null) {
    notificationAlertEntity.noticeStatus = noticeStatus;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    notificationAlertEntity.createTime = createTime;
  }
  final int? intoHistory = jsonConvert.convert<int>(json['intoHistory']);
  if (intoHistory != null) {
    notificationAlertEntity.intoHistory = intoHistory;
  }
  final int? jumpStatus = jsonConvert.convert<int>(json['jumpStatus']);
  if (jumpStatus != null) {
    notificationAlertEntity.jumpStatus = jumpStatus;
  }
  final String? jumpUrl = jsonConvert.convert<String>(json['jumpUrl']);
  if (jumpUrl != null) {
    notificationAlertEntity.jumpUrl = jumpUrl;
  }
  final int? gameBelong = jsonConvert.convert<int>(json['gameBelong']);
  if (gameBelong != null) {
    notificationAlertEntity.gameBelong = gameBelong;
  }
  final int? venueId = jsonConvert.convert<int>(json['venueId']);
  if (venueId != null) {
    notificationAlertEntity.venueId = venueId;
  }
  final String? platformCode = jsonConvert.convert<String>(
      json['platformCode']);
  if (platformCode != null) {
    notificationAlertEntity.platformCode = platformCode;
  }
  final int? gameId = jsonConvert.convert<int>(json['gameId']);
  if (gameId != null) {
    notificationAlertEntity.gameId = gameId;
  }
  return notificationAlertEntity;
}

Map<String, dynamic> $NotificationAlertEntityToJson(
    NotificationAlertEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['noticeType'] = entity.noticeType;
  data['noticeTitle'] = entity.noticeTitle;
  data['noticeContent'] = entity.noticeContent;
  data['beginTime'] = entity.beginTime;
  data['endTime'] = entity.endTime;
  data['receiveType'] = entity.receiveType;
  data['noticeSeq'] = entity.noticeSeq;
  data['noticeStatus'] = entity.noticeStatus;
  data['createTime'] = entity.createTime;
  data['intoHistory'] = entity.intoHistory;
  data['jumpStatus'] = entity.jumpStatus;
  data['jumpUrl'] = entity.jumpUrl;
  data['gameBelong'] = entity.gameBelong;
  data['venueId'] = entity.venueId;
  data['platformCode'] = entity.platformCode;
  data['gameId'] = entity.gameId;
  return data;
}

extension NotificationAlertEntityExtension on NotificationAlertEntity {
  NotificationAlertEntity copyWith({
    int? id,
    int? noticeType,
    String? noticeTitle,
    String? noticeContent,
    String? beginTime,
    String? endTime,
    int? receiveType,
    int? noticeSeq,
    int? noticeStatus,
    String? createTime,
    int? intoHistory,
    int? jumpStatus,
    String? jumpUrl,
    int? gameBelong,
    int? venueId,
    String? platformCode,
    int? gameId,
  }) {
    return NotificationAlertEntity()
      ..id = id ?? this.id
      ..noticeType = noticeType ?? this.noticeType
      ..noticeTitle = noticeTitle ?? this.noticeTitle
      ..noticeContent = noticeContent ?? this.noticeContent
      ..beginTime = beginTime ?? this.beginTime
      ..endTime = endTime ?? this.endTime
      ..receiveType = receiveType ?? this.receiveType
      ..noticeSeq = noticeSeq ?? this.noticeSeq
      ..noticeStatus = noticeStatus ?? this.noticeStatus
      ..createTime = createTime ?? this.createTime
      ..intoHistory = intoHistory ?? this.intoHistory
      ..jumpStatus = jumpStatus ?? this.jumpStatus
      ..jumpUrl = jumpUrl ?? this.jumpUrl
      ..gameBelong = gameBelong ?? this.gameBelong
      ..venueId = venueId ?? this.venueId
      ..platformCode = platformCode ?? this.platformCode
      ..gameId = gameId ?? this.gameId;
  }
}