import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/lottery_cart_item_entity.dart';

LotteryCartItemEntity $LotteryCartItemEntityFromJson(
    Map<String, dynamic> json) {
  final LotteryCartItemEntity lotteryCartItemEntity = LotteryCartItemEntity();
  final double? betAmount = jsonConvert.convert<double>(json['betAmount']);
  if (betAmount != null) {
    lotteryCartItemEntity.betAmount = betAmount;
  }
  final String? itemType = jsonConvert.convert<String>(json['itemType']);
  if (itemType != null) {
    lotteryCartItemEntity.itemType = itemType;
  }
  final String? itemObject = jsonConvert.convert<String>(json['itemObject']);
  if (itemObject != null) {
    lotteryCartItemEntity.itemObject = itemObject;
  }
  final int? rule = jsonConvert.convert<int>(json['rule']);
  if (rule != null) {
    lotteryCartItemEntity.rule = rule;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    lotteryCartItemEntity.id = id;
  }
  final bool? isLhcGuoGuan = jsonConvert.convert<bool>(json['isLhcGuoGuan']);
  if (isLhcGuoGuan != null) {
    lotteryCartItemEntity.isLhcGuoGuan = isLhcGuoGuan;
  }
  final String? option = jsonConvert.convert<String>(json['option']);
  if (option != null) {
    lotteryCartItemEntity.option = option;
  }
  final String? optionName = jsonConvert.convert<String>(json['optionName']);
  if (optionName != null) {
    lotteryCartItemEntity.optionName = optionName;
  }
  return lotteryCartItemEntity;
}

Map<String, dynamic> $LotteryCartItemEntityToJson(
    LotteryCartItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['betAmount'] = entity.betAmount;
  data['itemType'] = entity.itemType;
  data['itemObject'] = entity.itemObject;
  data['rule'] = entity.rule;
  data['id'] = entity.id;
  data['isLhcGuoGuan'] = entity.isLhcGuoGuan;
  data['option'] = entity.option;
  data['optionName'] = entity.optionName;
  return data;
}

extension LotteryCartItemEntityExtension on LotteryCartItemEntity {
  LotteryCartItemEntity copyWith({
    double? betAmount,
    String? itemType,
    String? itemObject,
    int? rule,
    int? id,
    bool? isLhcGuoGuan,
    String? option,
    String? optionName,
  }) {
    return LotteryCartItemEntity()
      ..betAmount = betAmount ?? this.betAmount
      ..itemType = itemType ?? this.itemType
      ..itemObject = itemObject ?? this.itemObject
      ..rule = rule ?? this.rule
      ..id = id ?? this.id
      ..isLhcGuoGuan = isLhcGuoGuan ?? this.isLhcGuoGuan
      ..option = option ?? this.option
      ..optionName = optionName ?? this.optionName;
  }
}