import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/chat_service_online_entity.dart';

ChatServiceOnlineList $ChatServiceOnlineListFromJson(
    Map<String, dynamic> json) {
  final ChatServiceOnlineList chatServiceOnlineList = ChatServiceOnlineList();
  final List<ChatServiceOnlineEntity>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ChatServiceOnlineEntity>(
          e) as ChatServiceOnlineEntity)
      .toList();
  if (list != null) {
    chatServiceOnlineList.list = list;
  }
  return chatServiceOnlineList;
}

Map<String, dynamic> $ChatServiceOnlineListToJson(
    ChatServiceOnlineList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension ChatServiceOnlineListExtension on ChatServiceOnlineList {
  ChatServiceOnlineList copyWith({
    List<ChatServiceOnlineEntity>? list,
  }) {
    return ChatServiceOnlineList()
      ..list = list ?? this.list;
  }
}

ChatServiceOnlineEntity $ChatServiceOnlineEntityFromJson(
    Map<String, dynamic> json) {
  final ChatServiceOnlineEntity chatServiceOnlineEntity = ChatServiceOnlineEntity();
  final String? status = jsonConvert.convert<String>(json['status']);
  if (status != null) {
    chatServiceOnlineEntity.status = status;
  }
  final String? toAccount = jsonConvert.convert<String>(json['to_Account']);
  if (toAccount != null) {
    chatServiceOnlineEntity.toAccount = toAccount;
  }
  return chatServiceOnlineEntity;
}

Map<String, dynamic> $ChatServiceOnlineEntityToJson(
    ChatServiceOnlineEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['status'] = entity.status;
  data['to_Account'] = entity.toAccount;
  return data;
}

extension ChatServiceOnlineEntityExtension on ChatServiceOnlineEntity {
  ChatServiceOnlineEntity copyWith({
    String? status,
    String? toAccount,
  }) {
    return ChatServiceOnlineEntity()
      ..status = status ?? this.status
      ..toAccount = toAccount ?? this.toAccount;
  }
}