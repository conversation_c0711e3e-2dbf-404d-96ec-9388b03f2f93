import 'package:game_store/generated/json/base/json_convert_content.dart';
import 'package:game_store/core/models/entities/lottery_odds_entity.dart';
import 'package:equatable/equatable.dart';


LotteryOddsList $LotteryOddsListFromJson(Map<String, dynamic> json) {
  final LotteryOddsList lotteryOddsList = LotteryOddsList();
  final List<LotteryOdds>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<LotteryOdds>(e) as LotteryOdds).toList();
  if (list != null) {
    lotteryOddsList.list = list;
  }
  return lotteryOddsList;
}

Map<String, dynamic> $LotteryOddsListToJson(LotteryOddsList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension LotteryOddsListExtension on LotteryOddsList {
  LotteryOddsList copyWith({
    List<LotteryOdds>? list,
  }) {
    return LotteryOddsList()
      ..list = list ?? this.list;
  }
}

LotteryOdds $LotteryOddsFromJson(Map<String, dynamic> json) {
  final LotteryOdds lotteryOdds = LotteryOdds();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    lotteryOdds.id = id;
  }
  final double? odds = jsonConvert.convert<double>(json['odds']);
  if (odds != null) {
    lotteryOdds.odds = odds;
  }
  final String? itemType = jsonConvert.convert<String>(json['itemType']);
  if (itemType != null) {
    lotteryOdds.itemType = itemType;
  }
  final String? itemObject = jsonConvert.convert<String>(json['itemObject']);
  if (itemObject != null) {
    lotteryOdds.itemObject = itemObject;
  }
  final int? limitValue = jsonConvert.convert<int>(json['limitValue']);
  if (limitValue != null) {
    lotteryOdds.limitValue = limitValue;
  }
  final int? returnRate = jsonConvert.convert<int>(json['returnRate']);
  if (returnRate != null) {
    lotteryOdds.returnRate = returnRate;
  }
  final bool? isSelected = jsonConvert.convert<bool>(json['isSelected']);
  if (isSelected != null) {
    lotteryOdds.isSelected = isSelected;
  }
  return lotteryOdds;
}

Map<String, dynamic> $LotteryOddsToJson(LotteryOdds entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['odds'] = entity.odds;
  data['itemType'] = entity.itemType;
  data['itemObject'] = entity.itemObject;
  data['limitValue'] = entity.limitValue;
  data['returnRate'] = entity.returnRate;
  data['isSelected'] = entity.isSelected;
  return data;
}

extension LotteryOddsExtension on LotteryOdds {
  LotteryOdds copyWith({
    int? id,
    double? odds,
    String? itemType,
    String? itemObject,
    int? limitValue,
    int? returnRate,
    bool? isSelected,
  }) {
    return LotteryOdds()
      ..id = id ?? this.id
      ..odds = odds ?? this.odds
      ..itemType = itemType ?? this.itemType
      ..itemObject = itemObject ?? this.itemObject
      ..limitValue = limitValue ?? this.limitValue
      ..returnRate = returnRate ?? this.returnRate
      ..isSelected = isSelected ?? this.isSelected;
  }
}

LotteryOddsGroup $LotteryOddsGroupFromJson(Map<String, dynamic> json) {
  final LotteryOddsGroup lotteryOddsGroup = LotteryOddsGroup();
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    lotteryOddsGroup.type = type;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    lotteryOddsGroup.name = name;
  }
  final bool? isSelected = jsonConvert.convert<bool>(json['isSelected']);
  if (isSelected != null) {
    lotteryOddsGroup.isSelected = isSelected;
  }
  final int? orderCount = jsonConvert.convert<int>(json['orderCount']);
  if (orderCount != null) {
    lotteryOddsGroup.orderCount = orderCount;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    lotteryOddsGroup.id = id;
  }
  final int? selIndex = jsonConvert.convert<int>(json['selIndex']);
  if (selIndex != null) {
    lotteryOddsGroup.selIndex = selIndex;
  }
  final Map<String, LotteryOddsSection>? sectionMap =
  (json['sectionMap'] as Map<String, dynamic>).map(
          (k, e) =>
          MapEntry(k, jsonConvert.convert<LotteryOddsSection>(
              e) as LotteryOddsSection));
  if (sectionMap != null) {
    lotteryOddsGroup.sectionMap = sectionMap;
  }
  return lotteryOddsGroup;
}

Map<String, dynamic> $LotteryOddsGroupToJson(LotteryOddsGroup entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['type'] = entity.type;
  data['name'] = entity.name;
  data['isSelected'] = entity.isSelected;
  data['orderCount'] = entity.orderCount;
  data['id'] = entity.id;
  data['selIndex'] = entity.selIndex;
  data['sectionMap'] = entity.sectionMap;
  return data;
}

extension LotteryOddsGroupExtension on LotteryOddsGroup {
  LotteryOddsGroup copyWith({
    String? type,
    String? name,
    bool? isSelected,
    int? orderCount,
    int? id,
    int? selIndex,
    Map<String, LotteryOddsSection>? sectionMap,
  }) {
    return LotteryOddsGroup()
      ..type = type ?? this.type
      ..name = name ?? this.name
      ..isSelected = isSelected ?? this.isSelected
      ..orderCount = orderCount ?? this.orderCount
      ..id = id ?? this.id
      ..selIndex = selIndex ?? this.selIndex
      ..sectionMap = sectionMap ?? this.sectionMap;
  }
}