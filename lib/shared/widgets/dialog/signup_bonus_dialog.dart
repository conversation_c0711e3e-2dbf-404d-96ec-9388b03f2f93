import 'package:flutter/material.dart';
import 'package:game_store/core/utils/font_size.dart';
import 'package:game_store/core/utils/screenUtil.dart';
import 'package:game_store/features/routers/navigator_utils.dart';
import 'package:game_store/injection_container.dart';

import '../../../features/routers/app_router.dart';
import '../common_button.dart';

/// 注册送礼金弹窗
class SignupBonusDialog {
  show(context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return _RegistrationBonusActivityDialogContent();
      },
    );
  }
}

class _RegistrationBonusActivityDialogContent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: 347.gw,
          height: 560.gw,
          decoration: BoxDecoration(
            image: const DecorationImage(image: AssetImage('assets/images/dialog/bg_bonus.png'), fit: BoxFit.fill),
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.gw),
          ),
          child: Column(
            children: [
              _buildHeader(),
              // Main image
              Image.asset(
                'assets/images/dialog/bg_bonus_activity.png',
                height: 279.gw,
                width: 347.gw,
                fit: BoxFit.fill,
              ),

              // Bonus amount text
              Image.asset(
                'assets/images/dialog/title_bonus_activity.png',
                height: 67.gw,
              ),

              // Description text
              Padding(
                padding: EdgeInsets.only(top: 15.gw, bottom: 9.gw),
                child: Text(
                  '即日起，注册账号后，绑定手机号即可\n轻松领取彩金。',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14.fs,
                    color: const Color(0xFF666666),
                    height: 1.5,
                  ),
                ),
              ),

              // Register button
              CommonButton(
                title: '立即注册',
                bgImgPath: 'assets/images/button/bg_button_golden_gradient_43h.png',
                titlePadding: EdgeInsets.only(top: 1.gw, bottom: 6.gw),
                backgroundColor: Colors.transparent,
                height: 43.gw,
                width: 287.gw,
                fontSize: 18.fs,
                fontWeight: FontWeight.w400,
                onPressed: () {
                  Navigator.of(context).pop();
                  sl<NavigatorService>().push(AppRouter.register);
                },
              ),
              SizedBox(
                height: 13.gw,
              ),
              // Login text with link
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '已有账号，',
                    style: TextStyle(
                      fontSize: 14.fs,
                      color: const Color(0xff333333),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                      sl<NavigatorService>().push(AppRouter.login);
                    },
                    child: Text(
                      '去登录',
                      style: TextStyle(
                        fontSize: 14.fs,
                        decoration: TextDecoration.underline,
                        color: const Color(0xffC6A695),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  _buildHeader() {
    return Container(
      height: 49.gw,
      width: double.infinity,
      decoration: BoxDecoration(
        color: const Color(0xffF3EAE1),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.gw),
          topRight: Radius.circular(12.gw),
        ),
        border: Border(
          bottom: BorderSide(
            width: 1.gw,
            color: const Color(0xffEFD1B5),
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xff693D09).withOpacity(0.25),
            offset: const Offset(0, 4.54),
            blurRadius: 4.54,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Text(
            '注册送礼金',
            style: TextStyle(
              fontSize: 22.fs,
              fontWeight: FontWeight.w600,
              color: const Color(0xffCCAE9E),
              shadows: const [
                BoxShadow(
                  color: Color(0xFFFFFFFF),
                  offset: Offset(0, 0.63),
                  blurRadius: 0.63,
                  spreadRadius: 0,
                ),
              ],
            ),
          ),
          Positioned(
            right: 0,
            child: InkWell(
              onTap: () => sl<NavigatorService>().pop(),
              child: SizedBox(
                width: 40.gw,
                height: 40.gw,
                child: Center(
                  child: Image.asset(
                    'assets/images/button/btn_close.png',
                    height: 15.gw,
                    width: 15.gw,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
