import 'package:flutter/material.dart';
import 'package:flutter_inner_shadow/flutter_inner_shadow.dart';
import 'package:game_store/core/utils/font_size.dart';
import 'package:game_store/core/utils/screenUtil.dart';
import 'package:game_store/shared/widgets/container/golden_border_container.dart';

class GoldenSectionCard extends StatelessWidget {
  final String title;
  final Widget child;

  const GoldenSectionCard({
    super.key,
    required this.title,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return GoldenBorderContainer(
      offsetY: 2,
      child: Column(
        children: [
          _buildFunctionHeader(),
          _buildSeparator(),
          child,
        ],
      ),
    );
  }

  Widget _buildFunctionHeader() {
    return Stack(
      children: [
        // 外部阴影
        Container(
          // width: ,
          height: 38.gw,
          decoration: BoxDecoration(
            color: const Color(0xFFF4F4F4), // 可自定义背景
            boxShadow: const [
              BoxShadow(
                offset: Offset(0, 1),
                blurRadius: 0,
                color: Color(0x80FFFFFF), // #FFFFFF80（白色半透明）
              ),
            ],
            borderRadius: BorderRadius.circular(12),
          ),
        ),

        // 内部阴影
        InnerShadow(
          shadows: const [
            Shadow(
              offset: Offset(0, 1),
              blurRadius: 2,
              color: Color(0xFFFFFFFF), // 完全白色
            ),
          ],
          child: Container(
            height: 38.gw,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.bottomCenter, // 0deg 对应从下到上
                end: Alignment.topCenter,
                colors: [
                  Color(0xFFECDED0), // #ECDED0 不透明
                  Color.fromRGBO(230, 223, 215, 0.7), // rgba(230,223,215,0.7)
                ],
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 4.4.gw,
                  height: 15.gw,
                  decoration: const BoxDecoration(
                    color: Color(0xffCDB296),
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(8),
                      bottomRight: Radius.circular(8),
                    ),
                  ),
                ),
                SizedBox(width: 6.gw),
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14.fs,
                    color: const Color(0xff463A2D),
                  ),
                )
              ],
            ),
          ),
        ),
      ],
    );
  }

  _buildSeparator() {
    return Container(height: 0.5, width: double.infinity, color: const Color(0xffEBE5E0));
  }
}
