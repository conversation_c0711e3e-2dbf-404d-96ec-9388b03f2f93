import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:game_store/core/models/entities/vip_model_entity.dart';
import 'package:game_store/core/singletons/user_cubit.dart';
import 'package:game_store/core/singletons/user_state.dart';
import 'package:game_store/core/utils/auth_util.dart';
import 'package:game_store/injection_container.dart';

class VipSemiCircleSlider extends StatefulWidget {
  final int currentVip;
  final List<VipModel> dataList;

  const VipSemiCircleSlider({
    super.key,
    required this.currentVip,
    required this.dataList,
  });

  @override
  State<VipSemiCircleSlider> createState() => _VipSemiCircleSliderState();
}

class _VipSemiCircleSliderState extends State<VipSemiCircleSlider> {
  late int currentVip;
  final width = 300.0;
  final height = 160.0;

  @override
  void initState() {
    super.initState();
    currentVip = (widget.currentVip + 1).clamp(1, widget.dataList.length);
  }

  void _onDrag(double delta) {
    return;

    /// 暂不开放滑动
    setState(() {
      currentVip -= delta.sign.toInt();
      currentVip = currentVip.clamp(1, widget.dataList.last.vipLevel);
    });
  }

  @override
  void didUpdateWidget(covariant VipSemiCircleSlider oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.currentVip != oldWidget.currentVip) {
      setState(() {
        currentVip = (widget.currentVip + 1).clamp(1, widget.dataList.length);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final vipInfo = sl<UserCubit>().state.vipInfo;

    return SizedBox(
      width: width,
      height: height,
      child: Stack(
        alignment: Alignment.center,
        children: [
          GestureDetector(
            onHorizontalDragUpdate: (details) => _onDrag(details.delta.dx),
            child: CustomPaint(
              size: Size(width, height),
              painter: _VipPainter(currentVip: currentVip, dataList: widget.dataList),
            ),
          ),
          // 头像
          BlocSelector<UserCubit, UserState, String>(
            selector: (state) => state.userInfo?.faceId.toString() ?? '',
            builder: (context, avatarUrl) {
              return Positioned(bottom: 35, child: AuthUtil.getAvatarWidget(avatarUrl, size: 48));
            },
          ),
          // vip等级
          Positioned(
            bottom: 5,
            child: Text(
              "VIP${vipInfo?.vipLevel}",
              style: const TextStyle(
                color: Color(0xff121217),
                fontSize: 19,
                fontWeight: FontWeight.w700,
              ),
            ),
          )
        ],
      ),
    );
  }
}

class _VipPainter extends CustomPainter {
  final int currentVip;
  final List<VipModel> dataList;

  _VipPainter({required this.currentVip, required this.dataList});

  @override
  void paint(Canvas canvas, Size size) {
    final rawCenter = Offset(size.width / 2, size.height);
    final center = Offset(rawCenter.dx, rawCenter.dy); // 保留你修改
    final radius = size.width / 2 - 15;

    final sliderRect = Rect.fromCircle(center: center, radius: radius - 35);
    final rect = Rect.fromCircle(center: center, radius: radius);

    const sweepAngle = 4 * pi / 5; // 144°
    const startAngle = pi + (pi - sweepAngle) / 2;

    // === 1. 绘制背景环 ===
    final basePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 5
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(sliderRect, startAngle, sweepAngle, false, basePaint);

    // === 2. 当前 VIP 在可视范围内位置 ===
    const visibleCount = 10;

    // 最大起始等级，确保最多显示 visibleCount 个
    final maxStart = dataList.length > visibleCount ? dataList.length - visibleCount + 1 : 1;

    // 以 currentVip 居中显示，往前偏移 4 位（中间位置）
    final startVip = (currentVip - 4).clamp(1, maxStart);
    final progressAngle = _calculateProgressAngle(currentVip, startVip, visibleCount, sweepAngle);

    final progressPaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4
      ..strokeCap = StrokeCap.round;
    if (kIsWeb) {
      progressPaint.color = const Color(0xffFFD6CB);
    } else {
      progressPaint.shader = const SweepGradient(
        startAngle: startAngle,
        endAngle: startAngle + sweepAngle,
        colors: [Color(0xff74E2F0), Color(0xffF0A6FF), Color(0xffFFD6CB)],
      ).createShader(rect);
    }

    canvas.drawArc(sliderRect, startAngle, progressAngle, false, progressPaint);

    // === 3. 绘制刻度文字 + 点 ===
    final tickPaint = Paint()..color = const Color(0xFFD8BFAF); // 棕色点
    const fontColor = Colors.black;

    for (int i = 0; i < visibleCount; i++) {
      final vip = startVip + i;
      final vipLevelText = 'V${dataList[vip - 1].vipLevel}';
      final t = i / (visibleCount - 1);
      final angle = startAngle + sweepAngle * t;

      final dx = center.dx + radius * cos(angle);
      final dy = center.dy + radius * sin(angle);

      final isCurrent = vip == currentVip;

      if (isCurrent) {
        const radius = 18.0;
        final center = Offset(dx, dy);
        // 0. 阴影（绘制阴影必须先绘制一个偏移的透明圆）
        final shadowPaint = Paint()
          ..color = const Color(0x1A000000) // #0000001A
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2.48);

        canvas.drawCircle(
          Offset(center.dx, center.dy + 2.48), // 阴影在下方
          radius,
          shadowPaint,
        );

        // 1. 渐变背景圆
        final rect = Rect.fromCircle(center: center, radius: radius);
        const bgGradient = LinearGradient(
          begin: Alignment(0.44, -1),
          end: Alignment(-0.44, 1),
          colors: [
            Color(0xFFD8C6B4),
            Color(0xFFAA8C6E),
          ],
        );
        final bgPaint = Paint()..shader = bgGradient.createShader(rect);
        canvas.drawCircle(center, radius, bgPaint);

        // 2. 白色文字
        final tp = TextPainter(
          text: TextSpan(
            text: vipLevelText,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
          textDirection: TextDirection.ltr,
        )..layout();

        tp.paint(canvas, Offset(center.dx - tp.width / 2, center.dy - tp.height / 2));
      } else {
        final tp = TextPainter(
          text: TextSpan(
            text: vipLevelText,
            style: const TextStyle(
              fontSize: 12,
              color: fontColor,
            ),
          ),
          textDirection: TextDirection.ltr,
        )..layout();

        tp.paint(canvas, Offset(dx - tp.width / 2, dy - tp.height / 2));
      }
      // 小点贴弧线上
      const dotRadius = 1.5;
      final dotDx = center.dx + (radius - 25) * cos(angle);
      final dotDy = center.dy + (radius - 25) * sin(angle);
      canvas.drawCircle(Offset(dotDx, dotDy), dotRadius, tickPaint);
    }
  }

  double _calculateProgressAngle(int currentVip, int startVip, int visibleCount, double sweepAngle) {
    final index = currentVip - startVip;
    final angle = sweepAngle * (index / (visibleCount - 1));
    return max(angle, 0.01); // 避免 0 角度时弧线不可见
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
