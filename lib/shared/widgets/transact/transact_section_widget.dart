

import 'package:flutter/material.dart';
import 'package:game_store/core/utils/screenUtil.dart';

class TransactSectionWidget extends StatelessWidget {
  final Widget child;
  const TransactSectionWidget({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
      return Container(
          padding: EdgeInsets.symmetric(horizontal: 10.gw),
          color: Colors.white,
          // decoration: const BoxDecoration(
          //   gradient: LinearGradient(
          //     begin: Alignment.topLeft,
          //     end: Alignment.bottomRight,
          //     stops: [0.0191, 0.5, 0.9438],
          //     colors: [
          //       Color(0xFFFFFFFF), // #FFFFFF
          //       Color(0xFFFFFCFA), // #FFFCFA
          //       Color(0xFFF9FCFF), // #F9FCFF
          //     ],
          //   ),
          //   boxShadow: [
          //     BoxShadow(
          //       color: Color(0x80B6B6B6), // #B6B6B680
          //       offset: Offset(0, 2), // (x, y)
          //       blurRadius: 4, // 模糊半径
          //     ),
          //   ],
          // ),
          child: child);


  }
}