import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:game_store/core/constants/constants.dart';
import 'package:game_store/core/models/apis/transact.dart';
import 'package:game_store/core/models/entities/withdraw_user_bank_list_entity.dart';
import 'package:game_store/core/utils/string_util.dart';
import 'package:game_store/core/utils/font_size.dart';
import 'package:game_store/shared/widgets/app_image.dart';
import 'package:game_store/core/utils/screenUtil.dart';
import 'package:game_store/shared/widgets/color_filtered/gray_filtered_widget.dart';
import 'package:game_store/shared/widgets/user/bank_ban_widget.dart';
import 'package:game_store/shared/widgets/user/pay_card_cell.dart';


class WithdrawSelectBankCellViewModel {
  String bankName;
  String bankCode;
  String cardNum;
  bool isBan; // 是否禁用

  WithdrawSelectBankCellViewModel({
    required this.bankName,
    required this.bankCode,
    required this.cardNum,
    required this.isBan,
  });

  factory WithdrawSelectBankCellViewModel.formWithdrawUserBankBrief(WithdrawUserBankBrief model) {
    return WithdrawSelectBankCellViewModel(
        bankName: model.bankName, bankCode: model.bankCode, cardNum: model.cardNo, isBan: model.useStatus == 1);
  }

  factory WithdrawSelectBankCellViewModel.formWithdrawUserBankInfoEntity(WithdrawUserBankInfoEntity model) {
    return WithdrawSelectBankCellViewModel(
        bankName: model.bankName, bankCode: model.bankCode, cardNum: model.cardNo, isBan: model.useStatus == 1);
  }
}

class WithdrawSelectBankCell extends StatelessWidget {
  final WithdrawType type;
  final WithdrawSelectBankCellViewModel model;
  final bool isSel;
  final bool showNext;

  const WithdrawSelectBankCell({
    super.key,
    required this.type,
    required this.model,
    this.isSel = false,
    this.showNext = false,
  });

  @override
  Widget build(BuildContext context) {
    var brandName = model.bankName;
    var bgColor = PayCardCell.getBankColor(brandName).color;
    var isSmall = PayCardCell.getBankColor(brandName).isSmall;
    var cardNum = model.cardNum;
    if (type == WithdrawType.bankCard) {
      cardNum = model.cardNum.formatCardNumber();
    }
    return type == WithdrawType.bankCard
        ? _bankCard(bgColor, context, cardNum, isSmall)
        : _manualChannel(bgColor, context, cardNum);
  }

  Container _bankCard(Color bgColor, BuildContext context, String cardNum, bool isSmall) {
    return Container(
      height: 78.gw,
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            blurRadius: 2,
            offset: const Offset(0, 2),
            color: Colors.white.withOpacity(0.2),
          )
        ],
        borderRadius: BorderRadius.circular(10.gw), // 使用和之前相同的圆角
        color: bgColor, // 背景色
        image: isSmall 
            ? const DecorationImage(
                image: AssetImage("assets/images/transact/bg_button.png"),
                fit: BoxFit.cover,
              )
            : null,
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              SizedBox(width: 10.gw),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 10.gw),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            AppImage(
                              imageUrl: getBankImageStr(model.bankCode),
                              height: 18.gw,
                              width: 18.gw,
                              radius: 12.gw,
                              placeholder: Image.asset("assets/images/transact/icon_card.png"),
                              errorWidget: Image.asset("assets/images/transact/icon_card.png"),
                            ),
                            SizedBox(width: 10.gw),
                            Text(model.bankName,
                                overflow: TextOverflow.ellipsis, // 添加文本溢出处理
                                maxLines: 1,
                                style: Theme.of(context).textTheme.displayLarge?.copyWith(
                                      fontSize: 17.fs,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.white,
                                    )),
                          ],
                        ),
                        SizedBox(height: 8.gw),
                        AutoSizeText(
                          cardNum,
                          minFontSize: 8.fs,
                          style: Theme.of(context).textTheme.displayLarge?.copyWith(
                                fontFamily: type == WithdrawType.bankCard ? 'DINCond-Bold' : null,
                                fontSize: type == WithdrawType.bankCard ? 24.fs : 16.fs,
                                color: Colors.white,
                              ),
                        ),
                      ],
                    ),
                    if (model.isBan)
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 8.gw),
                        child: const BankBanWidget(),
                      ),
                    if (isSel)
                      SvgPicture.asset(
                        "assets/images/common/icon_check_white.svg",
                        width: 24.0,
                        height: 24.0,
                      ),
                  ],
                ),
              ),
              if (showNext)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    height: 22.gw,
                    width: 51.gw,
                    decoration: BoxDecoration(
                      color: bgColor,
                      borderRadius:
                          BorderRadius.only(topRight: Radius.circular(4.gw), bottomLeft: Radius.circular(4.gw)),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 2,
                          offset: const Offset(0, 2),
                        ),

                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          "更换",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12.fs,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        SizedBox(width: 1.gw),
                        Icon(
                          Icons.chevron_right,
                          color: Colors.white,
                          size: 17.gw,
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Container _manualChannel(Color bgColor, BuildContext context, String cardNum) {
    return Container(
      height: 78.gw,
      decoration: BoxDecoration(
        color: Colors.white, // 背景白色
        border: Border.all(
          color: const Color(0xffF5F3F1),
          width: 1,
        ),
        boxShadow: const [
          BoxShadow(
            color: Color(0x1A000000), // 0x1A = 10% 透明度的黑色 (#0000001A)
            offset: Offset(0, 2), // x: 0, y: 2
            blurRadius: 2,
          ),
        ],
        borderRadius: BorderRadius.circular(4.gw),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              SizedBox(width: 10.gw),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 14.gw),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              height: 20.gw,
                              width: 20.gw,
                              clipBehavior: Clip.hardEdge,
                              decoration:
                                  BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(15.gw)),
                              alignment: Alignment.center,
                              child: AppImage(
                                imageUrl: getBankImageStr(model.bankCode),
                                height: 24.gw,
                                width: 24.gw,
                                radius: 12.gw,
                                placeholder: Image.asset("assets/images/transact/icon_payment_1.png"),
                                errorWidget: Image.asset("assets/images/transact/icon_payment_1.png"),
                              ),
                            ),
                            SizedBox(width: 10.gw),
                            Text(model.bankName,
                                overflow: TextOverflow.ellipsis, // 添加文本溢出处理
                                maxLines: 1,
                                style: Theme.of(context).textTheme.displayLarge?.copyWith(
                                      fontSize: 14.fs,
                                      fontWeight: FontWeight.w500,
                                      color: const Color(0xffB39572),
                                    )),
                          ],
                        ),
                        SizedBox(height: 7.gw),
                        Row(
                          children: [
                            Container(
                              height: 20.gw,
                              width: 20.gw,
                              clipBehavior: Clip.hardEdge,
                              decoration:
                                  BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(15.gw)),
                              alignment: Alignment.center,
                              child: AppImage(
                                imageUrl: getBankImageStr(model.bankCode),
                                height: 24.gw,
                                width: 24.gw,
                                radius: 12.gw,
                                placeholder: Image.asset("assets/images/transact/icon_payment_2.png"),
                                errorWidget: Image.asset("assets/images/transact/icon_payment_2.png"),
                              ),
                            ),
                            SizedBox(width: 10.gw),
                            AutoSizeText(
                              cardNum,
                              minFontSize: 8.fs,
                              style: Theme.of(context).textTheme.displayLarge?.copyWith(
                                    fontSize: 14.fs,
                                    color: const Color(0xff3B416B),
                                  ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    if (model.isBan)
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 8.gw),
                        child: const BankBanWidget(),
                      ),
                    if (isSel)
                      SvgPicture.asset(
                        "assets/images/common/icon_check_white.svg",
                        width: 24.0,
                        height: 24.0,
                      ),
                  ],
                ),
              ),
              if (showNext)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    height: 22.gw,
                    width: 51.gw,
                    decoration: BoxDecoration(
                      color: const Color(0xffF5F3F1),
                      borderRadius:
                          BorderRadius.only(topRight: Radius.circular(4.gw), bottomLeft: Radius.circular(4.gw)),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          "更换",
                          style: TextStyle(
                            color: const Color(0xffB39572),
                            fontSize: 12.fs,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        SizedBox(width: 1.gw),
                        Icon(
                          Icons.chevron_right,
                          color: const Color(0xffB39572),
                          size: 17.gw,
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
