import 'package:flutter/material.dart';
import 'package:game_store/core/utils/font_size.dart';
import 'package:game_store/core/utils/screenUtil.dart';

class TransactTitleView extends StatelessWidget {
  final String title;
  final Widget? child;
  final bool showLine;

  /// 是否显示文字前垂直线

  const TransactTitleView({
    super.key,
    required this.title,
    this.showLine = false,
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40.gw,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (showLine) ...[
            Image.asset("assets/images/transact/icon_section_block.png", width: 4.39.gw, height: 15.gw),
            SizedBox(width: 5.5.gw),
          ],
          Text(
            title,
            style: TextStyle(
              color: const Color(0xff393C47),
                  fontSize: 14.fs,
                  fontWeight: FontWeight.w600,
                ),
            strutStyle: StrutStyle(
              fontSize: 14.fs,
              height: 1.0,
              leading: 0,
              forceStrutHeight: true,
            ),
          ),
          if (child != null) Expanded(child: child!)
        ],
      ),
    );
  }
}
