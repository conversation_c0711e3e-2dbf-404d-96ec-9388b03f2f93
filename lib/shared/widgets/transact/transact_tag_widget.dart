import 'package:flutter/material.dart';
import 'package:game_store/core/utils/font_size.dart';
import 'package:game_store/core/utils/screenUtil.dart';
import 'package:game_store/core/utils/string_util.dart';

class TransactTagWidget extends StatelessWidget {
  final String title;

  const TransactTagWidget({
    super.key,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    if (StringUtil.isEmpty(title.trim())) {
      return const SizedBox.shrink();
    }
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Image.asset(
          "assets/images/transact/icon_tag_roll.png",
          width: 3,
          height: 19,
        ),
        Container(
          height: 16,
          padding: EdgeInsets.only(right: 4.gw),
          decoration: BoxDecoration(
              color: const Color(0xffEA4445),
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(7.gw),
                bottomRight: Radius.circular(7.gw),
              )),
          child: Center(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 9.fs,
                color: Colors.white,
                height: 1,
              ),
            ),
          ),
        )
      ],
    );
  }
}
