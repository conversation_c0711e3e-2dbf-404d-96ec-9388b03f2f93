import 'package:flutter/material.dart';
import 'package:game_store/core/models/entities/top_up_record_entity.dart';
import 'package:game_store/core/utils/clipboardTool.dart';
import 'package:game_store/core/utils/screenUtil.dart';
import 'package:game_store/shared/widgets/gstext_image_button.dart';
import 'package:game_store/shared/widgets/row/text_row.dart';

class TopUpHistoryCell extends StatelessWidget {
  final TopUpRecord model;

  const TopUpHistoryCell({super.key, required this.model});


  static Widget buildOrderStatusWidget(context, {required int orderStatus}) {
    // orderStatus 订单状态（0：待审核，1：已通过，2：已取消, 3: 已拒绝）
    var title = "";
    var titleColor = Colors.white;
    var fontWeight = FontWeight.w500;
    switch (orderStatus) {
      case 0:
        title = "待审核";
        titleColor = const Color(0xff838383);
        break;
      case 1:
        title = "已通过";
        titleColor = const Color(0xff61bc8d);
        fontWeight = FontWeight.w700;
        break;
      case 2:
        title = "已取消";
        titleColor = const Color(0xffec8c89);
        break;
      case 3:
        title = "已拒绝";
        titleColor = const Color(0xffdc4a3a);
        break;
    }
    return Text(title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: titleColor,
              fontWeight: fontWeight,
            ));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15.gw, vertical: 10.gw),
      margin: EdgeInsets.symmetric(horizontal: 15.gw),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4.gw),
          boxShadow: const [
            BoxShadow(
              color: Color(0xfff4f4fc),
              blurRadius: 20,
              offset: Offset(0, 0),
            ),
          ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextRow(
            leftWidget: Container(
                decoration: BoxDecoration(
                  color: const Color(0xff61bc8d),
                  borderRadius: BorderRadius.circular(3.gw),
                ),
                padding: EdgeInsets.symmetric(horizontal: 18.gw, vertical: 1.gw),
                child: Text(
                  "充值",
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(color: Colors.white),
                )),
            rightWidget: TopUpHistoryCell.buildOrderStatusWidget(context, orderStatus: model.orderStatus),
          ),
          TextRow(
            title: "申请金额",
            content: "${model.orderAmount}",
          ),
          if (model.orderInitialAmount != model.finalAmount && model.orderStatus == 1)
            TextRow(
              title: "入账金额",
              content: "${model.finalAmount}",
            ),
          TextRow(
            title: "类型",
            content: "${model.cashinWayName}-${model.cashinTypeName}",
          ),
          TextRow(
            title: "时间",
            content: model.requestTime,
          ),
          TextRow(
            title: "订单号",
            rightWidget: GSTextImageButton(
              text: model.transactionNo,
              textStyle: Theme.of(context).textTheme.titleMedium,
              imageAssets: "assets/images/common/icon_copy_black.png",
              position: GSTextImageButtonPosition.right,
              onPressed: () {
                ClipboardTool.setData(model.transactionNo);
              },
            ),
          ),
        ],
      ),
    );
  }
}
