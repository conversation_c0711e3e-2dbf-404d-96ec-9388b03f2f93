import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:game_store/core/constants/constants.dart';
import 'package:game_store/core/utils/font_size.dart';
import 'package:game_store/core/utils/global_config.dart';
import 'package:game_store/core/utils/screenUtil.dart';
import 'package:game_store/core/utils/string_util.dart';

class TopUpTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final double maxLimit;
  final double? rates;
  final Function(String) onChanged;
  final bool enabled;
  final TextStyle? style;

  const TopUpTextField({
    super.key,
    required this.controller,
    required this.hintText,
    required this.maxLimit,
    this.rates,
    required this.onChanged,
    this.enabled = true,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    final border = OutlineInputBorder(
      borderSide: const BorderSide(color: Colors.transparent),
      borderRadius: BorderRadius.circular(6.gw),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 40.gw,
          child: TextField(
            controller: controller,
            style: style ?? TextStyle(color: const Color(0xffCDB296), fontSize: 20.fs, fontWeight: FontWeight.w600),
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            enabled: enabled,
            minLines: null,
            maxLines: null,
            // expands: true,
            textAlignVertical: TextAlignVertical.center,
            // 使内容垂直居中
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*$')), // 只允许一个小数点
            ],
            decoration: InputDecoration(
              filled: true,
              fillColor: const Color(0xffF5F3F1),

              contentPadding: const EdgeInsets.fromLTRB(6.5, 0, 6.5, 0),
              prefixIcon: Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.gw),
                child: Container(
                  width: 24.gw,
                  height: 24.gw,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: const Color(0xffB39572), width: 1.gw),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    '¥',
                    style: TextStyle(
                      fontSize: 16.fs,
                      color: const Color(0xffB39572),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              // 允许自定义大小的图标
              prefixIconConstraints: const BoxConstraints(minWidth: 0, minHeight: 0),
              suffixIconConstraints: const BoxConstraints(minWidth: 0, minHeight: 0),
              counterText: '',
              hintText: hintText,
              hintStyle: TextStyle(
                fontSize: 14.fs,
                color: const Color(0xFF999999),
                height: 2.1.gw,
                fontWeight: FontWeight.w600,
              ),
              border: border,
              enabledBorder: border,
              focusedBorder: border,
              disabledBorder: border,
            ),
            onChanged: (value) {
              if (value.isEmpty) {
                onChanged(value);
                return;
              }

              double inputAmount = double.tryParse(value) ?? 0;
              if (inputAmount > maxLimit) {
                inputAmount = maxLimit;
                controller.text = inputAmount.removeZeros;
              }
              onChanged(controller.text);
            },
          ),
        ),
        if (rates != null && controller.text.isNotEmpty && kChannel != "JS") ...[
          SizedBox(height: 8.gw),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              rates == null
                  ? Container()
                  : SizedBox(
                      height: 20.gw,
                      child: Row(
                        children: [
                          Text(
                            "当前汇率换算 ",
                            style: TextStyle(
                              color: const Color(0xffC2AA8E),
                              fontSize: 14.fs,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                          Text(
                            "≈ ${(double.parse(controller.text) / rates!).toStringAsFixed(2)} USDT",
                            style: TextStyle(
                              color: const Color(0xffC2AA8E),
                              fontSize: 16.fs,
                              fontFamily: 'DINCond-Bold',
                              height: 1.5.gw,
                              fontWeight: FontWeight.w500,
                            ),
                          )
                        ],
                      ),
                  ),
            ],
          ),
        ],
      ],
    );
  }
}
