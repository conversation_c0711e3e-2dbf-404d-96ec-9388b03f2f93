import 'package:flutter/material.dart';
import 'package:game_store/core/utils/font_size.dart';
import 'package:game_store/core/utils/screenUtil.dart';
import 'package:text_scroll/text_scroll.dart';

import '../transact_tag_widget.dart';

class TopUpWayCell extends StatelessWidget {
  final String title;
  final bool isSelected;
  final bool isRecommended;
  final Widget? suffix;
  final double fontSize;
  final Color selectedBorderColor;
  final String? payWayTag;

  TopUpWayCell({
    super.key,
    required this.title,
    required this.isSelected,
    this.isRecommended = false,
    this.suffix,
    double? fontSize,
    Color? selectedBorderColor,
    this.payWayTag,
  })  : fontSize = fontSize ?? 16.fs,
        selectedBorderColor = selectedBorderColor ?? const Color(0xffCDB296);

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Positioned.fill(
          child: Container(
            margin: EdgeInsets.only(bottom: 2.gw),
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(
              color: Colors.white, // 背景白色
              border: Border.all(
                color: isSelected ? const Color(0xffB39572) : const Color(0xffF5F3F1),
                width: 1,
              ),
              boxShadow: const [
                BoxShadow(
                  color: Color(0x1A000000), // 0x1A = 10% 透明度的黑色 (#0000001A)
                  offset: Offset(0, 2), // x: 0, y: 2
                  blurRadius: 2,
                ),
              ],
              borderRadius: BorderRadius.circular(4.gw),
            ),
            // decoration: BoxDecoration(
            //   color: isSelected ? const Color(0xffB39572) : const Color(0xffF5F3F1),
            //   border: Border.all(color: isSelected ? selectedBorderColor : Colors.transparent, width: 1.5.gw),
            //   borderRadius: BorderRadius.circular(6.gw),
            // ),
            child: Container(
              // padding: const EdgeInsets.symmetric(horizontal: 20),
              alignment: Alignment.center,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (suffix != null) Padding(padding: EdgeInsets.only(right: 4.gw), child: suffix),
                  Flexible(
                    child: _buildTitleWidget(),
                  ),
                ],
              ),
            ),
          ),
        ),
        if (isRecommended)
          Positioned(
              top: 0,
              right: 0,
              child: Image.asset(
                "assets/images/transact/icon_transact_recommended.png",
                width: 25.gw,
                height: 25.gw,
                fit: BoxFit.fill,
              )),
        if (isSelected)
          Positioned(
              bottom: 2.gw,
              right: 1.gw,
              child: Image.asset(
                "assets/images/transact/icon_transact_item_selected.png",
                width: 20.gw,
                height: 13.gw,
                fit: BoxFit.fill,
              )),
        if (payWayTag != null && payWayTag!.isNotEmpty)
          Positioned(top: -8.gw, left: -3, child: TransactTagWidget(title: payWayTag!))
      ],
    );
  }

 
  _buildTitleWidget() {
    return TextScroll(
      title,
      mode: TextScrollMode.endless,
      velocity: const Velocity(pixelsPerSecond: Offset(20, 0)),
      delayBefore: const Duration(milliseconds: 500),
      style: TextStyle(
        fontSize: fontSize.toDouble(),
        color: isSelected ? const Color(0xffBDA283) : const Color(0xff3B416B),
      ),
    );
  }
}
