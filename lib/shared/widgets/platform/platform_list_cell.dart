import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:game_store/core/constants/constants.dart';
import 'package:game_store/core/models/entities/game_entity.dart';
import 'package:game_store/core/utils/font_size.dart';
import 'package:game_store/core/utils/image_cache_manager.dart';
import 'package:game_store/core/utils/screenUtil.dart';
import 'package:game_store/shared/widgets/app_image.dart';

import '../../../core/utils/game_util.dart';

class PlatformListCellViewModel {
  final String title;
  final String category;
  final BoxFit imageFit;
  final double width;
  final double height;
  final bool isClosed;

  /// 场馆logo
  final String logoUrl;

  /// 封面大图
  final String cgUrl;

  const PlatformListCellViewModel({
    required this.title,
    required this.logoUrl,
    required this.cgUrl,
    required this.category,
    required this.width,
    required this.height,
    this.imageFit = BoxFit.contain,
    required this.isClosed,
  });

  factory PlatformListCellViewModel.fromAny({
    dynamic gameType,
    required dynamic platform,
    double? width,
    double? height,
    BoxFit? imageFit,
  }) {
    final fit = imageFit ?? BoxFit.contain;

    // GameType + GamePlatform
    if (gameType is GameType && platform is GamePlatform) {
      return PlatformListCellViewModel(
        title: platform.name,
        logoUrl: getPlatformLogoUrl(
          platform.platformCode,
          platformName: (gameType.code == 'YLCP' || gameType.code == 'CP') ? platform.name : null,
        ),
        cgUrl: platform.iconUrl,
        imageFit: fit,
        category: gameType.code,
        width: width ?? double.infinity,
        height: height ?? double.infinity,
        isClosed: !platform.isLogin,
      );
    }


    // 单独 GamePlatform（无 gameType）
    if (gameType == null && platform is GamePlatform) {
      return PlatformListCellViewModel(
        title: platform.name,
        logoUrl: getPlatformLogoUrl(
          platform.platformCode,
          platformName: platform.gameType == 1 ? platform.iconUrl : null,
        ),
        cgUrl: platform.iconUrl,
        imageFit: fit,
        category: platform.platformCode,
        width: width ?? double.infinity,
        height: height ?? double.infinity,
        isClosed: false,
      );
    }

    throw ArgumentError("Invalid combination of gameType and platform");
  }

  // factory PlatformListCellViewModel.fromGameType(GameType gameType, GamePlatform item, {BoxFit? imageFit}) {
  //   return PlatformListCellViewModel(
  //     title: item.name,
  //     logoUrl: getPlatformLogoUrl(
  //       item.platformCode,
  //       platformName: (gameType.code == 'YLCP' || gameType.code == 'CP') ? item.name : null,
  //     ),
  //     cgUrl: item.iconUrl,
  //     imageFit: imageFit ?? BoxFit.contain,
  //     category: gameType.code,
  //     width: double.infinity,
  //     height: double.infinity,
  //     isClosed: !item.isLogin,
  //   );
  // }

  // factory PlatformListCellViewModel.fromGamePlatform(GamePlatform item, {BoxFit? imageFit}) {
  //   return PlatformListCellViewModel(
  //     title: item.name,
  //     cgUrl: item.iconUrl,
  //     category: item.platformCode,
  //     width: double.infinity,
  //     height: double.infinity,
  //     isClosed: false,
  //     logoUrl: getPlatformLogoUrl(
  //       item.platformCode,
  //       platformName: item.gameType == 1 ? item.iconUrl : null,
  //     ),
  //   );
  // }
  //
  // factory PlatformListCellViewModel.fromGameTypeV2(GameTypeV2 gameType, GamePlatformV2 item, {BoxFit? imageFit}) {
  //   return PlatformListCellViewModel(
  //     title: item.name,
  //     logoUrl: item.logoUrl,
  //     cgUrl: GlobalConfig().systemConfig.gamePicBaseUrl + item.mainImgUrl,
  //     imageFit: imageFit ?? BoxFit.contain,
  //     category: gameType.code,
  //     width: double.infinity,
  //     height: double.infinity,
  //     isClosed: item.isAegis == 0,
  //   );
  // }
}

class PlatformListCell extends StatelessWidget {
  final PlatformListCellViewModel model;
  final VoidCallback onTap;

  const PlatformListCell({
    super.key,
    required this.model,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final platformCell = GameUtil().getPlatformCellData(model.category);
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        width: model.width,
        height: model.height,
        child: Stack(
          children: [
            Positioned.fill(
              child: Container(
                decoration:   BoxDecoration(
                    image: DecorationImage(
                  image: AssetImage(platformCell.bgImage),
                  fit: BoxFit.fill,
                )),
                child: Column(
                  children: [
                    Expanded(
                        child: Stack(
                      fit: StackFit.expand,
                      children: [
                        /// 场馆大图
                        AppImage(
                          imageUrl: model.cgUrl,
                          fit: model.imageFit,
                          cacheManager: !kIsWeb ? ImageCacheManager() : null,
                        ),

                        /// 场馆logo
                        Positioned(
                            right: 2.gw,
                            top: 2.gw,
                            child: Container(
                              width: 23.gw,
                              height: 23.3.gw,
                              decoration:  BoxDecoration(
                                  image: DecorationImage(
                                image: AssetImage(platformCell.logoImage),
                                fit: BoxFit.fill,
                              )),
                              alignment: Alignment.center,
                              // padding: EdgeInsets.only(bottom: 8.gw),
                              child: AppImage(
                                imageUrl: model.logoUrl,
                                placeholder: const SizedBox.shrink(),
                                fit: model.imageFit,
                                width: 16.gw,
                                height: 16.gw,
                                cacheManager: ImageCacheManager(),
                              ),
                            ))
                      ],
                    )),
                    Container(
                        width: double.infinity,
                        // height: 30.gw,
                        padding: EdgeInsets.fromLTRB(10.gw, 7.gw, 10.gw, 0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              model.title,
                              maxLines: 1,
                              style: TextStyle(
                                fontSize: 14.fs,
                                fontWeight: FontWeight.w500,
                                color: platformCell.textColor,
                              ),
                            ),
                            // Text(
                            //   "${model.category} CASINO",
                            //   maxLines: 1,
                            //   style: TextStyle(
                            //     fontSize: 10.fs,
                            //     fontWeight: FontWeight.w600,
                            //     color: platformCell.textColor,
                            //   ),
                            // ),
                          ],
                        )),
                    SizedBox(height: 10.gw),
                  ],
                ),
              ),
            ),
            if (model.isClosed)
              Container(
                width: model.width,
                height: model.height - 5.gw,
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.4),
                  borderRadius: BorderRadius.circular(10.gw),
                ),
                child: Center(
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 6.gw, vertical: 2.gw),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(3.gw),
                    ),
                    child: Text(
                      "暂未开放",
                      style: TextStyle(color: Colors.white.withOpacity(0.5), fontSize: 14.fs),
                    ),
                  ),
                ),
              )
          ],
        ),
      ),
    );
  }
}
