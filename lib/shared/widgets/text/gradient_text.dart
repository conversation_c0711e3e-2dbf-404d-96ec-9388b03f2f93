import 'package:flutter/material.dart';
import 'package:game_store/core/utils/font_size.dart';
import 'package:game_store/core/utils/system_util.dart'; // for kIsWeb

class GradientText extends StatelessWidget {
  final String text;
  final List<Color> colors;
  final double fontSize;
  final String? fontFamily;

  const GradientText({
    super.key,
    required this.text,
    required this.colors,
    this.fontSize = 17.0,
    this.fontFamily,
  });

  @override
  Widget build(BuildContext context) {
    bool isGradientSupported = SystemUtil.getPlatform() == 'Chrome on iOS';

    return isGradientSupported
        ? ShaderMask(
            shaderCallback: (Rect bounds) {
              return LinearGradient(
                colors: colors,
              ).createShader(bounds);
            },
            child: Text(
              text,
              style: TextStyle(
                fontSize: fontSize.fs,
                fontWeight: SystemUtil.isWeb() ? FontWeight.w500 : FontWeight.w600,
                color: Colors.white, // 文字颜色设置为白色，这样才会被 ShaderMask 覆盖
                fontFamily: fontFamily,
              ),
            ),
          )
        : Text(
            text,
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: SystemUtil.isWeb() ? FontWeight.w500 : FontWeight.w600,
              color: colors.first, // 使用颜色数组中的第一个颜色作为纯色
              fontFamily: fontFamily,
            ),
          );
  }
}
