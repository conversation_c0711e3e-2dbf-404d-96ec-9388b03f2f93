import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

import 'linked_scroll_controller.dart';
import 'nested_scroll_position.dart';

enum PageExpandState { NotExpand, Expanding, Expanded }

/// 协调器
/// 页面 Primary [CustomScrollView] 控制
class NestedScrollCoordinator {
  final String pageLabel = 'page';

  late final LinkedScrollController _pageScrollController;
  late double Function() pinnedHeaderSliverHeightBuilder;

  NestedScrollPosition get _pageScrollPosition => _pageScrollController.position;

  ScrollDragController? scrollDragController;

  /// 主页面滑动部件默认位置
  double _pageInitialOffset = 0.0;

  /// 获取主页面滑动控制器
  LinkedScrollController pageScrollController([double initialOffset = 0.0]) {
    assert(initialOffset >= 0.0);
    _pageInitialOffset = initialOffset;
    _pageScrollController = LinkedScrollController(
      this,
      debugLabel: pageLabel,
      initialScrollOffset: initialOffset,
    );
    return _pageScrollController;
  }

  /// 创建并获取一个子滑动控制器
  LinkedScrollController newChildScrollController([String? debugLabel]) =>
      LinkedScrollController(this, debugLabel: debugLabel);

  /// 子部件滑动数据协调
  void applyUserOffset(
      double delta,
      ScrollDirection userScrollDirection,
      NestedScrollPosition position,
      ) {
    if (userScrollDirection == ScrollDirection.reverse) {
      updateUserScrollDirection(_pageScrollPosition, userScrollDirection);
      final double innerDelta = _pageScrollPosition.applyClampedDragUpdate(delta);
      if (innerDelta != 0.0) {
        updateUserScrollDirection(position, userScrollDirection);
        position.applyFullDragUpdate(innerDelta);
      }
    } else {
      updateUserScrollDirection(position, userScrollDirection);
      final double outerDelta = position.applyClampedDragUpdate(delta);
      if (outerDelta != 0.0) {
        updateUserScrollDirection(_pageScrollPosition, userScrollDirection);
        _pageScrollPosition.applyFullDragUpdate(outerDelta);
      }
    }
  }

  bool applyContentDimensions(double minScrollExtent, double maxScrollExtent, NestedScrollPosition position) {
    final headerHeight = pinnedHeaderSliverHeightBuilder();
    maxScrollExtent = math.max(0.0, maxScrollExtent - headerHeight);
    return position.applyContentDimensions(minScrollExtent, maxScrollExtent, true);
  }

  /// 当默认位置不为0时，主部件已下拉距离超过默认位置，但超过的距离不大于该值时，
  /// 若手指离开屏幕，主部件头部会回弹至默认位置
  static const double _scrollRedundancy = 80;

  /// 当前页面Header最大程度展开状态
  PageExpandState pageExpand = PageExpandState.NotExpand;

  /// 当手指离开屏幕
  void onPointerUp(PointerUpEvent event) {
    final double pagePixels = _pageScrollPosition.pixels;
    if (pagePixels > 0.0 && pagePixels < _pageInitialOffset) {
      if (pageExpand == PageExpandState.NotExpand &&
          _pageInitialOffset - pagePixels > _scrollRedundancy) {
        _pageScrollPosition
            .animateTo(
          0.0,
          duration: const Duration(milliseconds: 400),
          curve: Curves.ease,
        )
            .then((_) => pageExpand = PageExpandState.Expanded);
      } else {
        pageExpand = PageExpandState.Expanding;
        _pageScrollPosition
            .animateTo(
          _pageInitialOffset,
          duration: const Duration(milliseconds: 400),
          curve: Curves.ease,
        )
            .then((_) => pageExpand = PageExpandState.NotExpand);
      }
    }
  }

  /// 更新用户滑动方向
  void updateUserScrollDirection(NestedScrollPosition position, ScrollDirection value) {
    position.didUpdateScrollDirection(value);
  }

  /// 启动物理滚动模拟
  void goBallistic(double velocity) => _pageScrollPosition.goBallistic(velocity);
}
