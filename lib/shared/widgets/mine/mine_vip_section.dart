import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inner_shadow/flutter_inner_shadow.dart';
import 'package:game_store/core/constants/assets.dart';
import 'package:game_store/core/models/apis/transact.dart';
import 'package:game_store/core/models/entities/user_vip_entity.dart';
import 'package:game_store/core/singletons/user_cubit.dart';
import 'package:game_store/core/singletons/user_state.dart';
import 'package:game_store/core/utils/auth_util.dart';
import 'package:game_store/core/utils/font_size.dart';
import 'package:game_store/core/utils/screenUtil.dart';
import 'package:game_store/features/page/3_transact/withdraw/withdraw_cubit.dart';
import 'package:game_store/features/page/main/screens/main_screen_cubit.dart';
import 'package:game_store/features/routers/app_router.dart';
import 'package:game_store/features/routers/navigator_utils.dart';
import 'package:game_store/injection_container.dart';
import 'package:game_store/shared/widgets/vip/vip_level_tag.dart';
import 'package:path/path.dart';

class MineVipSection extends StatelessWidget {
  const MineVipSection({super.key});

  /// vip 进度条
  Widget _buildVIPProgressBar(double progressFactor) {
    return InnerShadow(
      shadows: const [
        // 模拟底部深色内阴影
        Shadow(
          offset: Offset(0, 1),
          blurRadius: 1,
          color: Color(0x33000000), // #00000033
        ),
        // 模拟顶部浅色内阴影
        Shadow(
          offset: Offset(0, -1),
          blurRadius: 1,
          color: Color(0x66FFFFFF), // #FFFFFF66
        ),
      ],
      child: Container(
        // width: GSScreenUtil().screenWidth - 175.gw,
        height: 8.gw,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFEFEFEF), // #EFEFEF
              Color(0xFFD9D9D9), // #D9D9D9
            ],
          ),
          borderRadius: BorderRadius.circular(4.gw),
        ),
        child: FractionallySizedBox(
          alignment: Alignment.centerLeft,
          widthFactor: progressFactor,
          heightFactor: 1,
          child: Container(
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFFC4AF99), // 起点颜色
                  Color(0xFF9F7B57), // 终点颜色
                ],
              ),
              borderRadius: BorderRadius.circular(4.gw),
            ),
          ),
        ),
      ),
    );
  }

  /// vip权益 ✅xxx
  Widget _buildVipCheck(context, String title) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Image.asset(Assets.iconCorrectB, width: 14.gw, height: 14.gw),
        const SizedBox(width: 2),
        Text(
          title,
          style: Theme
              .of(context)
              .textTheme
              .titleSmall
              ?.copyWith(
            color: const Color(0xff3C4C65),
            fontSize: 12.fs,
            fontWeight: FontWeight.w500,
          ),
          maxLines: 2,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {


    return BlocSelector<UserCubit, UserState, ({bool isLogin, UserVipEntity? vipInfo})>(
      selector: (state) => (isLogin: state.isLogin, vipInfo: state.vipInfo),
      builder: (context, userState) {

        final vipInfo = userState.vipInfo;
        final currentLevel = vipInfo?.vipLevel ?? 1;
        final currentIntegral = vipInfo?.integral ?? 0.0;
        final nextLevelIntegral = vipInfo?.nextLevelIntegral;

        final hasNextLevel = nextLevelIntegral != null;
        final nextLevel = hasNextLevel ? currentLevel + 1 : kMaxVipLevel;
        var progressFactor = hasNextLevel ? (currentIntegral / nextLevelIntegral).clamp(0.0, 1.0) : 1.0;
        if (vipInfo == null) progressFactor = 0;

        final slogan = userState.isLogin ? "您是VIP$currentLevel用户 尊享奢华服务" : 'login_to_see_vip'.tr();

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            GestureDetector(
              onTap: () => AuthUtil.checkIfLogin(() => sl<NavigatorService>().push(AppRouter.vipCenter)),
              child: Container(
                color: Colors.transparent,
                child: Column(
                  children: [
                    Text(
                      slogan,
                      style: TextStyle(
                        fontSize: 12.fs,
                        color: const Color(0xffA48669),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Row(
                      children: [
                        VipLevelTag(level: currentLevel),
                        SizedBox(width: 4.gw),
                        // Expanded(child: _buildVIPProgressBar(progressFactor)),
                        Expanded(child: _buildVIPProgressBar(progressFactor)),
                        SizedBox(width: 4.gw),
                        VipLevelTag(level: nextLevel),
                      ],
                    ),
                    SizedBox(height: 12.gw),
                    SizedBox(
                      width: double.infinity,
                      child: Wrap(
                        direction: Axis.horizontal,
                        alignment: WrapAlignment.spaceBetween,
                        // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _buildVipCheck(context, 'level_up_reward'.tr()),
                          _buildVipCheck(context, 'weekly_reward'.tr()),
                          _buildVipCheck(context, 'monthly_reward'.tr()),
                          _buildVipCheck(context, 'yearly_reward'.tr()),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 13.gw),
            GestureDetector(
              onTap: () =>
                  AuthUtil.checkIfLogin(() {
                    sl<WithdrawCubit>().onChangeWithdrawType(WithdrawType.manualChannel);
                    final mainScreenCubit = sl<MainScreenCubit>();
                    mainScreenCubit.goToTransactPage(isDeposit: false);
                  }),
              child: Image.asset(
                "assets/images/mine/v2/banner_mine_manual_withdraw.png",
                height: 32.gw,
                width: double.infinity,
                fit: BoxFit.fill,
              ),
            ),
          ],
        );
      },
    );
  }
}