import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:game_store/core/utils/font_size.dart';
import 'package:game_store/core/utils/screenUtil.dart';

class MineVideoVipRemainWidget extends StatelessWidget {
  final int days;
  final GestureTapCallback onTap;

  const MineVideoVipRemainWidget({
    super.key,
    required this.days,
    required this.onTap,
  });

  //
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: onTap,
        child: Stack(
          alignment: Alignment.center,
          children: [
            SvgPicture.asset(
              "assets/images/mine/v2/bg_mine_video_vip.svg",
              width: 120.gw,
              height: 40.gw,
              fit: BoxFit.cover,
            ),
            SizedBox(
              width: 120.gw,
              child: Row(
                children: [
                  SizedBox(width: 20.gw), // 辅助
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.baseline,
                          // 关键配置
                          textBaseline: TextBaseline.ideographic,
                          children: [
                            Flexible(
                              child: AutoSizeText(
                                days.toString(),
                                style: TextStyle(
                                  fontSize: 20.fs,
                                  height: 1,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                  shadows: [
                                    Shadow(
                                      offset: const Offset(0, 2),
                                      blurRadius: 4,
                                      color: Colors.black.withOpacity(0.25),
                                    ),
                                  ],
                                ),
                                minFontSize: 10.fs,
                                maxFontSize: 20.fs,
                                maxLines: 1,
                                textAlign: TextAlign.center,
                              ),
                            ),
                            Text(
                              '天',
                              style: TextStyle(
                                fontSize: 12.fs,
                                height: 1.2,
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                                shadows: [
                                  Shadow(
                                    offset: const Offset(0, 2),
                                    blurRadius: 4,
                                    color: Colors.black.withOpacity(0.25),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 2.gw),
                        Text(
                          '观影天数',
                          style: TextStyle(
                            fontSize: 10.fs,
                            height: 1.2,
                            color: Colors.white.withOpacity(0.5),
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 40.gw), // 辅助左边视图定位用
                ],
              ),
            ),
          ],
        ));
  }
}
