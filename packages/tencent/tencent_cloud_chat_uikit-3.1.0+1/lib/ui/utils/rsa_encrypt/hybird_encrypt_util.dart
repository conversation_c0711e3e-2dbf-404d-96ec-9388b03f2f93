// import 'dart:convert';
// import 'dart:math';
// import 'package:basic_utils/basic_utils.dart';
// import 'package:encrypt/encrypt.dart' as encrypt;
//
// import 'aes_cipher.dart';
//
// class HybridEncryptedData {
//   String encryptedData = ''; // AES 加密后的数据（Base64）
//   String encryptedAESKey = ''; // RSA 加密后的 AES 密钥（Base64）
//
//   HybridEncryptedData();
//
//   factory HybridEncryptedData.fromJson(Map<String, dynamic> json) {
//       final entity = HybridEncryptedData();
//       entity.encryptedData = json["param"] ?? '';
//       entity.encryptedAESKey = json["e"] ?? '';
//       return entity;
//   }
//
//   Map<String, dynamic> toJson() => {
//         'param': encryptedData,
//         'e': encryptedAESKey,
//       };
// }
//
// const String apiPrivateKeyBase64 = """-----B<PERSON>IN PRIVATE KEY-----
// MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQD7lv6tftYDMH7hnz8abKnd1qSotPEHLquy6TFGNkfwrMdL81wcK8RiRXMy/uuvCzTutvEKKrjy8mtCjN3/F4FYcsWHdik1uak9xD+YJWI1kJtPImDztY8gqM/BL1O5r+bwgwYjVQUTlmLDI4YKTlDe+WapGI2vRNOo71x1/Ncv2exeZ26YL0U0eprzGqxZbCb4iS0uA/YcQZihSlkcLJkgwkP37xEfviCv3qrTJRexb81ObeQhwaSrUC5dr299pHqKejuonye8IrdNZPCdMuSWziHN6+RyRRPuo02gd1/lvfGwn8Uuermb16hXvmWoK13IRN9dBk01UZ7KxvfWi33tAgMBAAECggEAOrTSq7BPhwH/cW1j36ogfxPRa06BeaARz5creMs8wTPH83iPc0FhO18iUPF8FGXkjZlhqHIsowAfODG/sYEOHRH29rODTTaL/8412q5Ket5J507J2cRxZ/kMNXNyMX+Sv2eLfJ4fp6DtXcSByBDbPRRsOKPyl8XTL/XemOeNXOwiE1byY1koDN+/arsQEU+ToS+tKGfc8Bo7Kj+d75s20e60Ss1EaKszQUTwIxqNdnEYB/DwVLpVe6RthCNTAb61ljOzc4FiFxZGM+fDymupl4NY4pEb9tslyVRUWkuHSFPZHAW+8JrR27ml+y42spPJC66gmsQTiSWQPx2oyNFFPwKBgQD/IJV/5+V3dmPrk78vWiZFxEZ849l3CNx2kTYaqMuCUVOnFznBwQnd21vpFA3Fyl32sGak0UCygOceXcTbZERIP4j00sjbQ2cT21re6ar+LuoJjrPaNShImHa6jhTadeCUtAIedkkyYtBHCOwOpBpocSTlGjpHTDAHZa+WELb0nwKBgQD8c1AmYOFYqSzRst3KC9NiyS9SopZqN8+DQ+s6L8NQSzV9yoJ7sNaYt2VyPO6/tdPMrEqfSI7FFamRfh1/98QhTYpwWyJMOriWPidNfGXO7t5WeMnlqXHsoiGIASLSx31AD9PhwaW5CFb5+Sq/QsRep1dZduO44petw+SRkQfV8wKBgHhpFYYXmZqHWuNtiPc9UgDtgOMi2pkuqnafVGzCq8Imxhd/g7fpdtGp3TglSdbHCxAJ83foduOIqQwQ738oVH3Q1r/N2BR8PGMHzVyV5ikvVTALlODw48FXi/nCYx80MTmdt75Oz38jWe+cy2ShmQEgHWyvsz/BOo3TNUrbOq47AoGBAKLWOq/Ws+R9kzRcHKW0BRDmVokF6OHwyACSscshfelv6bwPASWJxpJRUbVjSlkMjuaph6TAp57Gv07iu2KMzczxeEfK8Q+cnO4Ef1OC1ySofW/nj8t9BcguOFTC7tRo6wiEbRZk02HL2FZ7ezBNf97SMAy8HF1og5AJGIkwtI4zAoGBANW22KM0CjbW9ongYLnkQ3iX/uHq7au3yy2k0KslONgyT3tWFX+LtdhFXSqdeb/lHPI2GYOqxK45+CCxee07nTH2Nw+JlllRkGm4Cj3MA1hWc2Pf0bgEMBJJJPxspntK0w89NkkgGWfX51ac7sPhiW+x8LP0UM8TS9b5E/Qca41l
// -----END PRIVATE KEY-----""";
// const String apiPublicKeyBase64 = """-----BEGIN PUBLIC KEY-----
// MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA+5b+rX7WAzB+4Z8/Gmyp3dakqLTxBy6rsukxRjZH8KzHS/NcHCvEYkVzMv7rrws07rbxCiq48vJrQozd/xeBWHLFh3YpNbmpPcQ/mCViNZCbTyJg87WPIKjPwS9Tua/m8IMGI1UFE5ZiwyOGCk5Q3vlmqRiNr0TTqO9cdfzXL9nsXmdumC9FNHqa8xqsWWwm+IktLgP2HEGYoUpZHCyZIMJD9+8RH74gr96q0yUXsW/NTm3kIcGkq1AuXa9vfaR6ino7qJ8nvCK3TWTwnTLkls4hzevkckUT7qNNoHdf5b3xsJ/FLnq5m9eoV75lqCtdyETfXQZNNVGeysb31ot97QIDAQAB
// -----END PUBLIC KEY-----""";
//
// class HybridCryptoUtil {
//   static HybridEncryptedData encryptWithAESAndRSA({
//     required Object data,
//   }) {
//     var plainText = jsonEncode(data);
//     if (data is String) {
//       plainText = data;
//     }
//     final rsaPublicKey = CryptoUtils.rsaPublicKeyFromPem(apiPublicKeyBase64);
//     final keyStr = _generateAESKey192();
//
//     // 2. 用 AES 加密实际数据
//     final encryptedData = AESCipher().encryptString(plainText, keyStr: keyStr);
//
//     // 3. 用 RSA 公钥加密 AES 密钥
//     final rsaEncrypter = encrypt.Encrypter(encrypt.RSA(
//       publicKey: rsaPublicKey,
//       encoding: encrypt.RSAEncoding.PKCS1,
//     ));
//     final encryptedAESKey = rsaEncrypter.encrypt(keyStr).base64;
//
//     // 4. 返回结构体
//     return HybridEncryptedData()
//       ..encryptedData = encryptedData
//       ..encryptedAESKey = encryptedAESKey;
//
//   }
//
//   static String decryptWithAESAndRSA({required HybridEncryptedData encryptedData}) {
//     // 1. 用RSA私钥解密AES密钥
//     final rsaPrivateKey = CryptoUtils.rsaPrivateKeyFromPem(apiPrivateKeyBase64);
//     final rsaDecrypter = encrypt.Encrypter(encrypt.RSA(
//       privateKey: rsaPrivateKey,
//       encoding: encrypt.RSAEncoding.PKCS1,
//     ));
//     final aesKey = rsaDecrypter.decrypt64(encryptedData.encryptedAESKey);
//     // 2. 用AES密钥解密数据
//     final plainText = AESCipher().decryptString(encryptedData.encryptedData, keyStr: aesKey);
//     return plainText;
//   }
//
//   static String _generateAESKey192() {
//     const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
//     final rand = Random.secure();
//     return List.generate(24, (_) => chars[rand.nextInt(chars.length)]).join();
//   }
// }
