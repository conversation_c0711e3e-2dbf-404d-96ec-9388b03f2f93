// import 'dart:convert';
// import 'dart:math';
// import 'dart:typed_data';
// import 'package:encrypt/encrypt.dart';
//
// const sdaflkwe = "zbsJgEB2+O62wW5/KTxK0w==";
// // const
//
// class AESCipher {
//
//   // 随机生成 IV
//   IV _generateRandomIV() {
//     final random = Random.secure();
//     final ivBytes = List<int>.generate(16, (_) => random.nextInt(256));
//     return IV(Uint8List.fromList(ivBytes));
//   }
//
//   // 加密 String -> String
//   String encryptString(String plainText, {String? keyStr}) {
//
//     final keyBytes = Key.fromUtf8(keyStr ?? sdaflkwe);
//     final iv = _generateRandomIV(); // 随机生成 IV
//
//     final encrypter = Encrypter(AES(keyBytes, mode: AESMode.cbc, padding: 'PKCS7'));
//     final encrypted = encrypter.encrypt(plainText, iv: iv);
//
//     // 将 IV 附加在密文前一起传输
//     return "${base64Encode(iv.bytes)}:${base64Encode(encrypted.bytes)}";
//   }
//
//   // 加密 Object -> String
//   String encryptObject(Object data) {
//     // 将 Object 转为 JSON 字符串
//     final plainText = jsonEncode(data);
//
//     final keyBytes = Key.fromUtf8(sdaflkwe);
//     final iv = _generateRandomIV(); // 随机生成 IV
//
//     final encrypter = Encrypter(AES(keyBytes, mode: AESMode.cbc, padding: 'PKCS7'));
//     final encrypted = encrypter.encrypt(plainText, iv: iv);
//
//     // 将 IV 附加在密文前一起传输
//     return "${base64Encode(iv.bytes)}:${base64Encode(encrypted.bytes)}";
//   }
//
//
//   // 解密 String -> String
//   // 解密方法：解密拼接的 IV 和密文
//   String decryptString(String encryptedData, {String? keyStr}) {
//     // 1. 拆分出 IV 和加密数据
//     final parts = encryptedData.split(':');
//     if (parts.length != 2) {
//       throw const FormatException('Invalid encrypted data format');
//     }
//
//     // 解码 Base64 得到 IV 和加密数据
//     final ivBytes = base64Decode(parts[0]);
//     final cipherBytes = base64Decode(parts[1]);
//
//     // 2. 使用给定的密钥
//     final keyBytes = Key.fromUtf8(keyStr ?? sdaflkwe);
//
//     // 3. 初始化解密器为 AES/CBC/PKCS7 模式
//     final iv = IV(Uint8List.fromList(ivBytes));
//     final encrypter = Encrypter(AES(keyBytes, mode: AESMode.cbc, padding: 'PKCS7'));
//
//     // 4. 执行解密
//     final decrypted = encrypter.decrypt(Encrypted(Uint8List.fromList(cipherBytes)), iv: iv);
//
//     return decrypted; // 返回解密后的字符串
//   }
//
//
// }
